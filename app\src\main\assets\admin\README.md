# 🎯 لوحة الإدارة - نظام الاشتراك المجاني

## 📋 نظرة عامة

نظام متقدم لإدارة حملات الاشتراك المجاني في تطبيق Mod Etaris، يتضمن:

- ✅ إنشاء وإدارة حملات الاشتراك المجاني
- ✅ نظام مهام تفاعلي متقدم
- ✅ إدارة البانرات مع دعم حملات الاشتراك
- ✅ واجهة إدارية جميلة ومتجاوبة
- ✅ معاينة مباشرة للبانرات والنوافذ المنبثقة

## 🚀 كيفية التشغيل

### الطريقة الأولى: استخدام الخادم المحلي (مُوصى بها)

1. **تأكد من تثبيت Python**:
   ```bash
   python --version
   ```

2. **تشغيل الخادم**:
   - على Windows: انقر نقراً مزدوجاً على `start_admin.bat`
   - على Linux/Mac: 
     ```bash
     python3 server.py
     ```

3. **فتح لوحة الإدارة**:
   - سيتم فتح المتصفح تلقائياً
   - أو افتح: `http://localhost:8000`

### الطريقة الثانية: رفع الملفات على خادم ويب

1. ارفع مجلد `admin` كاملاً على خادمك
2. افتح `http://yourserver.com/admin/`

## 📁 هيكل الملفات

```
admin/
├── index.html                 # الصفحة الرئيسية للإدارة
├── subscription_admin.html    # إدارة الاشتراك المجاني
├── subscription_admin.js      # منطق إدارة الاشتراك
├── banner_admin.html          # إدارة البانرات المحدثة
├── banner_admin.js            # منطق إدارة البانرات
├── server.py                  # خادم محلي لحل مشكلة CORS
├── start_admin.bat           # ملف تشغيل للويندوز
└── README.md                 # هذا الملف
```

## 🎯 المميزات الجديدة

### 1. نظام الاشتراك المجاني المتقدم

- **إنشاء حملات**: إنشاء حملات اشتراك مجاني مخصصة
- **أنواع المهام المدعومة**:
  - 📱 اشتراك في قناة تيليجرام
  - 🎥 اشتراك في قناة يوتيوب
  - 🐦 متابعة على تويتر
  - 📘 إعجاب صفحة فيسبوك
  - 👻 إضافة على سناب شات
  - 📲 تحميل تطبيق
  - 🎮 تحميل مود
  - ⭐ تقييم منتج
  - 🎯 تحميل عدة مودات
  - 🛠️ مهام مخصصة

### 2. إدارة البانرات المحدثة

- **نوعان من البانرات**:
  - بانر عادي (رابط خارجي)
  - بانر اشتراك مجاني (يفتح نافذة المهام)
- **التحقق من الصور**: فحص الأبعاد والحجم
- **معاينة مباشرة**: معاينة البانر والنافذة المنبثقة

### 3. واجهة المستخدم المتقدمة

- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **ألوان متطابقة**: متطابقة مع ألوان التطبيق الذهبية
- **تأثيرات بصرية**: انيميشن وتأثيرات جميلة
- **إحصائيات مباشرة**: عرض الإحصائيات في الوقت الفعلي

## 🔧 الإعداد الأولي

### 1. إعداد قاعدة البيانات

قم بتشغيل كود SQL التالي في Supabase:

```sql
-- انسخ والصق محتوى ملف database/free_subscription_system.sql
```

### 2. إعداد Supabase Storage

1. انتقل إلى Supabase Dashboard
2. اذهب إلى Storage
3. أنشئ bucket جديد باسم `banner-images`
4. اجعله عام (public)

### 3. تحديث إعدادات Supabase

تأكد من أن معلومات Supabase صحيحة في الملفات:
- `subscription_admin.js`
- `banner_admin.js`

## 📊 كيفية الاستخدام

### إنشاء حملة اشتراك مجاني جديدة

1. **افتح صفحة إدارة الاشتراك المجاني**
2. **انقر على "إنشاء حملة جديدة"**
3. **املأ البيانات المطلوبة**:
   - عنوان الحملة (عربي وإنجليزي)
   - وصف الحملة (عربي وإنجليزي)
   - مدة الاشتراك بالأيام
   - الحد الأقصى للمستخدمين (اختياري)
   - تاريخ انتهاء الحملة (اختياري)
4. **احفظ الحملة**

### إنشاء بانر للحملة

1. **افتح صفحة إدارة البانر**
2. **اختر "بانر اشتراك مجاني"**
3. **املأ بيانات البانر**
4. **ارفع صورة البانر** (800x200 بكسل كحد أقصى)
5. **ارفع صورة النافذة المنبثقة** (اختياري)
6. **املأ تفاصيل الحملة**
7. **معاينة البانر** قبل النشر
8. **احفظ البانر**

### إضافة مهام للحملة

1. **انتقل إلى تبويب "إدارة المهام"**
2. **اختر الحملة**
3. **أضف المهام المطلوبة**
4. **حدد نوع كل مهمة ورابطها**

## 🐛 حل المشاكل الشائعة

### مشكلة "Not allowed to load local resource"

**الحل**: استخدم الخادم المحلي المرفق:
```bash
python server.py
```

### مشكلة CORS

**الحل**: الخادم المحلي يحل هذه المشكلة تلقائياً

### مشكلة عدم ظهور الصور

**تأكد من**:
- إنشاء bucket `banner-images` في Supabase Storage
- جعل الـ bucket عام (public)
- صحة رابط Supabase في الكود

### مشكلة عدم حفظ البيانات

**تأكد من**:
- تشغيل كود SQL لإنشاء الجداول
- صحة مفاتيح Supabase
- تفعيل RLS policies في Supabase

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من console المتصفح** للأخطاء
2. **تأكد من إعدادات Supabase**
3. **تأكد من تشغيل كود SQL**
4. **استخدم الخادم المحلي** لتجنب مشاكل CORS

## 🔄 التحديثات المستقبلية

- [ ] إدارة المهام المتقدمة
- [ ] تحليلات مفصلة للحملات
- [ ] إشعارات تلقائية للمشرفين
- [ ] تصدير التقارير
- [ ] API للتكامل مع أنظمة خارجية

---

**تم التطوير بواسطة فريق Mod Etaris** 🚀
