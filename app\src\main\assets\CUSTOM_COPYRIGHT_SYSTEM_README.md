# نظام حقوق الطبع والنشر المخصصة - Custom Copyright System

## نظرة عامة
تم تطوير نظام حقوق الطبع والنشر المخصصة لعرض وصف خاص للمودات التي تتطلب مشاهدة إعلانات أو اختصار روابط، حيث تذهب الأرباح مباشرة إلى مالك المود. يمكن للمدير تحديد المودات التي ستظهر لها هذا الوصف المخصص.

## المميزات الرئيسية

### 🎯 **الهدف من النظام**
- **شفافية كاملة**: إعلام المستخدمين بطبيعة المودات التي تحتوي على إعلانات
- **حماية قانونية**: وضوح في حقوق الطبع والنشر للمودات الخاصة
- **مرونة في الإدارة**: تحديد المودات التي تحتاج وصف مخصص

### 📋 **الوصف المخصص**

#### **النص العربي:**
"هذا النوع من المودات يتطلب من المالك فرض اختصار روابط أو مشاهدة إعلانات، وأرباحها تذهب إلى المالك مباشرة من خلال الإعلانات التي يشاهدها المستخدم. يمكن للمالك طلب إزالة المود من التطبيق عبر التواصل معنا."

#### **النص الإنجليزي:**
"This type of mod requires the owner to impose link shortening or ad viewing, and the profits go directly to the owner through the ads viewed by the user. The owner can request removal of the mod from the app by contacting us."

### 🎨 **التأثيرات البصرية**
- **لون مميز**: حدود حمراء بدلاً من الذهبية للمودات المخصصة
- **أيقونة تحذير**: `fa-exclamation-triangle` بدلاً من `fa-shield-alt`
- **شارة "خاص"**: تظهر بجانب عنوان حقوق الطبع والنشر
- **خلفية مميزة**: لون أحمر فاتح للتمييز

## التنفيذ التقني

### 🗄️ **قاعدة البيانات**

#### **جدول `custom_copyright_mods`**
```sql
CREATE TABLE custom_copyright_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_copyright_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_copyright_mod UNIQUE (mod_id)
);
```

#### **الغرض من الجدول:**
- **mod_id**: معرف المود المرتبط بحقوق الطبع المخصصة
- **is_active**: حالة تفعيل الحقوق المخصصة
- **unique_copyright_mod**: ضمان عدم تكرار المود في الجدول

### 📁 **الملفات المضافة**

#### 1. **صفحة الإدارة**
- **`admin/custom_copyright.html`**: واجهة إدارة حقوق الطبع المخصصة
- **`admin/custom_copyright.js`**: منطق إدارة وتحديد المودات

#### 2. **التعديلات على الملفات الموجودة**
- **`supabase-manager.js`**: إضافة جدول `custom_copyright_mods`
- **`script.js`**: تعديل دالة `showModCreatorInfo()` لاستخدام الوصف المخصص
- **`translations.js`**: إضافة ترجمات للنصوص الجديدة
- **`admin/index.html`**: إضافة رابط صفحة الإدارة الجديدة

### 🔧 **الوظائف الرئيسية**

#### **في صفحة الإدارة (`custom_copyright.js`)**
```javascript
// تحميل المودات المحددة مسبقاً
async function loadSelectedMods()

// تحميل جميع المودات مع البحث والتصفية
async function loadMods()

// تبديل تحديد المود
function toggleModSelection(modId)

// حفظ المودات المحددة
async function saveCustomCopyrightMods()
```

#### **في التطبيق الرئيسي (`script.js`)**
```javascript
// التحقق من وجود حقوق طبع مخصصة
const { data: customCopyright } = await supabaseClient
    .from('custom_copyright_mods')
    .select('is_active')
    .eq('mod_id', modId)
    .eq('is_active', true)
    .single();

// استخدام الوصف المناسب
copyrightDesc: hasCustomCopyright ? t('custom_copyright_desc') : t('copyright_desc')
```

## واجهة الإدارة

### 🎛️ **المميزات**

#### 1. **عرض الوصف المخصص**
- نص عربي وإنجليزي واضح
- تصميم مميز بألوان تحذيرية
- شرح مفصل لطبيعة هذه المودات

#### 2. **البحث والتصفية**
- بحث بالاسم
- تصفية حسب الفئة
- ترقيم الصفحات

#### 3. **إدارة المودات**
- عرض جميع المودات في شبكة
- تحديد/إلغاء تحديد المودات
- عرض حالة كل مود (عادي/مخصص)
- حفظ التغييرات

#### 4. **المودات المحددة**
- قائمة منفصلة للمودات المحددة
- إمكانية إزالة مودات من القائمة
- عداد للمودات المحددة

### 🎨 **التصميم**

#### **الألوان المستخدمة:**
- **أحمر تحذيري**: `#ff6b6b` للمودات المخصصة
- **أخضر**: `#22c55e` للمودات المحفوظة
- **ذهبي**: `#ffcc00` للعناصر العامة

#### **الحالات البصرية:**
- **مود عادي**: حدود رمادية، نص "حقوق طبع عادية"
- **مود مخصص**: حدود حمراء، نص "حقوق طبع مخصصة"
- **مود محفوظ**: خلفية خضراء فاتحة

## سلوك المستخدم

### 🎯 **للمدير:**
1. الدخول إلى لوحة الإدارة
2. اختيار "إدارة حقوق الطبع المخصصة"
3. قراءة الوصف المخصص المعروض
4. البحث عن المودات المطلوبة
5. تحديد المودات التي تحتاج وصف مخصص
6. حفظ التغييرات

### 👤 **للمستخدم:**
1. فتح نافذة عرض بيانات المود
2. النقر على زر "Creator" في الشريط السفلي
3. رؤية معلومات صانع المود
4. قراءة وصف حقوق الطبع والنشر:
   - **عادي**: الوصف الافتراضي مع حدود ذهبية
   - **مخصص**: وصف خاص مع حدود حمراء وشارة "خاص"

## الأمان والشفافية

### 🔒 **الحماية**
- **تأمين البيانات**: منع SQL injection
- **التحقق من الصلاحيات**: صفحة الإدارة محمية
- **معالجة الأخطاء**: عدم عرض بيانات خاطئة

### 📜 **الشفافية**
- **وضوح الغرض**: شرح واضح لطبيعة المودات
- **حق الإزالة**: إمكانية طلب إزالة المود
- **معلومات الاتصال**: وسيلة للتواصل مع الإدارة

## التطوير المستقبلي

### 🚀 **تحسينات مقترحة**
1. **فئات مختلفة**: أنواع مختلفة من الوصف المخصص
2. **إحصائيات**: تتبع المودات التي تحتوي على إعلانات
3. **تنبيهات**: إشعارات للمستخدمين عند تحديث الوصف
4. **تقارير**: تقارير للمدير عن المودات المخصصة

### 🎨 **تحسينات التصميم**
1. **أيقونات مخصصة**: أيقونات مختلفة لأنواع المودات
2. **ألوان متدرجة**: تدرجات لونية للتمييز
3. **أنيميشن**: تأثيرات حركية للتنبيه
4. **ثيمات**: ثيمات مختلفة حسب نوع المود

## استكشاف الأخطاء

### ❌ **مشاكل محتملة**

#### 1. **الوصف لا يظهر**
- تحقق من وجود المود في جدول `custom_copyright_mods`
- تأكد من أن `is_active = true`
- فحص console للأخطاء

#### 2. **صفحة الإدارة لا تعمل**
- تحقق من اتصال قاعدة البيانات
- تأكد من وجود جدول `custom_copyright_mods`
- فحص صلاحيات المدير

#### 3. **التحديد لا يحفظ**
- تحقق من صحة معرفات المودات
- تأكد من عدم وجود قيود في قاعدة البيانات
- فحص رسائل الخطأ

### ✅ **الحلول**
```javascript
// للتحقق من وجود الجدول
console.log(await supabaseClient.from('custom_copyright_mods').select('*').limit(1));

// للتحقق من المودات المحددة
console.log(await supabaseClient.from('custom_copyright_mods').select('mod_id').eq('is_active', true));

// للتحقق من وصف مود معين
const modId = 'your-mod-id';
const result = await supabaseClient.from('custom_copyright_mods').select('*').eq('mod_id', modId);
console.log(result);
```

## الخلاصة

تم تنفيذ نظام شامل لحقوق الطبع والنشر المخصصة مع:
- ✅ وصف مخصص واضح بلغتين
- ✅ صفحة إدارة سهلة الاستخدام
- ✅ تأثيرات بصرية مميزة للتمييز
- ✅ شفافية كاملة للمستخدمين
- ✅ مرونة في الإدارة والتحكم
- ✅ حماية قانونية وأمان البيانات

النظام جاهز للاستخدام ويوفر شفافية كاملة حول طبيعة المودات التي تحتوي على إعلانات أو اختصار روابط! 🎉
