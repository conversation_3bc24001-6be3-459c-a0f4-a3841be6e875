<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المربعات المخصصة ثنائية اللغة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e1e2e, #2a2a3e);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(42, 42, 62, 0.8);
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #ffcc00;
        }

        h1 {
            text-align: center;
            color: #ffcc00;
            margin-bottom: 30px;
        }

        .test-section {
            background: rgba(30, 30, 46, 0.6);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #555;
        }

        .test-section h2 {
            color: #ffcc00;
            margin-bottom: 15px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        button {
            background: linear-gradient(45deg, #ffcc00, #ff9800);
            color: #000;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 204, 0, 0.4);
        }

        .language-selector {
            text-align: center;
            margin-bottom: 30px;
        }

        .language-selector button {
            margin: 0 10px;
        }

        .current-language {
            background: #28a745 !important;
            color: white !important;
        }

        .status {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 10px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 14px;
        }

        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }

        .test-dialog-preview {
            background: rgba(255, 204, 0, 0.1);
            border: 1px solid #ffcc00;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }

        .test-dialog-preview h3 {
            color: #ffcc00;
            margin: 0 0 10px 0;
        }

        .test-dialog-preview p {
            margin: 5px 0;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 اختبار المربعات المخصصة ثنائية اللغة</h1>

        <!-- Language Selector -->
        <div class="language-selector">
            <h2>اختيار اللغة / Language Selection</h2>
            <button id="btnArabic" onclick="setLanguage('ar')">العربية 🇸🇦</button>
            <button id="btnEnglish" onclick="setLanguage('en')">English 🇺🇸</button>
            <div class="status">
                <span class="info">اللغة الحالية / Current Language: </span>
                <span id="currentLang">غير محدد / Not Set</span>
            </div>
        </div>

        <!-- Test Custom Dialog -->
        <div class="test-section">
            <h2>اختبار المربع المخصص / Test Custom Dialog</h2>
            <div class="button-group">
                <button onclick="testCustomDialog()">عرض مربع تجريبي / Show Test Dialog</button>
                <button onclick="testModDialog()">اختبار مربع مود / Test Mod Dialog</button>
            </div>

            <div class="test-dialog-preview">
                <h3>معاينة المحتوى حسب اللغة / Content Preview by Language</h3>
                <div id="dialogPreview">
                    <p><strong>العنوان / Title:</strong> <span id="previewTitle">-</span></p>
                    <p><strong>الوصف / Description:</strong> <span id="previewDescription">-</span></p>
                    <p><strong>نص الزر / Button Text:</strong> <span id="previewButton">-</span></p>
                </div>
            </div>

            <div class="status" id="testStatus">
                <span class="info">جاهز للاختبار / Ready for testing</span>
            </div>
        </div>

        <!-- Language System Status -->
        <div class="test-section">
            <h2>حالة نظام اللغة / Language System Status</h2>
            <div class="button-group">
                <button onclick="checkLanguageSystem()">فحص النظام / Check System</button>
                <button onclick="clearLanguageData()">مسح البيانات / Clear Data</button>
            </div>
            <div class="status" id="systemStatus">
                <span class="info">انقر على "فحص النظام" للتحقق / Click "Check System" to verify</span>
            </div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-manager.js"></script>
    <script src="translations.js"></script>
    <script src="script.js"></script>

    <script>
        // Initialize
        let currentLanguage = localStorage.getItem('selectedLanguage') || 'en';

        // Update UI on load
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageUI();
            updateDialogPreview();
        });

        // Set language
        function setLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('selectedLanguage', lang);
            localStorage.setItem('languageSelected', 'true');

            // Update translation manager if available
            if (window.translationManager) {
                window.translationManager.setLanguage(lang);
            }

            updateLanguageUI();
            updateDialogPreview();

            document.getElementById('testStatus').innerHTML =
                `<span class="success">تم تغيير اللغة إلى ${lang === 'ar' ? 'العربية' : 'الإنجليزية'} / Language changed to ${lang === 'ar' ? 'Arabic' : 'English'}</span>`;
        }

        // Update language UI
        function updateLanguageUI() {
            document.getElementById('currentLang').textContent =
                currentLanguage === 'ar' ? 'العربية / Arabic' : 'الإنجليزية / English';

            // Update button styles
            document.getElementById('btnArabic').classList.toggle('current-language', currentLanguage === 'ar');
            document.getElementById('btnEnglish').classList.toggle('current-language', currentLanguage === 'en');

            // Update document direction
            document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.lang = currentLanguage;
        }

        // Update dialog preview
        function updateDialogPreview() {
            const isArabic = currentLanguage === 'ar';

            const sampleDialog = {
                title: 'مربع تجريبي',
                title_en: 'Test Dialog',
                description: 'هذا مربع تجريبي لاختبار النظام ثنائي اللغة',
                description_en: 'This is a test dialog to verify the bilingual system',
                button_text: 'تم',
                button_text_en: 'OK'
            };

            document.getElementById('previewTitle').textContent = isArabic
                ? (sampleDialog.title || sampleDialog.title_en || 'عنوان غير محدد')
                : (sampleDialog.title_en || sampleDialog.title || 'No title');

            document.getElementById('previewDescription').textContent = isArabic
                ? (sampleDialog.description || sampleDialog.description_en || '')
                : (sampleDialog.description_en || sampleDialog.description || '');

            document.getElementById('previewButton').textContent = isArabic
                ? (sampleDialog.button_text || sampleDialog.button_text_en || 'تم')
                : (sampleDialog.button_text_en || sampleDialog.button_text || 'OK');
        }

        // Test custom dialog
        function testCustomDialog() {
            const testDialog = {
                id: 'test_dialog_' + Date.now(),
                title: 'مربع تجريبي محدث',
                title_en: 'Updated Test Dialog',
                description: 'هذا مربع تجريبي بتصميم جديد: خلفية سوداء، نصوص بيضاء، بدون خط بيكسل.',
                description_en: 'This is a test dialog with new design: black background, white text, no pixel font.',
                button_text: 'فهمت',
                button_text_en: 'Got it',
                show_dont_show_again: true,
                is_active: true,
                image_url: null
            };

            // Create fake item for testing
            const fakeItem = { id: 'test_mod_123', name: 'Test Mod' };

            if (typeof showCustomDialog === 'function') {
                showCustomDialog(testDialog, fakeItem, function(item) {
                    document.getElementById('testStatus').innerHTML =
                        `<span class="success">تم إغلاق المربع بنجاح / Dialog closed successfully</span>`;
                });
            } else {
                document.getElementById('testStatus').innerHTML =
                    `<span class="error">دالة showCustomDialog غير متوفرة / showCustomDialog function not available</span>`;
            }
        }

        // Test mod dialog (requires actual mod data)
        function testModDialog() {
            document.getElementById('testStatus').innerHTML =
                `<span class="info">هذا الاختبار يتطلب بيانات مود حقيقية من قاعدة البيانات / This test requires real mod data from database</span>`;
        }

        // Check language system
        function checkLanguageSystem() {
            const status = [];

            // Check localStorage
            const storedLang = localStorage.getItem('selectedLanguage');
            const langSelected = localStorage.getItem('languageSelected');
            status.push(`localStorage: selectedLanguage=${storedLang}, languageSelected=${langSelected}`);

            // Check translation manager
            if (window.translationManager) {
                status.push(`TranslationManager: available, current=${window.translationManager.getCurrentLanguage()}`);
            } else {
                status.push(`TranslationManager: not available`);
            }

            // Check custom dialog functions
            if (typeof showCustomDialog === 'function') {
                status.push(`showCustomDialog: available`);
            } else {
                status.push(`showCustomDialog: not available`);
            }

            if (typeof checkCustomDialog === 'function') {
                status.push(`checkCustomDialog: available`);
            } else {
                status.push(`checkCustomDialog: not available`);
            }

            document.getElementById('systemStatus').innerHTML =
                `<span class="info">${status.join('<br>')}</span>`;
        }

        // Clear language data
        function clearLanguageData() {
            localStorage.removeItem('selectedLanguage');
            localStorage.removeItem('languageSelected');
            currentLanguage = 'en';
            updateLanguageUI();
            updateDialogPreview();

            document.getElementById('systemStatus').innerHTML =
                `<span class="success">تم مسح بيانات اللغة / Language data cleared</span>`;
        }
    </script>
</body>
</html>
