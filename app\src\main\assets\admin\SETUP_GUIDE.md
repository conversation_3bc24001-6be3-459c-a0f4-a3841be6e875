# 🚀 دليل الإعداد السريع - نظام الاشتراك المجاني

## ⚡ إعداد سريع (5 دقائق)

### الخطوة 1: إعداد قاعدة البيانات

1. **افتح Supabase Dashboard**:
   - اذهب إلى: https://supabase.com/dashboard
   - اختر مشروعك

2. **افتح SQL Editor**:
   - من القائمة الجانبية، انقر على "SQL Editor"
   - انقر على "New query"

3. **تشغيل كود الإعداد**:
   - انسخ كامل محتوى ملف: `database/setup_subscription_system.sql`
   - الصق الكود في SQL Editor
   - انقر على "Run" أو اضغط `Ctrl+Enter`

4. **التحقق من النجاح**:
   - يجب أن ترى رسالة: "تم إنشاء الجداول بنجاح!"
   - تحقق من إنشاء الجداول في قسم "Table Editor"

### الخطوة 2: إعداد Storage (للصور)

1. **إنشاء Bucket**:
   - اذهب إلى "Storage" في القائمة الجانبية
   - انقر على "Create a new bucket"
   - اسم الـ bucket: `banner-images`
   - اجعله **Public** (عام)

2. **إعداد السياسات**:
   - انقر على الـ bucket الجديد
   - اذهب إلى "Policies"
   - أضف policy للقراءة العامة:
     ```sql
     CREATE POLICY "Public Access" ON storage.objects FOR SELECT USING (bucket_id = 'banner-images');
     ```

### الخطوة 3: تشغيل لوحة الإدارة

#### الطريقة الأسهل (Windows):
```bash
# انقر نقراً مزدوجاً على:
start_admin.bat
```

#### الطريقة اليدوية:
```bash
cd app/src/main/assets/admin
python server.py
```

### الخطوة 4: اختبار النظام

1. **افتح لوحة الإدارة**: http://localhost:8000
2. **انتقل إلى "إدارة الاشتراك المجاني"**
3. **تحقق من ظهور الحملة التجريبية**
4. **جرب إنشاء حملة جديدة**

---

## 🔧 حل المشاكل الشائعة

### مشكلة: "relation does not exist"
**الحل**: لم يتم تشغيل كود SQL بعد
- تأكد من تشغيل `setup_subscription_system.sql` كاملاً
- تحقق من عدم وجود أخطاء في SQL Editor

### مشكلة: "Not allowed to load local resource"
**الحل**: استخدم الخادم المحلي
```bash
python server.py
```

### مشكلة: عدم ظهور الصور
**الحل**: تأكد من إعداد Storage
- إنشاء bucket `banner-images`
- جعله عام (Public)
- إضافة policy للقراءة

### مشكلة: خطأ في الاتصال بـ Supabase
**الحل**: تحقق من المفاتيح
- تأكد من صحة `SUPABASE_URL`
- تأكد من صحة `SUPABASE_ANON_KEY`

---

## 📋 قائمة التحقق

- [ ] تشغيل كود SQL في Supabase
- [ ] إنشاء bucket `banner-images`
- [ ] جعل الـ bucket عام
- [ ] تشغيل الخادم المحلي
- [ ] فتح لوحة الإدارة
- [ ] اختبار إنشاء حملة جديدة

---

## 🎯 الخطوات التالية

بعد الإعداد الناجح:

1. **إنشاء حملة حقيقية**:
   - احذف الحملة التجريبية
   - أنشئ حملة بمعلومات حقيقية

2. **إضافة مهام مخصصة**:
   - أضف روابط قنواتك الحقيقية
   - حدد المهام المطلوبة

3. **إنشاء بانر للحملة**:
   - اذهب إلى "إدارة البانر"
   - أنشئ بانر من نوع "اشتراك مجاني"
   - ارفع صورة مناسبة

4. **اختبار التجربة الكاملة**:
   - اختبر النقر على البانر في التطبيق
   - تأكد من ظهور النوافذ المنبثقة
   - اختبر إكمال المهام

---

## 📞 الدعم

إذا واجهت مشاكل:

1. **تحقق من Console المتصفح** للأخطاء
2. **راجع هذا الدليل** مرة أخرى
3. **تأكد من تشغيل جميع خطوات الإعداد**

---

**🎉 مبروك! نظام الاشتراك المجاني جاهز للاستخدام!**
