-- ========================================
-- إنشاء الجداول المفقودة في Supabase
-- Create Missing Tables in Supabase
-- ========================================

-- 1. إنشاء جدول custom_mod_dialogs
-- Create custom_mod_dialogs table
CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100) DEFAULT 'تم',
    show_dont_show_again BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. إن<PERSON>اء جدول custom_dialog_mods (junction table)
-- Create custom_dialog_mods table (junction table)
CREATE TABLE IF NOT EXISTS custom_dialog_mods (
    id SERIAL PRIMARY KEY,
    dialog_id INTEGER NOT NULL,
    mod_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_dialog_mods_dialog_id FOREIGN KEY (dialog_id) REFERENCES custom_mod_dialogs(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_dialog_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_dialog_mod UNIQUE (dialog_id, mod_id)
);

-- 3. إنشاء جدول custom_copyright_mods
-- Create custom_copyright_mods table
CREATE TABLE IF NOT EXISTS custom_copyright_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_copyright_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_copyright_mod UNIQUE (mod_id)
);

-- 4. إنشاء دالة execute_sql للاستخدام المستقبلي (اختيارية)
-- Create execute_sql function for future use (optional)
CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- هذه الدالة محدودة لأغراض الأمان
    -- This function is limited for security purposes
    -- يمكن توسيعها حسب الحاجة
    -- Can be expanded as needed
    
    -- للأمان، نرفض تنفيذ أي استعلامات خطيرة
    -- For security, reject any dangerous queries
    IF sql_query ILIKE '%DROP%' OR 
       sql_query ILIKE '%DELETE%' OR 
       sql_query ILIKE '%TRUNCATE%' OR
       sql_query ILIKE '%ALTER%' THEN
        RAISE EXCEPTION 'Dangerous SQL operations are not allowed';
    END IF;
    
    -- تنفيذ الاستعلام
    -- Execute the query
    EXECUTE sql_query;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error executing SQL: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. إضافة تعليقات توضيحية
-- Add documentation comments
COMMENT ON TABLE custom_mod_dialogs IS 'Stores custom dialog configurations for mods';
COMMENT ON TABLE custom_dialog_mods IS 'Junction table linking dialogs to specific mods';
COMMENT ON TABLE custom_copyright_mods IS 'Tracks mods that have custom copyright descriptions';

-- 6. إنشاء فهارس لتحسين الأداء
-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_custom_dialog_mods_dialog_id ON custom_dialog_mods(dialog_id);
CREATE INDEX IF NOT EXISTS idx_custom_dialog_mods_mod_id ON custom_dialog_mods(mod_id);
CREATE INDEX IF NOT EXISTS idx_custom_copyright_mods_mod_id ON custom_copyright_mods(mod_id);
CREATE INDEX IF NOT EXISTS idx_custom_mod_dialogs_active ON custom_mod_dialogs(is_active);

-- 7. إنشاء trigger لتحديث updated_at تلقائياً
-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق trigger على الجداول
-- Apply trigger to tables
CREATE TRIGGER update_custom_mod_dialogs_updated_at
    BEFORE UPDATE ON custom_mod_dialogs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_copyright_mods_updated_at
    BEFORE UPDATE ON custom_copyright_mods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 8. إنشاء view للإحصائيات
-- Create statistics view
CREATE OR REPLACE VIEW custom_dialogs_stats AS
SELECT 
    COUNT(*) as total_dialogs,
    COUNT(*) FILTER (WHERE is_active = true) as active_dialogs,
    COUNT(DISTINCT cdm.mod_id) as mods_with_dialogs,
    AVG(CASE WHEN is_active THEN 1 ELSE 0 END) * 100 as active_percentage
FROM custom_mod_dialogs cmd
LEFT JOIN custom_dialog_mods cdm ON cmd.id = cdm.dialog_id;

-- 9. دالة للحصول على dialogs خاصة بمود معين
-- Function to get dialogs for a specific mod
CREATE OR REPLACE FUNCTION get_mod_dialogs(p_mod_id UUID)
RETURNS TABLE (
    dialog_id INTEGER,
    title VARCHAR(255),
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100),
    show_dont_show_again BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cmd.id,
        cmd.title,
        cmd.description,
        cmd.image_url,
        cmd.button_text,
        cmd.show_dont_show_again
    FROM custom_mod_dialogs cmd
    INNER JOIN custom_dialog_mods cdm ON cmd.id = cdm.dialog_id
    WHERE cdm.mod_id = p_mod_id 
    AND cmd.is_active = true
    ORDER BY cmd.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 10. دالة للتحقق من وجود copyright مخصص لمود
-- Function to check if mod has custom copyright
CREATE OR REPLACE FUNCTION has_custom_copyright(p_mod_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    result BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM custom_copyright_mods 
        WHERE mod_id = p_mod_id AND is_active = true
    ) INTO result;
    
    RETURN COALESCE(result, false);
END;
$$ LANGUAGE plpgsql;
