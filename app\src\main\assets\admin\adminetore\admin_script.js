document.addEventListener('DOMContentLoaded', () => {
    // --- Supabase Configuration (Centralized) ---
    // Get client from centralized manager to prevent multiple instances
    let supabaseClient;
    let tableNames;

    // Initialize Supabase client
    if (typeof supabaseManager !== 'undefined') {
        supabaseClient = supabaseManager.getMainClient();
        tableNames = supabaseManager.getTableNames();
        console.log('Admin: Using centralized Supabase client');
    } else {
        console.warn('Admin: Supabase manager not available, creating direct client');
        const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
        supabaseClient = supabase.createClient(supabaseUrl, supabaseKey, {
            auth: {
                persistSession: false,
                autoRefreshToken: false,
                detectSessionInUrl: false
            }
        });
        tableNames = {
            MODS: 'mods',
            SUGGESTED_MODS: 'suggested_mods',
            FEATURED_ADDONS: 'featured_addons',
            FREE_ADDONS: 'free_addons',
            BANNER_ADS: 'banner_ads',
            UPDATE_NOTIFICATIONS: 'update_notifications',
            APP_ANNOUNCEMENTS: 'app_announcements',
            DRAWER_LINKS: 'drawer_links'
        };
    }

    // Storage Client (using centralized manager)
    let storageClient;
    const imageBucketName = 'image';
    const modsBucketName = 'zanzan4';

    // Get storage client from manager
    if (typeof supabaseManager !== 'undefined') {
        storageClient = supabaseManager.getStorageClient();
        console.log('Admin: Using centralized storage client');
    } else {
        console.warn('Admin: Creating separate storage client');
        const modStorageUrl = 'https://mwxzwfeqsashcwvqthmd.supabase.co';
        const modStorageKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im13eHp3ZmVxc2FzaGN3dnF0aG1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MzU2NDcsImV4cCI6MjA2MTQxMTY0N30.nU0smAgNsoLi1zRNKA3AFM3q112jp4fhPgYeeXqKmPU';
        storageClient = supabase.createClient(modStorageUrl, modStorageKey, {
            auth: {
                persistSession: false,
                autoRefreshToken: false,
                detectSessionInUrl: false
            }
        });
    }

    console.log("Admin Dashboard Script Loaded. Using centralized Supabase clients.");

    // --- DOM Elements ---
    const mostClickedList = document.getElementById('most-clicked-list');
    const mostLikedList = document.getElementById('most-liked-list');
    const mostDownloadedList = document.getElementById('most-downloaded-list');
    const modsTableBody = document.getElementById('mods-table-body');
    const showPublishFormBtn = document.getElementById('show-publish-form-btn');
    const publishModFormContainer = document.getElementById('publish-mod-form-container');
    const editModFormContainer = document.getElementById('edit-mod-form-container');
    const publishModForm = document.getElementById('publish-mod-form');
    const editModForm = document.getElementById('edit-mod-form');
    const cancelPublishBtn = document.getElementById('cancel-publish-btn');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    const deleteModBtn = document.getElementById('delete-mod-btn');
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const publishImageInput = document.getElementById('publish-images');
    const publishImagePreviews = document.getElementById('publish-image-previews');
    const publishOtherImageInput = document.getElementById('publish-other-images');
    const publishOtherImagePreviews = document.getElementById('publish-other-image-previews');
    const publishExtraImageInput = document.getElementById('publish-extra-images'); // Added new input reference
    const publishExtraImagePreviews = document.getElementById('publish-extra-image-previews'); // Added new preview reference
    const publishModFileInput = document.getElementById('publish-mod-file');
    const editNewImageInput = document.getElementById('edit-new-images');
    const editCurrentImagePreviews = document.getElementById('edit-current-images');
    const editNewImagePreviews = document.getElementById('edit-new-image-previews');
    const editModFileInput = document.getElementById('edit-mod-file'); // Added
    const editIsRequiredCheckbox = document.getElementById('edit-is-required'); // Added for required checkbox
    // Recent Activity Elements
    const recentLikesBody = document.getElementById('recent-likes-body');
    // Delete All Button
    const deleteAllModsBtn = document.getElementById('delete-all-mods-btn'); // Added button reference
    // const storageUsageText = document.getElementById('storage-usage-text'); // Not dynamically updated for now
    // filesToDeleteContainer was removed as the section is gone.

    // User Activity Stat Elements
    const totalUsersCountEl = document.getElementById('total-users-count');
    const activeUsersCountEl = document.getElementById('active-users-count');
    const inactiveUsersCountEl = document.getElementById('inactive-users-count');

    // User Preferences Elements
    const popularCategoriesListEl = document.getElementById('popular-categories-list');

    // Drawer Links Management Elements
    const drawerLinkForm = document.getElementById('drawer-link-form');
    const drawerLinkIdInput = document.getElementById('drawer-link-id');
    const drawerLinkTextInput = document.getElementById('drawer-link-text');
    const drawerLinkUrlInput = document.getElementById('drawer-link-url');
    const drawerLinkIconInput = document.getElementById('drawer-link-icon');
    const drawerLinkOrderInput = document.getElementById('drawer-link-order');
    const drawerLinkActiveCheckbox = document.getElementById('drawer-link-active');
    const drawerLinksTableBody = document.getElementById('drawer-links-table-body');
    const cancelDrawerLinkEditBtn = document.getElementById('cancel-drawer-link-edit-btn');
    const drawerLinkSocialIconSelect = document.getElementById('drawer-link-social-icon'); // Added social icon select

    // Suggested Mods Elements
    const searchAvailableSuggestInput = document.getElementById('search-available-suggest-input');
    const categoryFilterAvailableSuggest = document.getElementById('category-filter-available-suggest');
    const availableModsForSuggestionBody = document.getElementById('available-mods-for-suggestion-body');
    const currentSuggestedModsBody = document.getElementById('current-suggested-mods-body');

    // Categories Management Elements
    const categoryForm = document.getElementById('category-form');
    const categoryIdInput = document.getElementById('category-id');
    const categoryNameInput = document.getElementById('category-name');
    const categoryIconInput = document.getElementById('category-icon');
    const categoriesTableBody = document.getElementById('categories-table-body');
    const cancelCategoryEditBtn = document.getElementById('cancel-category-edit-btn');


    let allModsData = []; // To store fetched mods for filtering
    let otherFilesArray = []; // Array to hold files for the "other images" input
    let extraFilesArray = []; // Array to hold files for the "extra images" input
    let editNewFilesArray = []; // Array to hold files for the edit form new images

    // Table constants (using centralized manager when available)
    const DRAWER_LINKS_TABLE = tableNames ? tableNames.DRAWER_LINKS : 'drawer_links';
    const SUGGESTED_MODS_TABLE = tableNames ? tableNames.SUGGESTED_MODS : 'suggested_mods';
    const CATEGORIES_TABLE = 'categories'; // Not in centralized manager yet
    const CATEGORY_MODS_TABLE = 'category_mods'; // Not in centralized manager yet

    // --- Helper Functions ---

    // Function to load categories into a dropdown
    async function loadCategoriesForDropdown(selectElement) {
        if (!selectElement) return;

        try {
            const { data: categories, error } = await supabaseClient
                .from(CATEGORIES_TABLE)
                .select('id, name, icon_class')
                .order('name', { ascending: true });

            if (error) throw error;

            // Store the current selected value if any
            const currentValue = selectElement.value;

            // Clear existing options except the default ones
            const defaultOptions = Array.from(selectElement.options)
                .filter(option => ['Addons', 'Texture', 'Shaders'].includes(option.value));

            selectElement.innerHTML = '';

            // Add back the default options
            defaultOptions.forEach(option => {
                selectElement.appendChild(option);
            });

            // Add new options from the database
            if (categories && categories.length > 0) {
                categories.forEach(category => {
                    // Skip if this category is already in the default options
                    if (defaultOptions.some(opt => opt.value === category.name)) {
                        return;
                    }

                    const option = document.createElement('option');
                    option.value = category.name;
                    option.textContent = category.name;
                    if (category.icon_class) {
                        option.setAttribute('data-icon', category.icon_class);
                    }
                    selectElement.appendChild(option);
                });
            }

            // Restore the previously selected value if it exists
            if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
                selectElement.value = currentValue;
            }

        } catch (error) {
            console.error("Error loading categories for dropdown:", error);
            // Don't show an alert as this is a background operation
        }
    }

    // Helper to extract storage path from public URL
    function getPathFromUrl(url, bucketName) {
        if (!url || !bucketName) return null;
        try {
            // Example URL structure: https://[project_ref].supabase.co/storage/v1/object/public/[bucket_name]/[path/to/file.ext]
            // Or potentially just the path if stored differently. Let's assume the standard public URL.
            const urlPrefix = `${storageClient.storageUrl}/object/public/${bucketName}/`;
            if (url.startsWith(urlPrefix)) {
                return url.substring(urlPrefix.length);
            }
            // Fallback: Maybe the URL itself is just the path? (Less likely for public URLs)
            // Basic check to avoid trying to delete full URLs as paths
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                return url;
            }
            console.warn(`Could not extract path from URL: ${url} for bucket: ${bucketName}`);
            return null;
        } catch (e) {
            console.error(`Error parsing URL ${url}:`, e);
            return null;
        }
    }

    function formatCount(count) {
        const num = Number(count);
        if (isNaN(num)) return '0';
        if (num >= 1_000_000) return (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + "M";
        if (num >= 1_000) return (num / 1_000).toFixed(1).replace(/\.0$/, '') + "K";
        return num.toString();
    }

    // Function to display loading state in a list
    function showLoading(listElement) {
        if (listElement) {
            listElement.innerHTML = '<li>جاري التحميل...</li>';
        }
    }

    // Function to display loading state in the table
    function showTableLoading() {
        if (modsTableBody) {
            modsTableBody.innerHTML = '<tr><td colspan="6">جاري تحميل المودات...</td></tr>';
        }
    }

    // Function to display error state
    function showError(element, message = "خطأ في تحميل البيانات.") {
        if (element) {
            if (element.tagName === 'TBODY') {
                element.innerHTML = `<tr><td colspan="6" style="color: red;">${message}</td></tr>`;
            } else if (element.tagName === 'OL') {
                element.innerHTML = `<li style="color: red;">${message}</li>`;
            } else {
                 element.innerHTML = `<p style="color: red;">${message}</p>`;
            }
        }
    }

    // Function to format timestamp
    function formatTimestamp(timestamp) {
        if (!timestamp) return 'N/A';
        try {
            const date = new Date(timestamp);
            // Options for formatting (adjust as needed)
            const options = {
                year: 'numeric', month: 'short', day: 'numeric',
                hour: '2-digit', minute: '2-digit', hour12: true
            };
            return date.toLocaleString('ar', options); // Use Arabic locale
        } catch (e) {
            console.error("Error formatting timestamp:", e);
            return timestamp; // Return original if formatting fails
        }
    }
    // --- Recent Activity Fetching ---
    async function fetchRecentLikes() {
        if (!recentLikesBody) return;
        recentLikesBody.innerHTML = '<tr><td colspan="3">جاري التحميل...</td></tr>'; // Show loading

        try {
            // Fetch recent likes, joining with mods table to get mod name
            const { data: likesData, error: likesError } = await supabaseClient
                .from('user_likes') // Corrected table name
                .select(`
                    user_id,
                    created_at,
                    mods ( name )
                `) // Attempt to fetch related mod name again
                .order('created_at', { ascending: false })
                .limit(15); // Limit to recent 15 likes

            if (likesError) throw likesError;

            if (!likesData || likesData.length === 0) {
                recentLikesBody.innerHTML = '<tr><td colspan="3">لا توجد إعجابات حديثة.</td></tr>';
                return;
            }

            // Display the data
            recentLikesBody.innerHTML = likesData.map(like => `
                <tr>
                    <td>${like.user_id || 'غير معروف'}</td>
                    <td>${like.mods?.name || like.mod_id || 'مود غير معروف'}</td> <!-- Show mod_id if name is null -->
                    <td>${formatTimestamp(like.created_at)}</td>
                </tr>
            `).join('');

        } catch (error) {
            console.error("Error fetching recent likes:", error);
             // Check for specific errors like table/column not found or RLS issues
             if (error.message.includes("relation \"user_likes\" does not exist")) { // Corrected table name in error check
                 showError(recentLikesBody, 'خطأ: جدول "user_likes" غير موجود.');
             } else if (error.message.includes("permission denied")) {
                  showError(recentLikesBody, 'خطأ: تم رفض الإذن للوصول إلى الإعجابات.');
             } else if (error.message.includes("Could not find a relationship")) { // Check for relationship error
                 showError(recentLikesBody, 'خطأ: لم يتم العثور على علاقة بين user_likes و mods. هل تم تعريف المفتاح الخارجي؟');
             } else {
                 showError(recentLikesBody, `خطأ في تحميل أحدث الإعجابات: ${error.message}`);
             }
        }
    }


    function updateUsageBarColor(barElement, percentage) {
        barElement.classList.remove('high-usage', 'critical-usage');
        if (percentage >= 90) {
            barElement.classList.add('critical-usage');
        } else if (percentage >= 75) {
            barElement.classList.add('high-usage');
        }
    }


    // --- Statistics Fetching ---
    async function fetchStats() {
        showLoading(mostClickedList);
        showLoading(mostLikedList);
        showLoading(mostDownloadedList);

        try {
            // Most Clicked (Requires 'clicks' column added previously)
            const { data: clickedData, error: clickedError } = await supabaseClient
                .from('mods')
                .select('id, name, clicks')
                .order('clicks', { ascending: false })
                .limit(5);

            if (clickedError) throw clickedError;
            displayStatsList(mostClickedList, clickedData, 'clicks');
            // Most Liked
            const { data: likedData, error: likedError } = await supabaseClient
                .from('mods')
                .select('id, name, likes')
                .order('likes', { ascending: false })
                .limit(5);

            if (likedError) throw likedError;
            displayStatsList(mostLikedList, likedData, 'likes');
            // Most Downloaded
            const { data: downloadedData, error: downloadedError } = await supabaseClient
                .from('mods')
                .select('id, name, downloads')
                .order('downloads', { ascending: false })
                .limit(5);

            if (downloadedError) throw downloadedError;
            displayStatsList(mostDownloadedList, downloadedData, 'downloads');

        } catch (error) {
            console.error("Error fetching stats:", error);
            showError(mostClickedList, `خطأ في تحميل النقرات: ${error.message}`);
            showError(mostLikedList, `خطأ في تحميل الإعجابات: ${error.message}`);
            showError(mostDownloadedList, `خطأ في تحميل التحميلات: ${error.message}`);
        }
    }

    function displayStatsList(listElement, data, field) {
        if (!listElement) return;
        if (!data || data.length === 0) {
            listElement.innerHTML = '<li>لا توجد بيانات متاحة.</li>';
            return;
        }
        listElement.innerHTML = data.map(item =>
            `<li>${item.name || 'مود بدون اسم'} (${formatCount(item[field] || 0)})</li>`
        ).join('');
    }

    // --- Mod Management Fetching & Display ---
    async function fetchAllMods() {
        showTableLoading();
        try {
            const { data, error } = await supabaseClient
                .from('mods')
                .select('*')
                .order('created_at', { ascending: false }); // Default sort

            if (error) throw error;

            allModsData = data || [];

            // Update category filters with all available categories
            updateCategoryFilters();

            filterAndDisplayMods(); // Initial display

        } catch (error) {
            console.error("Error fetching mods:", error);
            showError(modsTableBody, `خطأ في تحميل المودات: ${error.message}`);
            allModsData = []; // Reset data on error
        }
    }

    // Function to update category filters in dropdowns
    async function updateCategoryFilters() {
        // Get all category filter dropdowns
        const categoryFilters = [
            categoryFilter,
            categoryFilterAvailableSuggest,
            document.getElementById('publish-category'),
            document.getElementById('edit-category')
        ].filter(el => el); // Filter out any null elements

        try {
            // Get all categories from the database
            const { data: categories, error } = await supabaseClient
                .from(CATEGORIES_TABLE)
                .select('name, icon_class')
                .order('name', { ascending: true });

            if (error) throw error;

            // Get unique categories from mods data as well
            const modCategories = new Set(allModsData.map(mod => mod.category).filter(Boolean));

            // Update each filter dropdown
            categoryFilters.forEach(filter => {
                // Store current value
                const currentValue = filter.value;

                // Keep the "All" option if it exists
                const allOption = Array.from(filter.options).find(opt => opt.value === 'All');

                // Keep default options for publish/edit forms
                const defaultOptions = Array.from(filter.options)
                    .filter(option => ['All', 'Addons', 'Texture', 'Shaders'].includes(option.value));

                // Clear dropdown
                filter.innerHTML = '';

                // Add back default options
                defaultOptions.forEach(option => {
                    filter.appendChild(option);
                });

                // Add categories from database
                if (categories && categories.length > 0) {
                    categories.forEach(category => {
                        // Skip if already in default options
                        if (defaultOptions.some(opt => opt.value === category.name)) {
                            return;
                        }

                        const option = document.createElement('option');
                        option.value = category.name;
                        option.textContent = category.name;
                        if (category.icon_class) {
                            option.setAttribute('data-icon', category.icon_class);
                        }
                        filter.appendChild(option);
                    });
                }

                // Add any additional categories from mods that aren't in the database yet
                modCategories.forEach(category => {
                    // Skip if already added or in default options
                    if (!category ||
                        Array.from(filter.options).some(opt => opt.value === category) ||
                        defaultOptions.some(opt => opt.value === category)) {
                        return;
                    }

                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    filter.appendChild(option);
                });

                // Restore selected value if it still exists
                if (currentValue && Array.from(filter.options).some(opt => opt.value === currentValue)) {
                    filter.value = currentValue;
                }
            });

        } catch (error) {
            console.error("Error updating category filters:", error);
            // Don't show alert as this is a background operation
        }
    }

    function filterAndDisplayMods() {
        if (!modsTableBody) return;

        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;

        const filteredMods = allModsData.filter(mod => {
            const nameMatch = mod.name?.toLowerCase().includes(searchTerm) ?? true;
            const categoryMatch = selectedCategory === 'All' || mod.category === selectedCategory;
            return nameMatch && categoryMatch;
        });

        displayModsTable(filteredMods);
    }

    function displayModsTable(mods) {
        if (!modsTableBody) return;
        if (mods.length === 0) {
            modsTableBody.innerHTML = '<tr><td colspan="6">لم يتم العثور على مودات تطابق المعايير.</td></tr>';
            return;
        }

        modsTableBody.innerHTML = mods.map(mod => `
            <tr data-id="${mod.id}">
                <td>${mod.name || 'N/A'}</td>
                <td>${mod.category || 'N/A'}</td>
                <td>${formatCount(mod.clicks || 0)}</td>
                <td>${formatCount(mod.likes || 0)}</td>
                <td>${formatCount(mod.downloads || 0)}</td>
                <td class="action-cell">
                    <button class="edit-button" data-id="${mod.id}"><i class="fas fa-edit"></i> تعديل</button>
                    <button class="delete-button" data-id="${mod.id}"><i class="fas fa-trash"></i> حذف</button>
                </td>
            </tr>
        `).join('');

        // Add event listeners to the new buttons
        addTableButtonListeners();
    }

    function addTableButtonListeners() {
        document.querySelectorAll('.edit-button').forEach(button => {
            button.addEventListener('click', handleEditClick);
        });
        document.querySelectorAll('.delete-button').forEach(button => {
            button.addEventListener('click', handleDeleteClick);
        });
    }

    // --- Image Upload Helper ---
    async function uploadImages(fileList, modId) {
        if (!fileList || fileList.length === 0) {
            return []; // No files to upload
        }

        const uploadPromises = Array.from(fileList).map(async (file) => {
            const fileExt = file.name.split('.').pop();
            const fileName = `${modId}_${Date.now()}_${Math.random().toString(36).substring(2)}.${fileExt}`;
            const filePath = `${fileName}`; // Store directly in the bucket root for simplicity

            console.log(`Uploading image: ${file.name} as ${filePath}`);

            // Use the dedicated storageClient for image uploads
            const { data, error } = await storageClient.storage // Use storageClient here
                .from(imageBucketName)
                .upload(filePath, file, {
                    cacheControl: '3600', // Cache for 1 hour
                    upsert: false // Don't overwrite existing files with the same name (should be unique)
                });

            if (error) {
                console.error(`Error uploading ${file.name}:`, error);
                throw new Error(`فشل تحميل ${file.name}: ${error.message}`); // Added semicolon
            }
            // Construct the public URL using the dedicated storageClient
            const { data: urlData } = storageClient.storage // Use storageClient here
                .from(imageBucketName)
                .getPublicUrl(filePath);

            console.log(`Upload successful for ${file.name}. Public URL: ${urlData?.publicUrl}`);
            return urlData?.publicUrl;
        }); // Closing parenthesis for async arrow function AND map() call

        // Wait for all uploads to complete
        const publicUrls = await Promise.all(uploadPromises);
        return publicUrls.filter(url => url); // Filter out any potential nulls/undefineds
    }

    // --- Mod File Upload Helper ---
    async function uploadModFile(file, modName) {
        if (!file) {
            return null; // No file to upload
        }

        // Sanitize mod name for use in filename, or use a generic name
        const sanitizedName = modName ? modName.replace(/[^a-zA-Z0-9_.-]/g, '_').substring(0, 50) : 'mod_file';
        const fileExt = file.name.split('.').pop();
        // Include timestamp/random string for uniqueness, store in bucket root
        const fileName = `${sanitizedName}_${Date.now()}_${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `${fileName}`;

        console.log(`Uploading mod file: ${file.name} as ${filePath} to bucket: ${modsBucketName}`);

        // Use the dedicated storageClient for mod file uploads
        const { data, error } = await storageClient.storage // Use storageClient here
            .from(modsBucketName)
            .upload(filePath, file, {
                cacheControl: '3600', // Cache for 1 hour
                upsert: false
            });

            if (error) {
                console.error(`Error uploading mod file ${file.name}:`, error);
                throw new Error(`فشل تحميل ملف المود ${file.name}: ${error.message}`); // Added semicolon
            }
        // Construct the public URL using the dedicated storageClient
        const { data: urlData } = storageClient.storage // Use storageClient here
            .from(modsBucketName)
            .getPublicUrl(filePath);

        console.log(`Mod file upload successful for ${file.name}. Public URL: ${urlData?.publicUrl}`);
        return urlData?.publicUrl;
    }


    // --- Image Preview Helper ---
    // Updated to handle arrays and add remove buttons
    function displayImagePreviews(filesArray, previewContainer, associatedArray) {
        previewContainer.innerHTML = ''; // Clear existing previews
        filesArray.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imgContainer = document.createElement('div');
                imgContainer.classList.add('img-container'); // Add class for styling

                const img = document.createElement('img');
                img.src = e.target.result;
                img.alt = file.name;

                const removeBtn = document.createElement('button');
                const closeIconImg = document.createElement('img');
                closeIconImg.src = 'image/close_icon.png';
                closeIconImg.alt = 'Remove';
                closeIconImg.style.width = '16px'; // Adjust size as needed
                closeIconImg.style.height = '16px';
                closeIconImg.style.verticalAlign = 'middle';
                removeBtn.appendChild(closeIconImg);
                removeBtn.classList.add('remove-img-btn'); // Add class for styling
                removeBtn.type = 'button'; // Prevent form submission
                removeBtn.title = `إزالة ${file.name}`;
                removeBtn.onclick = () => {
                    // Remove the file from the associated array
                    associatedArray.splice(index, 1);
                    // Re-render the previews for that container
                    displayImagePreviews(associatedArray, previewContainer, associatedArray);
                    console.log(`Removed file: ${file.name}. Remaining in array:`, associatedArray.length);
                };

                imgContainer.appendChild(img);
                imgContainer.appendChild(removeBtn);
                previewContainer.appendChild(imgContainer);
            }
            reader.readAsDataURL(file);
        });
    }

     // Specific preview function for standard inputs (without remove button or array management)
     function displayStandardImagePreviews(inputElement, previewContainer) {
        previewContainer.innerHTML = ''; // Clear existing previews
        const files = inputElement.files;
        if (files) {
            Array.from(files).forEach(file => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    previewContainer.appendChild(img);
                }
                reader.readAsDataURL(file);
            });
        }
    }


    // --- Form Handling ---
    function showPublishForm() {
        publishModForm.reset(); // Clear form
        otherFilesArray = []; // Reset the array for other images
        extraFilesArray = []; // Reset the array for extra images
        publishImagePreviews.innerHTML = ''; // Clear previews
        publishOtherImagePreviews.innerHTML = ''; // Clear other previews (now managed by array)
        publishExtraImagePreviews.innerHTML = ''; // Clear extra previews
        editModFormContainer.style.display = 'none';
        publishModFormContainer.style.display = 'block';
        showPublishFormBtn.style.display = 'none';

        // Load categories for the dropdown
        loadCategoriesForDropdown(document.getElementById('publish-category'));
    }

    function hidePublishForm() {
        publishModFormContainer.style.display = 'none';
        showPublishFormBtn.style.display = 'block';
    }

    async function showEditForm(mod) {
        editModForm.reset(); // Clear form
        editCurrentImagePreviews.innerHTML = '';
        editNewImagePreviews.innerHTML = '';
        editNewFilesArray = []; // Reset the array for new images in edit form

        // Load categories for the dropdown
        loadCategoriesForDropdown(document.getElementById('edit-category'));

        // Populate form fields
        document.getElementById('edit-mod-id').value = mod.id;
        document.getElementById('edit-name').value = mod.name || '';
        document.getElementById('edit-description').value = mod.description || '';
        document.getElementById('edit-category').value = mod.category || 'Addons';
        document.getElementById('edit-version').value = mod.version || '';
        document.getElementById('edit-size').value = mod.size || '';
        document.getElementById('edit-download-url').value = mod.download_url || '';

        // Check if this mod is in the suggested_mods table and if it's marked as required
        try {
            const { data: suggestedMod, error } = await supabaseClient
                .from(SUGGESTED_MODS_TABLE)
                .select('is_required')
                .eq('mod_id', mod.id)
                .maybeSingle();

            if (!error && suggestedMod) {
                // Set the checkbox based on the is_required value
                editIsRequiredCheckbox.checked = suggestedMod.is_required || false;
            } else {
                // If not in suggested_mods table, uncheck the box
                editIsRequiredCheckbox.checked = false;
            }
        } catch (error) {
            console.error("Error checking if mod is required:", error);
            editIsRequiredCheckbox.checked = false;
        }

        // Display current images (if any)
        let currentImages = [];
        if (mod.image_urls) {
             try {
                 // Handle both array and JSON string representations
                 if (Array.isArray(mod.image_urls)) {
                     currentImages = mod.image_urls;
                 } else if (typeof mod.image_urls === 'string') {
                     currentImages = JSON.parse(mod.image_urls);
                 }
             } catch (e) {
                 console.error("Error parsing current image URLs:", e);
                 // Handle case where it might be a single URL string
                 if (typeof mod.image_urls === 'string' && mod.image_urls.startsWith('http')) {
                     currentImages = [mod.image_urls];
                 }
             }
        }


        if (Array.isArray(currentImages) && currentImages.length > 0) {
            currentImages.forEach(url => {
                if (typeof url === 'string' && url.startsWith('http')) {
                    const img = document.createElement('img');
                    img.src = url;
                    img.alt = "Current Image";
                    editCurrentImagePreviews.appendChild(img);
                }
            });
        } else {
            editCurrentImagePreviews.innerHTML = '<p>لا توجد صور حالية.</p>';
        }


        publishModFormContainer.style.display = 'none';
        editModFormContainer.style.display = 'block';
        showPublishFormBtn.style.display = 'none';
    }

    function hideEditForm() {
        editModFormContainer.style.display = 'none';
        showPublishFormBtn.style.display = 'block';
    }

    async function handlePublishSubmit(event) {
        event.preventDefault();
        const formData = new FormData(publishModForm);
        const modData = Object.fromEntries(formData.entries());
        const primaryImageFiles = publishImageInput.files;
        const otherImageFiles = publishOtherImageInput.files;
        const extraImageFiles = publishExtraImageInput.files; // Get files from the third input
        const modFile = publishModFileInput.files[0]; // Get the single mod file

        // Combine files: standard inputs + the managed arrays for "other images" and "extra images"
        const allImageFiles = [
            ...(primaryImageFiles || []),
            ...otherFilesArray, // Use the managed array for other images
            ...extraFilesArray  // Use the managed array for extra images
        ];

        console.log("Publishing mod:", modData);
        console.log("Total image files to upload:", allImageFiles.length, allImageFiles);
        alert("جاري نشر المود... يرجى الانتظار للتأكيد."); // Basic feedback

        let modDownloadUrl = modData.download_url; // Start with the URL from the input field
        try {
            // 1. Upload Mod File if provided
            if (modFile) {
                console.log("Mod file provided, uploading...");
                modDownloadUrl = await uploadModFile(modFile, modData.name);
                if (!modDownloadUrl) {
                    throw new Error("فشل تحميل ملف المود والحصول على الرابط.");
                }
                console.log("Using uploaded mod file URL:", modDownloadUrl);
            } else if (!modDownloadUrl) {
                 // Ensure download URL is provided either via input or upload
                 throw new Error("رابط التحميل مطلوب. يرجى لصق رابط أو رفع ملف مود.");
            }

            // 2. Insert basic mod data (using the determined download URL)
            const { data: insertedMod, error: insertError } = await supabaseClient
                .from('mods')
                .insert({
                    name: modData.name,
                    description: modData.description,
                    category: modData.category,
                    version: modData.version || null,
                    size: modData.size || null,
                    download_url: modDownloadUrl, // Use the determined URL
                    // Initialize counts/clicks
                    likes: 0,
                    downloads: 0,
                    clicks: 0
                })
                .select() // Return the inserted row
                .single(); // Expect only one row

            if (insertError) throw insertError;
            if (!insertedMod) throw new Error("فشل في استرداد بيانات المود المدرجة.");

            const newModId = insertedMod.id;
            console.log("Mod data inserted successfully. ID:", newModId);

            // 3. Upload images using the new mod ID
            let uploadedImageUrls = [];
            if (allImageFiles.length > 0) { // Use the combined list
                uploadedImageUrls = await uploadImages(allImageFiles, newModId); // Pass combined list
                console.log("Image URLs uploaded:", uploadedImageUrls);
            }

            // 4. Update the mod entry with the image URLs (if any were uploaded)
            if (uploadedImageUrls.length > 0) {
                const { error: updateError } = await supabaseClient
                    .from('mods')
                    .update({ image_urls: uploadedImageUrls })
                    .eq('id', newModId);

                if (updateError) {
                    // Attempt to clean up if image URL update fails? Difficult.
                    console.error("Error updating mod with image URLs:", updateError);
                    throw new Error("تم إنشاء المود، ولكن فشل ربط الصور.");
                }
                console.log("Mod updated with image URLs.");
            }

            alert("تم نشر المود بنجاح!");
            hidePublishForm();
            await fetchAllMods(); // Refresh the list
            await fetchStats(); // Refresh stats

        } catch (error) {
            console.error("Error publishing mod:", error);
            alert(`خطأ في نشر المود: ${error.message}`);
        }
    }

    async function handleEditSubmit(event) {
        event.preventDefault();
        const formData = new FormData(editModForm);
        const modData = Object.fromEntries(formData.entries());
        const modId = modData.id;
        const newModFile = editModFileInput.files[0]; // Get the new mod file if any
        // Use the array of files instead of the input files directly
        const newImageFiles = editNewFilesArray.length > 0 ? editNewFilesArray : null;

        if (!modId) {
            alert("خطأ: معرف المود مفقود.");
            return;
        }

        console.log("Saving changes for mod:", modId, modData);
        alert("جاري حفظ التغييرات... يرجى الانتظار للتأكيد.");

        try {
            let imageUrlsToUpdate = null; // null means don't change images
            let modDownloadUrlToUpdate = modData.download_url; // Start with URL from input

            // 1. Upload *new* Mod File if provided
            if (newModFile) {
                console.log("New mod file provided, uploading...");
                // TODO: Optionally delete the old mod file from storage first? Requires knowing the old path.
                modDownloadUrlToUpdate = await uploadModFile(newModFile, modData.name);
                if (!modDownloadUrlToUpdate) {
                    throw new Error("فشل تحميل ملف المود الجديد والحصول على الرابط.");
                }
                console.log("Using uploaded new mod file URL:", modDownloadUrlToUpdate);
            } else if (!modDownloadUrlToUpdate) {
                 // Ensure download URL is provided either via input or upload
                 throw new Error("رابط التحميل مطلوب. يرجى لصق رابط أو رفع ملف مود.");
            }


            // 2. Upload *new* Images if provided
            if (newImageFiles && newImageFiles.length > 0) {
                console.log("New images selected, uploading...");
                // TODO: Optionally delete old images from storage? Requires knowing old paths.
                imageUrlsToUpdate = await uploadImages(newImageFiles, modId);
                console.log("New image URLs:", imageUrlsToUpdate);
            }

            // 3. Prepare data for update
            const updatePayload = {
                name: modData.name,
                description: modData.description,
                category: modData.category,
                version: modData.version || null,
                size: modData.size || null,
                download_url: modDownloadUrlToUpdate, // Use the determined URL
            };

            // Only include image_urls in the update payload if new images were uploaded
            if (imageUrlsToUpdate !== null) {
                updatePayload.image_urls = imageUrlsToUpdate;
            }

            // 4. Execute the update
            const { error: updateError } = await supabaseClient
                .from('mods')
                .update(updatePayload)
                .eq('id', modId);

            if (updateError) throw updateError;

            // 5. Update the is_required status in the suggested_mods table
            const isRequired = editIsRequiredCheckbox.checked;

            // Check if the mod is already in the suggested_mods table
            const { data: existingSuggested, error: checkError } = await supabaseClient
                .from(SUGGESTED_MODS_TABLE)
                .select('id')
                .eq('mod_id', modId)
                .maybeSingle();

            if (checkError) {
                console.error("Error checking if mod is in suggested table:", checkError);
            } else {
                if (existingSuggested) {
                    // Update the existing record
                    const { error: updateSuggestedError } = await supabaseClient
                        .from(SUGGESTED_MODS_TABLE)
                        .update({ is_required: isRequired })
                        .eq('mod_id', modId);

                    if (updateSuggestedError) {
                        console.error("Error updating mod required status:", updateSuggestedError);
                    }
                } else if (isRequired) {
                    // If not in the table but checkbox is checked, add it
                    const { error: insertError } = await supabaseClient
                        .from(SUGGESTED_MODS_TABLE)
                        .insert({
                            mod_id: modId,
                            is_required: true,
                            display_order: 0 // Default order
                        });

                    if (insertError) {
                        console.error("Error adding mod to suggested table:", insertError);
                    }
                }
                // If not in table and checkbox is not checked, do nothing
            }

            alert("تم تحديث المود بنجاح!");
            hideEditForm();
            await fetchAllMods(); // Refresh list
            await fetchStats(); // Refresh stats
            loadCurrentSuggestedMods(); // Refresh suggested mods list

        } catch (error) {
            console.error("Error updating mod:", error);
            alert(`خطأ في تحديث المود: ${error.message}`);
        }
    }

    async function handleEditClick(event) {
        const modId = event.target.closest('button').dataset.id;
        const mod = allModsData.find(m => m.id === modId);
        if (mod) {
            await showEditForm(mod);
        } else {
            alert("تعذر العثور على بيانات المود للتعديل.");
        }
    }

    async function handleDeleteClick(event) {
        const modId = event.target.closest('button').dataset.id;
        const mod = allModsData.find(m => m.id === modId);

        if (!mod) {
            alert("تعذر العثور على بيانات المود للحذف.");
            return;
        }

        if (confirm(`هل أنت متأكد أنك تريد حذف المود "${mod.name || 'مود بدون اسم'}"؟ لا يمكن التراجع عن هذا الإجراء.`)) {
            console.log("Deleting mod:", modId);
            alert("جاري حذف المود وملفاته المرتبطة... يرجى الانتظار.");

            try {
                // 1. Identify files to delete from storage
                const filesToDelete = [];
                const imagePathsToDelete = [];

                // Get mod file path
                const modFilePath = getPathFromUrl(mod.download_url, modsBucketName);
                if (modFilePath) {
                    filesToDelete.push(modFilePath);
                    console.log(`Identified mod file to delete: ${modFilePath} from bucket ${modsBucketName}`);
                } else {
                     console.warn(`Could not determine mod file path from URL: ${mod.download_url}`);
                }

                // Get image file paths
                let currentImageUrls = [];
                 if (mod.image_urls) {
                     try {
                         if (Array.isArray(mod.image_urls)) {
                             currentImageUrls = mod.image_urls;
                         } else if (typeof mod.image_urls === 'string') {
                             currentImageUrls = JSON.parse(mod.image_urls);
                         }
                     } catch (e) {
                         console.error("Error parsing image URLs for deletion:", e);
                          // Handle case where it might be a single URL string
                         if (typeof mod.image_urls === 'string' && mod.image_urls.startsWith('http')) {
                             currentImageUrls = [mod.image_urls];
                         }
                     }
                 }

                if (Array.isArray(currentImageUrls)) {
                    currentImageUrls.forEach(url => {
                        const imgPath = getPathFromUrl(url, imageBucketName);
                        if (imgPath) {
                            imagePathsToDelete.push(imgPath);
                            console.log(`Identified image file to delete: ${imgPath} from bucket ${imageBucketName}`);
                        } else {
                             console.warn(`Could not determine image file path from URL: ${url}`);
                        }
                    });
                }

                // 2. Delete files from storage (run deletions in parallel)
                const deletePromises = [];

                if (filesToDelete.length > 0) {
                    console.log(`Attempting to delete ${filesToDelete.length} file(s) from bucket ${modsBucketName}...`);
                    deletePromises.push(
                        storageClient.storage
                            .from(modsBucketName)
                            .remove(filesToDelete)
                            .then(({ data, error }) => {
                                if (error) {
                                    console.error(`Error deleting mod file(s) from ${modsBucketName}:`, error);
                                    // Decide if this should stop the process or just log
                                    // alert(`حدث خطأ أثناء حذف ملف المود: ${error.message}`);
                                } else {
                                    console.log(`Successfully deleted mod file(s) from ${modsBucketName}:`, data);
                                }
                            })
                    );
                }

                 if (imagePathsToDelete.length > 0) {
                    console.log(`Attempting to delete ${imagePathsToDelete.length} image file(s) from bucket ${imageBucketName}...`);
                    deletePromises.push(
                        storageClient.storage
                            .from(imageBucketName)
                            .remove(imagePathsToDelete)
                             .then(({ data, error }) => {
                                if (error) {
                                    console.error(`Error deleting image file(s) from ${imageBucketName}:`, error);
                                    // Decide if this should stop the process or just log
                                    // alert(`حدث خطأ أثناء حذف بعض الصور: ${error.message}`);
                                } else {
                                    console.log(`Successfully deleted image file(s) from ${imageBucketName}:`, data);
                                }
                            })
                    );
                }

                // Wait for all storage deletions to attempt completion
                await Promise.allSettled(deletePromises);
                console.log("Storage deletion attempts finished.");

                // 3. Delete the mod record from the database
                console.log(`Deleting mod record ${modId} from database...`);
                const { error: deleteDbError } = await supabaseClient
                    .from('mods')
                    .delete()
                    .eq('id', modId);

                if (deleteDbError) {
                     console.error("Error deleting mod from database:", deleteDbError);
                     throw new Error(`فشل حذف سجل المود من قاعدة البيانات: ${deleteDbError.message}`);
                }

                console.log(`Successfully deleted mod record ${modId} from database.`);
                alert("تم حذف المود وجميع ملفاته المرتبطة بنجاح!");
                await fetchAllMods(); // Refresh list
                await fetchStats(); // Refresh stats
                hideEditForm(); // Hide edit form if it was open for this mod

            } catch (error) {
                console.error("Error deleting mod:", error);
                alert(`خطأ في حذف المود: ${error.message}`);
            }
        }
    }

    // --- Delete All Mods ---
    async function handleDeleteAllModsClick() {
        console.log("Delete All Mods button clicked.");

        // Double confirmation - very important!
        const confirmation1 = prompt("تحذير شديد! أنت على وشك حذف **جميع** المودات وجميع ملفاتها المرتبطة (الصور وملفات المودات) بشكل دائم. لا يمكن التراجع عن هذا الإجراء. للمتابعة، اكتب 'DELETE ALL' في المربع أدناه:");
        if (confirmation1 !== "DELETE ALL") {
            alert("تم إلغاء عملية الحذف الجماعي.");
            return;
        }

        const confirmation2 = confirm("هل أنت متأكد تمامًا من رغبتك في حذف جميع المودات؟ هذه هي الفرصة الأخيرة للتراجع.");
        if (!confirmation2) {
            alert("تم إلغاء عملية الحذف الجماعي.");
            return;
        }

        alert("جاري حذف جميع المودات وملفاتها... قد تستغرق هذه العملية بعض الوقت. يرجى الانتظار.");
        console.log("Proceeding with deleting all mods...");

        try {
            // Ensure we have the latest mod data (or use existing if confident)
            // Let's re-fetch to be safe, although allModsData should be up-to-date
            const { data: modsToDelete, error: fetchError } = await supabaseClient
                .from('mods')
                .select('id, download_url, image_urls'); // Select only needed fields

            if (fetchError) {
                throw new Error(`فشل في جلب قائمة المودات للحذف: ${fetchError.message}`);
            }

            if (!modsToDelete || modsToDelete.length === 0) {
                alert("لا توجد مودات لحذفها.");
                return;
            }

            console.log(`Found ${modsToDelete.length} mods to delete.`);

            // 1. Collect all file paths to delete
            const allModFilePaths = [];
            const allImageFilePaths = [];

            modsToDelete.forEach(mod => {
                // Collect mod file path
                const modFilePath = getPathFromUrl(mod.download_url, modsBucketName);
                if (modFilePath) {
                    allModFilePaths.push(modFilePath);
                } else if (mod.download_url) {
                     console.warn(`Could not determine mod file path from URL: ${mod.download_url} for mod ID ${mod.id}`);
                }

                // Collect image file paths
                let currentImageUrls = [];
                 if (mod.image_urls) {
                     try {
                         if (Array.isArray(mod.image_urls)) {
                             currentImageUrls = mod.image_urls;
                         } else if (typeof mod.image_urls === 'string') {
                             currentImageUrls = JSON.parse(mod.image_urls);
                         }
                     } catch (e) {
                          if (typeof mod.image_urls === 'string' && mod.image_urls.startsWith('http')) {
                             currentImageUrls = [mod.image_urls];
                         } else {
                            console.error(`Error parsing image URLs for deletion for mod ID ${mod.id}:`, e);
                         }
                     }
                 }
                 if (Array.isArray(currentImageUrls)) {
                    currentImageUrls.forEach(url => {
                        const imgPath = getPathFromUrl(url, imageBucketName);
                        if (imgPath) {
                            allImageFilePaths.push(imgPath);
                        } else if (url) {
                             console.warn(`Could not determine image file path from URL: ${url} for mod ID ${mod.id}`);
                        }
                    });
                }
            });

            console.log(`Total mod files to attempt delete: ${allModFilePaths.length}`);
            console.log(`Total image files to attempt delete: ${allImageFilePaths.length}`);

            // 2. Attempt to delete all files from storage
            const deleteStoragePromises = [];

            // Batch delete mod files (Supabase client handles batching internally if needed)
            if (allModFilePaths.length > 0) {
                 console.log(`Attempting to delete ${allModFilePaths.length} file(s) from bucket ${modsBucketName}...`);
                 deleteStoragePromises.push(
                     storageClient.storage
                         .from(modsBucketName)
                         .remove(allModFilePaths)
                         .then(({ data, error }) => {
                             if (error) {
                                 console.error(`Error deleting batch of mod files from ${modsBucketName}:`, error);
                                 // Log error but continue to DB deletion attempt
                             } else {
                                 console.log(`Successfully deleted batch of mod files from ${modsBucketName}:`, data);
                             }
                         })
                 );
            }

             // Batch delete image files
             if (allImageFilePaths.length > 0) {
                 console.log(`Attempting to delete ${allImageFilePaths.length} image file(s) from bucket ${imageBucketName}...`);
                 deleteStoragePromises.push(
                     storageClient.storage
                         .from(imageBucketName)
                         .remove(allImageFilePaths)
                          .then(({ data, error }) => {
                             if (error) {
                                 console.error(`Error deleting batch of image files from ${imageBucketName}:`, error);
                                  // Log error but continue to DB deletion attempt
                             } else {
                                 console.log(`Successfully deleted batch of image files from ${imageBucketName}:`, data);
                             }
                         })
                 );
             }

            // Wait for all storage deletions to attempt completion
            await Promise.allSettled(deleteStoragePromises);
            console.log("All storage deletion attempts finished.");


            // 3. Delete ALL records from the mods table
            // WARNING: This deletes everything in the table!
            console.log("Attempting to delete all records from the 'mods' database table...");
            const { error: deleteAllDbError } = await supabaseClient
                .from('mods')
                .delete()
                .neq('id', '00000000-0000-0000-0000-000000000000'); // Hack to delete all rows, adjust if needed or use RPC

            if (deleteAllDbError) {
                console.error("Error deleting all mods from database:", deleteAllDbError);
                // Even if DB delete fails, files might have been deleted. State is inconsistent.
                throw new Error(`فشل حذف سجلات المودات من قاعدة البيانات: ${deleteAllDbError.message}. قد تكون بعض الملفات قد حُذفت بالفعل.`);
            }

            console.log("Successfully deleted all mod records from the database.");
            alert("تم حذف جميع المودات وجميع ملفاتها المرتبطة بنجاح!");

            // 4. Refresh UI
            await fetchAllMods(); // Refresh list (should be empty)
            await fetchStats();   // Refresh stats
            hideEditForm();     // Ensure edit form is hidden
            hidePublishForm();  // Ensure publish form is hidden

        } catch (error) {
            console.error("Error during delete all mods process:", error);
            alert(`حدث خطأ فادح أثناء حذف جميع المودات: ${error.message}`);
            // Refresh the list anyway to show the current state
            await fetchAllMods();
            await fetchStats();
        }
    }


    // --- Event Listeners ---
    showPublishFormBtn.addEventListener('click', showPublishForm);
    cancelPublishBtn.addEventListener('click', hidePublishForm);
    cancelEditBtn.addEventListener('click', hideEditForm);
    deleteModBtn.addEventListener('click', handleDeleteClick); // Listener for single delete in edit form
    deleteAllModsBtn.addEventListener('click', handleDeleteAllModsClick); // Added listener for the new button

    publishModForm.addEventListener('submit', handlePublishSubmit);
    editModForm.addEventListener('submit', handleEditSubmit);

    searchInput.addEventListener('input', filterAndDisplayMods);
    categoryFilter.addEventListener('change', filterAndDisplayMods);

    // Image preview listeners
    publishImageInput.addEventListener('change', () => displayStandardImagePreviews(publishImageInput, publishImagePreviews)); // Use standard preview
    publishOtherImageInput.addEventListener('change', (event) => {
        // Add newly selected files to the array
        const newFiles = Array.from(event.target.files);
        otherFilesArray.push(...newFiles);
        // Display previews from the updated array
        displayImagePreviews(otherFilesArray, publishOtherImagePreviews, otherFilesArray);
        // Clear the input value so the user can select more files with the same button
        event.target.value = '';
        console.log("Added files to otherFilesArray. Current count:", otherFilesArray.length);
    });
    publishExtraImageInput.addEventListener('change', (event) => {
        // Add newly selected files to the array
        const newFiles = Array.from(event.target.files);
        extraFilesArray.push(...newFiles);
        // Display previews from the updated array
        displayImagePreviews(extraFilesArray, publishExtraImagePreviews, extraFilesArray);
        // Clear the input value so the user can select more files with the same button
        event.target.value = '';
        console.log("Added files to extraFilesArray. Current count:", extraFilesArray.length);
    });
    editNewImageInput.addEventListener('change', (event) => {
        // Add newly selected files to the array
        const newFiles = Array.from(event.target.files);
        editNewFilesArray.push(...newFiles);
        // Display previews from the updated array
        displayImagePreviews(editNewFilesArray, editNewImagePreviews, editNewFilesArray);
        // Clear the input value so the user can select more files with the same button
        event.target.value = '';
        console.log("Added files to editNewFilesArray. Current count:", editNewFilesArray.length);
    });


    // --- User Activity Statistics Fetching ---
    async function fetchUserActivityStats() {
        if (!totalUsersCountEl || !activeUsersCountEl || !inactiveUsersCountEl) {
            console.warn("User activity stat elements not found. Skipping fetch.");
            return;
        }
        totalUsersCountEl.textContent = 'جاري التحميل...';
        activeUsersCountEl.textContent = 'جاري التحميل...';
        inactiveUsersCountEl.textContent = 'جاري التحميل...';

        try {
            // Fetch all users to get the total count and last_sign_in_at
            // Note: This requires appropriate RLS policies on auth.users or a privileged client.
            // For simplicity, we'll assume the client has rights or you'll use an RPC.
            // A more secure way for client-side admin panels is often to use an RPC function
            // that performs these counts on the server-side.

            const { data: users, error: usersError, count } = await supabaseClient
                .from('users') // This should be 'auth.users' but direct query might be restricted.
                               // If 'users' is a public table mirroring auth.users, this is fine.
                               // Otherwise, an RPC function is better.
                .select('user_id, last_seen_at', { count: 'exact' }); // Corrected column names

            if (usersError) {
                // Attempt to query auth.users if 'users' fails and it's a common Supabase setup
                if (usersError.message.includes("relation \"users\" does not exist")) {
                    console.warn("Table 'users' not found, trying 'auth.users' via RPC (get_user_activity_stats)...");
                    // This RPC function would need to be created in Supabase SQL Editor:
                    // CREATE OR REPLACE FUNCTION get_user_activity_stats()
                    // RETURNS TABLE(total_users BIGINT, active_users BIGINT) AS $$
                    // BEGIN
                    //   RETURN QUERY
                    //   SELECT
                    //     (SELECT count(*) FROM auth.users) AS total_users,
                    //     (SELECT count(*) FROM auth.users WHERE last_sign_in_at >= (NOW() - INTERVAL '30 days')) AS active_users;
                    // END;
                    // $$ LANGUAGE plpgsql SECURITY DEFINER;
                    // GRANT EXECUTE ON FUNCTION get_user_activity_stats() TO authenticated; // or service_role if called from secure context

                    const { data: rpcData, error: rpcError } = await supabaseClient.rpc('get_user_activity_stats');
                    if (rpcError) {
                        console.error("Error fetching user activity stats via RPC:", rpcError);
                        showError(totalUsersCountEl.parentElement.parentElement, `خطأ RPC: ${rpcError.message}`);
                        totalUsersCountEl.textContent = 'خطأ';
                        activeUsersCountEl.textContent = 'خطأ';
                        inactiveUsersCountEl.textContent = 'خطأ';
                        return;
                    }
                    if (rpcData && rpcData.length > 0) {
                        const stats = rpcData[0];
                        totalUsersCountEl.textContent = formatCount(stats.total_users);
                        activeUsersCountEl.textContent = formatCount(stats.active_users);
                        inactiveUsersCountEl.textContent = formatCount(stats.total_users - stats.active_users);
                    } else {
                         totalUsersCountEl.textContent = 'لا بيانات';
                         activeUsersCountEl.textContent = 'لا بيانات';
                         inactiveUsersCountEl.textContent = 'لا بيانات';
                    }
                    return; // Exit after RPC attempt
                }
                throw usersError; // Re-throw original error if not 'relation does not exist'
            }

            const totalUsers = count || 0;
            totalUsersCountEl.textContent = formatCount(totalUsers);

            if (users && users.length > 0) {
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

                const activeUsers = users.filter(user => {
                    // Use the corrected column name 'last_seen_at' here
                    return user.last_seen_at && new Date(user.last_seen_at) > thirtyDaysAgo;
                }).length;

                activeUsersCountEl.textContent = formatCount(activeUsers);
                inactiveUsersCountEl.textContent = formatCount(totalUsers - activeUsers);
            } else {
                activeUsersCountEl.textContent = '0';
                inactiveUsersCountEl.textContent = formatCount(totalUsers);
            }

        } catch (error) {
            console.error("Error fetching user activity stats:", error);
            showError(totalUsersCountEl.parentElement.parentElement, `خطأ في تحميل إحصائيات المستخدمين: ${error.message}`);
            totalUsersCountEl.textContent = 'خطأ';
            activeUsersCountEl.textContent = 'خطأ';
            inactiveUsersCountEl.textContent = 'خطأ';
        }
    }

    // --- User Preferences Fetching ---
    async function fetchUserPreferences() {
        if (!popularCategoriesListEl) {
            console.warn("Popular categories list element not found. Skipping fetch.");
            return;
        }
        showLoading(popularCategoriesListEl);

        try {
            // Fetch all likes and the category of the liked mod
            // This requires a join between user_likes and mods
            const { data: likedModsData, error: likedModsError } = await supabaseClient
                .from('user_likes')
                .select(`
                    mods ( category )
                `); // Fetches { mods: { category: '...' } } for each like

            if (likedModsError) {
                 // Check for specific errors like table/column not found or RLS issues
                if (likedModsError.message.includes("Could not find a relationship")) {
                    showError(popularCategoriesListEl, 'خطأ: لم يتم العثور على علاقة بين user_likes و mods. هل تم تعريف المفتاح الخارجي؟');
                } else if (likedModsError.message.includes("permission denied")) {
                    showError(popularCategoriesListEl, 'خطأ: تم رفض الإذن للوصول إلى تفضيلات المستخدم.');
                } else {
                    throw likedModsError;
                }
                return;
            }

            if (!likedModsData || likedModsData.length === 0) {
                popularCategoriesListEl.innerHTML = '<li>لا توجد بيانات إعجابات لتحليل التفضيلات.</li>';
                return;
            }

            const categoryCounts = {};
            likedModsData.forEach(like => {
                if (like.mods && like.mods.category) {
                    const category = like.mods.category;
                    categoryCounts[category] = (categoryCounts[category] || 0) + 1;
                }
            });

            const sortedCategories = Object.entries(categoryCounts)
                .sort(([, a], [, b]) => b - a) // Sort by count descending
                .slice(0, 5); // Take top 5 or fewer

            if (sortedCategories.length === 0) {
                popularCategoriesListEl.innerHTML = '<li>لم يتم العثور على تفضيلات فئات.</li>';
                return;
            }

            popularCategoriesListEl.innerHTML = sortedCategories.map(([category, count]) =>
                `<li>${category} (${formatCount(count)} إعجاب)</li>`
            ).join('');

        } catch (error) {
            console.error("Error fetching user preferences:", error);
            showError(popularCategoriesListEl, `خطأ في تحميل تفضيلات المستخدمين: ${error.message}`);
        }
    }


    // --- Initial Load ---
    // fetchUsageLimits(); // Removed as the section is gone
    fetchStats();       // Fetch general stats
    fetchAllMods();     // Fetch all mods for management table
    fetchRecentLikes(); // Fetch recent likes activity
    fetchUserActivityStats(); // Fetch user activity stats
    fetchUserPreferences(); // Fetch user preferences

    // --- Event listener for social icon select ---
    if (drawerLinkSocialIconSelect && drawerLinkIconInput) {
        drawerLinkSocialIconSelect.addEventListener('change', (event) => {
            const selectedValue = event.target.value;
            if (selectedValue === "NO_ICON") {
                drawerLinkIconInput.value = ''; // Clear manual input if "No Icon" is selected
            } else if (selectedValue) {
                drawerLinkIconInput.value = selectedValue; // Set manual input to selected Font Awesome class
            } else {
                drawerLinkIconInput.value = ''; // Clear if "-- اختر أيقونة --" is selected
            }
        });
    }
    // --- End Event listener for social icon select ---

    // --- Drawer Links Management Functions ---

    async function fetchDrawerLinks() {
        if (!drawerLinksTableBody) return;
        drawerLinksTableBody.innerHTML = '<tr><td colspan="6">جاري تحميل الروابط...</td></tr>';
        try {
            // NOTE: If you encounter 401 Unauthorized errors here,
            // check your Supabase RLS (Row Level Security) policies for the 'drawer_links' table.
            // The anonymous key might not have permission to access or modify this table.
            // You may need to adjust RLS to allow anon access (if appropriate for your security model)
            // or implement admin authentication for the dashboard.
            const { data, error } = await supabaseClient
                .from(DRAWER_LINKS_TABLE)
                .select('*')
                .order('order', { ascending: true });

            if (error) throw error;
            displayDrawerLinksTable(data || []);
        } catch (error) {
            console.error("Error fetching drawer links:", error);
            showError(drawerLinksTableBody, `خطأ في تحميل روابط القائمة: ${error.message}`);
        }
    }

    function displayDrawerLinksTable(links) {
        if (!drawerLinksTableBody) return;
        if (links.length === 0) {
            drawerLinksTableBody.innerHTML = '<tr><td colspan="6">لا توجد روابط حالياً.</td></tr>';
            return;
        }

        drawerLinksTableBody.innerHTML = links.map(link => `
            <tr data-id="${link.id}">
                <td>${link.text || 'N/A'}</td>
                <td><a href="${link.url}" target="_blank">${link.url || 'N/A'}</a></td>
                <td>${link.icon_class ? `<i class="${link.icon_class}"></i> (${link.icon_class})` : 'لا يوجد'}</td>
                <td>${link.order !== null ? link.order : 'N/A'}</td>
                <td>${link.is_active ? 'نعم' : 'لا'}</td>
                <td class="action-cell">
                    <button class="edit-drawer-link-button" data-id="${link.id}"><i class="fas fa-edit"></i> تعديل</button>
                    <button class="delete-drawer-link-button" data-id="${link.id}" data-text="${link.text}"><i class="fas fa-trash"></i> حذف</button>
                </td>
            </tr>
        `).join('');

        addDrawerLinkButtonListeners();
    }

    function addDrawerLinkButtonListeners() {
        document.querySelectorAll('.edit-drawer-link-button').forEach(button => {
            button.addEventListener('click', handleEditDrawerLinkClick);
        });
        document.querySelectorAll('.delete-drawer-link-button').forEach(button => {
            button.addEventListener('click', handleDeleteDrawerLinkClick);
        });
    }

    function handleEditDrawerLinkClick(event) {
        const linkId = event.target.closest('button').dataset.id;
        // Fetch the full link data again to ensure we have the latest
        supabaseClient.from(DRAWER_LINKS_TABLE).select('*').eq('id', linkId).single()
            .then(({ data: link, error }) => {
                if (error || !link) {
                    alert("تعذر العثور على بيانات الرابط للتعديل.");
                    console.error("Error fetching link for edit:", error);
                    return;
                }
                drawerLinkIdInput.value = link.id;
                drawerLinkTextInput.value = link.text || '';
                drawerLinkUrlInput.value = link.url || '';
                drawerLinkIconInput.value = link.icon_class || '';
                drawerLinkOrderInput.value = link.order !== null ? link.order : 0;
                drawerLinkActiveCheckbox.checked = link.is_active !== null ? link.is_active : true;

                drawerLinkForm.querySelector('button[type="submit"]').textContent = 'حفظ التعديلات';
                cancelDrawerLinkEditBtn.style.display = 'inline-block';
                drawerLinkTextInput.focus();
            });
    }

    async function handleDeleteDrawerLinkClick(event) {
        const linkId = event.target.closest('button').dataset.id;
        const linkText = event.target.closest('button').dataset.text;

        if (confirm(`هل أنت متأكد أنك تريد حذف الرابط "${linkText || 'هذا الرابط'}"؟`)) {
            try {
                const { error } = await supabaseClient
                    .from(DRAWER_LINKS_TABLE)
                    .delete()
                    .eq('id', linkId);

                if (error) throw error;
                alert("تم حذف الرابط بنجاح.");
                fetchDrawerLinks(); // Refresh the list
            } catch (error) {
                console.error("Error deleting drawer link:", error);
                alert(`خطأ في حذف الرابط: ${error.message}`);
            }
        }
    }

    async function handleDrawerLinkFormSubmit(event) {
        event.preventDefault();
        const linkId = drawerLinkIdInput.value;
        // NOTE: If you encounter 401 Unauthorized errors on save,
        // check your Supabase RLS (Row Level Security) policies for the 'drawer_links' table.
        // The anonymous key might not have permission to insert/update this table.
        const linkData = {
            text: drawerLinkTextInput.value,
            url: drawerLinkUrlInput.value,
            icon_class: drawerLinkIconInput.value || null,
            order: parseInt(drawerLinkOrderInput.value, 10) || 0,
            is_active: drawerLinkActiveCheckbox.checked
        };

        try {
            let response;
            if (linkId) { // Update existing link
                response = await supabaseClient
                    .from(DRAWER_LINKS_TABLE)
                    .update(linkData)
                    .eq('id', linkId);
            } else { // Insert new link
                response = await supabaseClient
                    .from(DRAWER_LINKS_TABLE)
                    .insert(linkData);
            }

            const { error } = response;
            if (error) throw error;

            alert(linkId ? "تم تحديث الرابط بنجاح." : "تمت إضافة الرابط بنجاح.");
            resetDrawerLinkForm();
            fetchDrawerLinks(); // Refresh the list
        } catch (error) {
            console.error("Error saving drawer link:", error);
            alert(`خطأ في حفظ الرابط: ${error.message}`);
        }
    }

    function resetDrawerLinkForm() {
        drawerLinkForm.reset();
        drawerLinkIdInput.value = ''; // Ensure hidden ID is cleared
        drawerLinkActiveCheckbox.checked = true; // Default to active
        drawerLinkForm.querySelector('button[type="submit"]').textContent = 'حفظ الرابط';
        cancelDrawerLinkEditBtn.style.display = 'none';
    }

    if (drawerLinkForm) {
        drawerLinkForm.addEventListener('submit', handleDrawerLinkFormSubmit);
    }
    if (cancelDrawerLinkEditBtn) {
        cancelDrawerLinkEditBtn.addEventListener('click', resetDrawerLinkForm);
    }

    // --- End Drawer Links Management Functions ---

    // --- Categories Management Functions ---

    async function fetchCategories() {
        if (!categoriesTableBody) return;
        categoriesTableBody.innerHTML = '<tr><td colspan="3">جاري تحميل الأقسام...</td></tr>';
        try {
            const { data, error } = await supabaseClient
                .from(CATEGORIES_TABLE)
                .select('*')
                .order('name', { ascending: true });

            if (error) throw error;
            displayCategoriesTable(data || []);
        } catch (error) {
            console.error("Error fetching categories:", error);
            showError(categoriesTableBody, `خطأ في تحميل الأقسام: ${error.message}`);
        }
    }

    function displayCategoriesTable(categories) {
        if (!categoriesTableBody) return;
        if (categories.length === 0) {
            categoriesTableBody.innerHTML = '<tr><td colspan="3">لا توجد أقسام حالياً.</td></tr>';
            return;
        }

        categoriesTableBody.innerHTML = categories.map(category => `
            <tr data-id="${category.id}">
                <td>${category.name || 'N/A'}</td>
                <td>${category.icon_class ? `<i class="${category.icon_class}"></i> (${category.icon_class})` : 'لا يوجد'}</td>
                <td class="action-cell">
                    <button class="edit-category-button" data-id="${category.id}"><i class="fas fa-edit"></i> تعديل</button>
                    <button class="delete-category-button" data-id="${category.id}" data-name="${category.name}"><i class="fas fa-trash"></i> حذف</button>
                    <button class="manage-category-mods-button" data-id="${category.id}" data-name="${category.name}"><i class="fas fa-link"></i> إدارة المودات</button>
                </td>
            </tr>
        `).join('');

        addCategoryButtonListeners();
    }

    function addCategoryButtonListeners() {
        document.querySelectorAll('.edit-category-button').forEach(button => {
            button.addEventListener('click', handleEditCategoryClick);
        });
        document.querySelectorAll('.delete-category-button').forEach(button => {
            button.addEventListener('click', handleDeleteCategoryClick);
        });
        document.querySelectorAll('.manage-category-mods-button').forEach(button => {
            button.addEventListener('click', handleManageCategoryModsClick);
        });
    }

    function handleEditCategoryClick(event) {
        const categoryId = event.target.closest('button').dataset.id;
        // Fetch the full category data to ensure we have the latest
        supabaseClient.from(CATEGORIES_TABLE).select('*').eq('id', categoryId).single()
            .then(({ data: category, error }) => {
                if (error || !category) {
                    alert("تعذر العثور على بيانات القسم للتعديل.");
                    console.error("Error fetching category for edit:", error);
                    return;
                }
                categoryIdInput.value = category.id;
                categoryNameInput.value = category.name || '';
                categoryIconInput.value = category.icon_class || '';

                categoryForm.querySelector('button[type="submit"]').textContent = 'حفظ التعديلات';
                cancelCategoryEditBtn.style.display = 'inline-block';
                categoryNameInput.focus();
            });
    }

    async function handleDeleteCategoryClick(event) {
        const categoryId = event.target.closest('button').dataset.id;
        const categoryName = event.target.closest('button').dataset.name;

        if (confirm(`هل أنت متأكد أنك تريد حذف القسم "${categoryName || 'هذا القسم'}"؟`)) {
            try {
                // First, delete all category-mod relationships for this category
                const { error: relError } = await supabaseClient
                    .from(CATEGORY_MODS_TABLE)
                    .delete()
                    .eq('category_id', categoryId);

                if (relError) {
                    console.error("Error deleting category-mod relationships:", relError);
                    // Continue with category deletion even if relationship deletion fails
                }

                // Then delete the category itself
                const { error } = await supabaseClient
                    .from(CATEGORIES_TABLE)
                    .delete()
                    .eq('id', categoryId);

                if (error) throw error;
                alert("تم حذف القسم بنجاح.");
                fetchCategories(); // Refresh the list
            } catch (error) {
                console.error("Error deleting category:", error);
                alert(`خطأ في حذف القسم: ${error.message}`);
            }
        }
    }

    async function handleCategoryFormSubmit(event) {
        event.preventDefault();
        const categoryId = categoryIdInput.value;
        const categoryData = {
            name: categoryNameInput.value,
            icon_class: categoryIconInput.value || null
        };

        try {
            let response;
            if (categoryId) { // Update existing category
                response = await supabaseClient
                    .from(CATEGORIES_TABLE)
                    .update(categoryData)
                    .eq('id', categoryId);
            } else { // Insert new category
                response = await supabaseClient
                    .from(CATEGORIES_TABLE)
                    .insert(categoryData);
            }

            const { error } = response;
            if (error) throw error;

            alert(categoryId ? "تم تحديث القسم بنجاح." : "تمت إضافة القسم بنجاح.");
            resetCategoryForm();
            fetchCategories(); // Refresh the list
        } catch (error) {
            console.error("Error saving category:", error);
            alert(`خطأ في حفظ القسم: ${error.message}`);
        }
    }

    function resetCategoryForm() {
        categoryForm.reset();
        categoryIdInput.value = ''; // Ensure hidden ID is cleared
        categoryForm.querySelector('button[type="submit"]').textContent = 'حفظ القسم';
        cancelCategoryEditBtn.style.display = 'none';
    }

    // --- Category-Mods Management Functions ---

    async function handleManageCategoryModsClick(event) {
        const categoryId = event.target.closest('button').dataset.id;
        const categoryName = event.target.closest('button').dataset.name;

        // Create a modal dialog for managing mods in this category
        const modalContainer = document.createElement('div');
        modalContainer.className = 'modal-container';
        modalContainer.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>إدارة مودات القسم: ${categoryName}</h3>
                    <button class="close-modal-button">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="search-container">
                        <input type="text" id="search-mods-for-category" placeholder="ابحث عن مود لإضافته للقسم...">
                    </div>
                    <div class="mods-container">
                        <div class="available-mods-section">
                            <h4>المودات المتاحة</h4>
                            <div id="available-mods-list" class="mods-list">
                                <p>جاري التحميل...</p>
                            </div>
                        </div>
                        <div class="category-mods-section">
                            <h4>مودات القسم الحالية</h4>
                            <div id="category-mods-list" class="mods-list">
                                <p>جاري التحميل...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modalContainer);

        // Add event listener to close button
        const closeButton = modalContainer.querySelector('.close-modal-button');
        closeButton.addEventListener('click', () => {
            document.body.removeChild(modalContainer);
        });

        // Add event listener to search input
        const searchInput = modalContainer.querySelector('#search-mods-for-category');
        searchInput.addEventListener('input', () => {
            filterModsForCategory(searchInput.value.toLowerCase(), categoryId, modalContainer);
        });

        // Load mods for this category
        loadModsForCategory(categoryId, modalContainer);
    }

    async function loadModsForCategory(categoryId, modalContainer) {
        const availableModsList = modalContainer.querySelector('#available-mods-list');
        const categoryModsList = modalContainer.querySelector('#category-mods-list');

        availableModsList.innerHTML = '<p>جاري التحميل...</p>';
        categoryModsList.innerHTML = '<p>جاري التحميل...</p>';

        try {
            // Get all mods
            const { data: allMods, error: modsError } = await supabaseClient
                .from('mods')
                .select('id, name, category');

            if (modsError) throw modsError;

            // Get mods already in this category
            const { data: categoryMods, error: catModsError } = await supabaseClient
                .from(CATEGORY_MODS_TABLE)
                .select('mod_id')
                .eq('category_id', categoryId);

            if (catModsError) throw catModsError;

            const categoryModIds = new Set(categoryMods.map(item => item.mod_id));

            // Filter mods into available and already in category
            const availableMods = allMods.filter(mod => !categoryModIds.has(mod.id));
            const modsInCategory = allMods.filter(mod => categoryModIds.has(mod.id));

            displayAvailableMods(availableMods, categoryId, availableModsList);
            displayCategoryMods(modsInCategory, categoryId, categoryModsList);

        } catch (error) {
            console.error("Error loading mods for category:", error);
            availableModsList.innerHTML = `<p class="error-message">خطأ في تحميل المودات: ${error.message}</p>`;
            categoryModsList.innerHTML = `<p class="error-message">خطأ في تحميل المودات: ${error.message}</p>`;
        }
    }

    function displayAvailableMods(mods, categoryId, container) {
        if (mods.length === 0) {
            container.innerHTML = '<p>لا توجد مودات متاحة للإضافة.</p>';
            return;
        }

        container.innerHTML = mods.map(mod => `
            <div class="mod-item" data-id="${mod.id}">
                <span>${mod.name} (${mod.category || 'بدون فئة'})</span>
                <button class="add-to-category-button" data-mod-id="${mod.id}" data-category-id="${categoryId}">
                    <i class="fas fa-plus-circle"></i> إضافة
                </button>
            </div>
        `).join('');

        // Add event listeners to add buttons
        container.querySelectorAll('.add-to-category-button').forEach(button => {
            button.addEventListener('click', handleAddModToCategory);
        });
    }

    function displayCategoryMods(mods, categoryId, container) {
        if (mods.length === 0) {
            container.innerHTML = '<p>لا توجد مودات في هذا القسم حالياً.</p>';
            return;
        }

        container.innerHTML = mods.map(mod => `
            <div class="mod-item" data-id="${mod.id}">
                <span>${mod.name} (${mod.category || 'بدون فئة'})</span>
                <button class="remove-from-category-button" data-mod-id="${mod.id}" data-category-id="${categoryId}">
                    <i class="fas fa-minus-circle"></i> إزالة
                </button>
            </div>
        `).join('');

        // Add event listeners to remove buttons
        container.querySelectorAll('.remove-from-category-button').forEach(button => {
            button.addEventListener('click', handleRemoveModFromCategory);
        });
    }

    function filterModsForCategory(searchTerm, categoryId, modalContainer) {
        const availableModsList = modalContainer.querySelector('#available-mods-list');
        const categoryModsList = modalContainer.querySelector('#category-mods-list');

        // Filter available mods
        const availableModItems = availableModsList.querySelectorAll('.mod-item');
        availableModItems.forEach(item => {
            const modName = item.querySelector('span').textContent.toLowerCase();
            if (modName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });

        // Filter category mods
        const categoryModItems = categoryModsList.querySelectorAll('.mod-item');
        categoryModItems.forEach(item => {
            const modName = item.querySelector('span').textContent.toLowerCase();
            if (modName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    async function handleAddModToCategory(event) {
        const button = event.target.closest('button');
        const modId = button.dataset.modId;
        const categoryId = button.dataset.categoryId;

        try {
            const { error } = await supabaseClient
                .from(CATEGORY_MODS_TABLE)
                .insert({ category_id: categoryId, mod_id: modId });

            if (error) throw error;

            // Refresh the lists
            const modalContainer = button.closest('.modal-container');
            loadModsForCategory(categoryId, modalContainer);

        } catch (error) {
            console.error("Error adding mod to category:", error);
            alert(`خطأ في إضافة المود للقسم: ${error.message}`);
        }
    }

    async function handleRemoveModFromCategory(event) {
        const button = event.target.closest('button');
        const modId = button.dataset.modId;
        const categoryId = button.dataset.categoryId;

        try {
            const { error } = await supabaseClient
                .from(CATEGORY_MODS_TABLE)
                .delete()
                .match({ category_id: categoryId, mod_id: modId });

            if (error) throw error;

            // Refresh the lists
            const modalContainer = button.closest('.modal-container');
            loadModsForCategory(categoryId, modalContainer);

        } catch (error) {
            console.error("Error removing mod from category:", error);
            alert(`خطأ في إزالة المود من القسم: ${error.message}`);
        }
    }

    // --- Suggested Mods Management Functions ---

    async function loadAvailableModsForSuggestion() {
        if (!availableModsForSuggestionBody) return;
        availableModsForSuggestionBody.innerHTML = '<tr><td colspan="3">جاري تحميل المودات المتاحة...</td></tr>';

        // Use existing allModsData if already fetched by fetchAllMods, otherwise fetch them.
        // For simplicity, we assume fetchAllMods has populated allModsData.
        // If not, this function might need its own fetch or ensure fetchAllMods is called first.
        // We'll rely on the existing searchInput and categoryFilter for the main mod list for now,
        // and use the new ones for this specific list.

        const searchTerm = searchAvailableSuggestInput.value.toLowerCase();
        const selectedCategory = categoryFilterAvailableSuggest.value;

        const filteredMods = allModsData.filter(mod => {
            const nameMatch = mod.name?.toLowerCase().includes(searchTerm) ?? true;
            const categoryMatch = selectedCategory === 'All' || mod.category === selectedCategory;
            return nameMatch && categoryMatch;
        });

        if (filteredMods.length === 0) {
            availableModsForSuggestionBody.innerHTML = '<tr><td colspan="3">لم يتم العثور على مودات.</td></tr>';
            return;
        }

        // Fetch current suggested mod IDs to disable "Add" button if already suggested
        const { data: suggestedModEntries, error: suggestedError } = await supabaseClient
            .from(SUGGESTED_MODS_TABLE)
            .select('mod_id');

        const suggestedModIds = suggestedError ? [] : (suggestedModEntries || []).map(e => e.mod_id);

        availableModsForSuggestionBody.innerHTML = filteredMods.map(mod => {
            const isAlreadySuggested = suggestedModIds.includes(mod.id);
            return `
                <tr data-id="${mod.id}">
                    <td>${mod.name || 'N/A'}</td>
                    <td>${mod.category || 'N/A'}</td>
                    <td class="action-cell">
                        <button class="add-to-suggested-button" data-id="${mod.id}" ${isAlreadySuggested ? 'disabled title="المود مقترح بالفعل"' : 'title="إضافة كمقترح"'}>
                            <i class="fas fa-plus-circle"></i> ${isAlreadySuggested ? 'مقترح' : 'إضافة كمقترح'}
                        </button>
                    </td>
                </tr>
            `;
        }).join('');

        document.querySelectorAll('.add-to-suggested-button').forEach(button => {
            button.addEventListener('click', handleAddToSuggestedClick);
        });
    }

    async function loadCurrentSuggestedMods() {
        if (!currentSuggestedModsBody) return;
        currentSuggestedModsBody.innerHTML = '<tr><td colspan="5">جاري تحميل المودات المقترحة...</td></tr>';

        try {
            // Use safe query if supabase manager is available
            let result;
            if (typeof supabaseManager !== 'undefined') {
                result = await supabaseManager.safeQuery(
                    SUGGESTED_MODS_TABLE,
                    supabaseClient
                        .from(SUGGESTED_MODS_TABLE)
                        .select('id, mod_id, display_order, is_required')
                        .order('display_order', { ascending: true }),
                    [] // fallback to empty array
                );
            } else {
                result = await supabaseClient
                    .from(SUGGESTED_MODS_TABLE)
                    .select('id, mod_id, display_order, is_required')
                    .order('display_order', { ascending: true });
            }

            const { data: suggestedEntries, error: suggestedError } = result;

            if (suggestedError) {
                // Check if it's a table not found error
                if (suggestedError.code === 'PGRST116' || suggestedError.message.includes('does not exist')) {
                    currentSuggestedModsBody.innerHTML = '<tr><td colspan="5">جدول المودات المقترحة غير موجود. سيتم إنشاؤه تلقائياً عند إضافة أول مود مقترح.</td></tr>';
                    return;
                }
                throw suggestedError;
            }

            if (!suggestedEntries || suggestedEntries.length === 0) {
                currentSuggestedModsBody.innerHTML = '<tr><td colspan="5">لا توجد مودات مقترحة حالياً.</td></tr>';
                return;
            }

            const modIds = suggestedEntries.map(entry => entry.mod_id);
            const { data: modsDetails, error: modsError } = await supabaseClient
                .from('mods')
                .select('id, name, category')
                .in('id', modIds);

            if (modsError) throw modsError;

            const modsDetailMap = new Map(modsDetails.map(mod => [mod.id, mod]));

            currentSuggestedModsBody.innerHTML = suggestedEntries.map(entry => {
                const modDetail = modsDetailMap.get(entry.mod_id);
                const isRequired = entry.is_required || false;
                return `
                    <tr data-suggested-id="${entry.id}" data-mod-id="${entry.mod_id}">
                        <td>${modDetail?.name || 'مود محذوف أو غير معروف'}</td>
                        <td>${modDetail?.category || 'N/A'}</td>
                        <td><input type="number" class="display-order-input" value="${entry.display_order || 0}" style="width: 60px;"></td>
                        <td>
                            <button class="toggle-required-button ${isRequired ? 'required-active' : ''}"
                                    data-suggested-id="${entry.id}"
                                    data-is-required="${isRequired ? 'true' : 'false'}"
                                    title="${isRequired ? 'إلغاء تحديد كمطلوب' : 'تحديد كمطلوب'}">
                                <i class="fas ${isRequired ? 'fa-check-circle' : 'fa-circle'}"></i>
                                ${isRequired ? 'مطلوب' : 'غير مطلوب'}
                            </button>
                        </td>
                        <td class="action-cell">
                            <button class="update-suggested-order-button" data-suggested-id="${entry.id}" title="تحديث الترتيب"><i class="fas fa-save"></i> تحديث</button>
                            <button class="remove-from-suggested-button" data-suggested-id="${entry.id}" title="إزالة من المقترحات"><i class="fas fa-trash-alt"></i> إزالة</button>
                        </td>
                    </tr>
                `;
            }).join('');

            document.querySelectorAll('.update-suggested-order-button').forEach(button => {
                button.addEventListener('click', handleUpdateSuggestedOrderClick);
            });
            document.querySelectorAll('.remove-from-suggested-button').forEach(button => {
                button.addEventListener('click', handleRemoveFromSuggestedClick);
            });
            document.querySelectorAll('.toggle-required-button').forEach(button => {
                button.addEventListener('click', handleToggleRequiredClick);
            });

        } catch (error) {
            console.error("Error loading current suggested mods:", error);
            showError(currentSuggestedModsBody, `خطأ في تحميل المودات المقترحة: ${error.message}`);
        }
    }

    async function handleAddToSuggestedClick(event) {
        const modId = event.target.closest('button').dataset.id;
        const defaultOrder = 0; // Or calculate next available order

        // Check if already suggested (client-side check for quick feedback, server will also prevent duplicates if constraint exists)
        const { data: existing, error: checkError } = await supabaseClient
            .from(SUGGESTED_MODS_TABLE)
            .select('id')
            .eq('mod_id', modId)
            .maybeSingle();

        if (checkError) {
            alert(`خطأ عند التحقق من المود: ${checkError.message}`);
            return;
        }
        if (existing) {
            alert("هذا المود مقترح بالفعل.");
            event.target.closest('button').disabled = true;
            event.target.closest('button').textContent = 'مقترح';
            return;
        }

        const displayOrder = parseInt(prompt("أدخل ترتيب العرض لهذا المود المقترح (مثلاً: 0, 1, 2...):", defaultOrder), 10);
        if (isNaN(displayOrder)) {
            alert("ترتيب العرض غير صالح.");
            return;
        }

        // سؤال المستخدم إذا كان المود مطلوب
        const isRequired = confirm("هل تريد تحديد هذا المود كمطلوب؟");

        try {
            const { error } = await supabaseClient
                .from(SUGGESTED_MODS_TABLE)
                .insert({
                    mod_id: modId,
                    display_order: displayOrder,
                    is_required: isRequired
                });
            if (error) throw error;
            alert("تمت إضافة المود إلى المقترحات بنجاح.");
            loadCurrentSuggestedMods(); // Refresh current suggested list
            loadAvailableModsForSuggestion(); // Refresh available list to update button states
        } catch (error) {
            console.error("Error adding mod to suggested:", error);
            alert(`خطأ في إضافة المود للمقترحات: ${error.message}`);
        }
    }

    async function handleRemoveFromSuggestedClick(event) {
        const suggestedId = event.target.closest('button').dataset.suggestedId;
        if (confirm("هل أنت متأكد أنك تريد إزالة هذا المود من المقترحات؟")) {
            try {
                const { error } = await supabaseClient
                    .from(SUGGESTED_MODS_TABLE)
                    .delete()
                    .eq('id', suggestedId);
                if (error) throw error;
                alert("تمت إزالة المود من المقترحات بنجاح.");
                loadCurrentSuggestedMods();
                loadAvailableModsForSuggestion(); // Refresh available list to update button states
            } catch (error) {
                console.error("Error removing mod from suggested:", error);
                alert(`خطأ في إزالة المود من المقترحات: ${error.message}`);
            }
        }
    }

    async function handleUpdateSuggestedOrderClick(event) {
        const button = event.target.closest('button');
        const suggestedId = button.dataset.suggestedId;
        const row = button.closest('tr');
        const orderInput = row.querySelector('.display-order-input');
        const newOrder = parseInt(orderInput.value, 10);

        if (isNaN(newOrder)) {
            alert("ترتيب العرض المدخل غير صالح.");
            return;
        }

        try {
            const { error } = await supabaseClient
                .from(SUGGESTED_MODS_TABLE)
                .update({ display_order: newOrder })
                .eq('id', suggestedId);
            if (error) throw error;
            alert("تم تحديث ترتيب عرض المود بنجاح.");
            loadCurrentSuggestedMods(); // Refresh to show new order
        } catch (error) {
            console.error("Error updating suggested mod order:", error);
            alert(`خطأ في تحديث ترتيب العرض: ${error.message}`);
        }
    }

    async function handleToggleRequiredClick(event) {
        const button = event.target.closest('button');
        const suggestedId = button.dataset.suggestedId;
        const currentIsRequired = button.dataset.isRequired === 'true';
        const newIsRequired = !currentIsRequired;

        try {
            const { error } = await supabaseClient
                .from(SUGGESTED_MODS_TABLE)
                .update({ is_required: newIsRequired })
                .eq('id', suggestedId);

            if (error) throw error;

            // تحديث حالة الزر مباشرة بدون إعادة تحميل الجدول بالكامل
            button.dataset.isRequired = newIsRequired ? 'true' : 'false';
            button.classList.toggle('required-active', newIsRequired);
            button.title = newIsRequired ? 'إلغاء تحديد كمطلوب' : 'تحديد كمطلوب';

            const icon = button.querySelector('i');
            if (icon) {
                icon.className = `fas ${newIsRequired ? 'fa-check-circle' : 'fa-circle'}`;
            }

            button.innerHTML = `<i class="fas ${newIsRequired ? 'fa-check-circle' : 'fa-circle'}"></i> ${newIsRequired ? 'مطلوب' : 'غير مطلوب'}`;

            // إظهار رسالة تأكيد
            alert(newIsRequired ? "تم تحديد المود كمطلوب بنجاح." : "تم إلغاء تحديد المود كمطلوب بنجاح.");

        } catch (error) {
            console.error("Error toggling mod required status:", error);
            alert(`خطأ في تغيير حالة المود: ${error.message}`);
        }
    }


    // --- Categories Form Event Listeners ---
    if (categoryForm) {
        categoryForm.addEventListener('submit', handleCategoryFormSubmit);
    }
    if (cancelCategoryEditBtn) {
        cancelCategoryEditBtn.addEventListener('click', resetCategoryForm);
    }

    // --- Initial Load ---
    // fetchUsageLimits(); // Removed as the section is gone
    fetchStats();       // Fetch general stats
    fetchAllMods().then(() => {
        // Only load available mods for suggestion after allModsData is populated
        loadAvailableModsForSuggestion();
    });     // Fetch all mods for management table
    loadCurrentSuggestedMods(); // Load currently suggested mods
    fetchRecentLikes(); // Fetch recent likes activity
    // loadAdSettings(); // Removed ad settings load
    fetchDrawerLinks(); // Fetch drawer links
    fetchUserActivityStats(); // Fetch user activity stats
    fetchUserPreferences(); // Fetch user preferences
    fetchCategories(); // Fetch categories

    // Event listeners for new search/filter for available suggested mods
    if(searchAvailableSuggestInput) searchAvailableSuggestInput.addEventListener('input', loadAvailableModsForSuggestion);
    if(categoryFilterAvailableSuggest) categoryFilterAvailableSuggest.addEventListener('change', loadAvailableModsForSuggestion);

});
