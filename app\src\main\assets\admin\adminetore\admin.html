<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم مشرف Modetaris</title>
    <link rel="stylesheet" href="admin_style.css">
    <!-- Include Supabase JS Library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Supabase Manager -->
    <script src="../../supabase-manager.js"></script>
    <!-- Include Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <h1>لوحة تحكم مشرف Modetaris</h1>
    </header>

    <main>
        <section id="stats-section">
            <h2>الإحصائيات</h2>
            <div class="stats-container">
                <div class="stat-card">
                    <h3>المودات الأكثر نقراً</h3>
                    <ol id="most-clicked-list">
                        <li>جاري التحميل...</li>
                    </ol>
                </div>
                <div class="stat-card">
                    <h3>المودات الأكثر إعجاباً</h3>
                    <ol id="most-liked-list">
                        <li>جاري التحميل...</li>
                    </ol>
                </div>
                <div class="stat-card">
                    <h3>المودات الأكثر تحميلاً</h3>
                    <ol id="most-downloaded-list">
                        <li>جاري التحميل...</li>
                    </ol>
                </div>
            </div>
        </section>

        <hr>

        <section id="recent-activity-section">
            <h2>أحدث النشاطات</h2>
            <div class="activity-container">
                <div class="activity-card">
                    <h3>أحدث الإعجابات</h3>
                    <table id="recent-likes-table">
                        <thead>
                            <tr>
                                <th>المستخدم (ID)</th>
                                <th>المود</th>
                                <th>الوقت</th>
                            </tr>
                        </thead>
                        <tbody id="recent-likes-body">
                            <tr><td colspan="3">جاري التحميل...</td></tr>
                        </tbody>
                    </table>
                </div>
                <!-- يمكن إضافة بطاقات أخرى هنا لاحقاً لنشاطات أخرى -->
            </div>
        </section>

        <!-- Section for User Activity Statistics (Active/Inactive Users) -->
        <hr>
        <section id="user-activity-section">
            <h2>إحصائيات نشاط المستخدمين</h2>
            <div class="stats-container">
                <div class="stat-card">
                    <h3>إجمالي المستخدمين</h3>
                    <p id="total-users-count">جاري التحميل...</p>
                </div>
                <div class="stat-card">
                    <h3>المستخدمون النشطون</h3>
                    <p id="active-users-count">جاري التحميل...</p>
                </div>
                <div class="stat-card">
                    <h3>المستخدمون غير النشطين</h3>
                    <p id="inactive-users-count">جاري التحميل...</p>
                </div>
            </div>
            <small>ملاحظة: سيتم تعريف المستخدم "النشط" بناءً على آخر تسجيل دخول أو نشاط.</small>
        </section>

        <!-- Section for User Preferences -->
        <hr>
        <section id="user-preferences-section">
            <h2>تفضيلات المستخدمين</h2>
            <div class="stats-container">
                <div class="stat-card">
                    <h3>الفئات الأكثر تفضيلاً (بناءً على الإعجابات)</h3>
                    <ol id="popular-categories-list">
                        <li>جاري التحميل...</li>
                    </ol>
                </div>
                <!-- Add more preference stats here if needed -->
            </div>
        </section>

        <hr>

        <section id="manage-mods-section">
            <h2>إدارة المودات</h2>
            <div class="manage-mods-buttons">
                <button id="show-publish-form-btn" class="action-button">نشر مود جديد</button>
                <button id="delete-all-mods-btn" class="delete-button danger-button">حذف كل المودات</button> <!-- Added Button -->
            </div>

            <div id="publish-mod-form-container" class="form-container" style="display: none;">
                <h3>نشر مود جديد</h3>
                <form id="publish-mod-form">
                    <label for="publish-name">الاسم:</label>
                    <input type="text" id="publish-name" name="name" required>

                    <label for="publish-description">الوصف:</label>
                    <textarea id="publish-description" name="description" required></textarea>

                    <label for="publish-category">الفئة:</label>
                    <select id="publish-category" name="category" required>
                        <option value="Addons">إضافات</option>
                        <option value="Texture">خامات</option>
                        <option value="Shaders">تظليل</option>
                        <!-- أضف فئات أخرى إذا لزم الأمر -->
                    </select>

                    <label for="publish-version">الإصدار:</label>
                    <input type="text" id="publish-version" name="version">

                    <label for="publish-size">الحجم:</label>
                    <input type="text" id="publish-size" name="size">

                    <label for="publish-download-url">رابط التحميل (.mcaddon):</label>
                    <input type="url" id="publish-download-url" name="download_url" placeholder="الصق الرابط أو قم برفع الملف أدناه">

                    <label for="publish-mod-file">رفع ملف المود (.mcaddon، إلخ):</label>
                    <input type="file" id="publish-mod-file" name="mod_file" accept=".mcaddon,.mcpack,.zip">
                    <small>إذا قمت برفع ملف، سيتم استخدام رابطه تلقائياً.</small>

                    <label for="publish-images">الصور (اختر متعددة):</label>
                    <input type="file" id="publish-images" name="images" multiple accept="image/*">
                    <div id="publish-image-previews" class="image-previews"></div>

                    <label for="publish-other-images">صور أخرى (اختياري):</label>
                    <input type="file" id="publish-other-images" name="other_images" multiple accept="image/*">
                    <small>يمكنك النقر على هذا الزر عدة مرات لإضافة المزيد من الصور. انقر على زر X لإزالة أي صورة.</small>
                    <div id="publish-other-image-previews" class="image-previews"></div>

                    <label for="publish-extra-images">صور إضافية (اختياري، يمكنك إضافة أكثر من 10 صور):</label>
                    <input type="file" id="publish-extra-images" name="extra_images" multiple accept="image/*">
                    <small>يمكنك النقر على هذا الزر عدة مرات لإضافة المزيد من الصور. انقر على زر X لإزالة أي صورة.</small>
                    <div id="publish-extra-image-previews" class="image-previews"></div>

                    <button type="submit" class="action-button">نشر المود</button>
                    <button type="button" id="cancel-publish-btn" class="cancel-button">إلغاء</button>
                </form>
            </div>

            <div id="edit-mod-form-container" class="form-container" style="display: none;">
                <h3>تعديل المود</h3>
                <form id="edit-mod-form">
                    <input type="hidden" id="edit-mod-id" name="id">
                    <label for="edit-name">الاسم:</label>
                    <input type="text" id="edit-name" name="name" required>

                    <label for="edit-description">الوصف:</label>
                    <textarea id="edit-description" name="description" required></textarea>

                    <label for="edit-category">الفئة:</label>
                    <select id="edit-category" name="category" required>
                        <option value="Addons">إضافات</option>
                        <option value="Texture">خامات</option>
                        <option value="Shaders">تظليل</option>
                         <!-- أضف فئات أخرى إذا لزم الأمر -->
                    </select>

                    <label for="edit-version">الإصدار:</label>
                    <input type="text" id="edit-version" name="version">

                    <label for="edit-size">الحجم:</label>
                    <input type="text" id="edit-size" name="size">

                    <label for="edit-download-url">رابط التحميل (.mcaddon):</label>
                    <input type="url" id="edit-download-url" name="download_url" placeholder="الصق الرابط أو قم برفع ملف جديد أدناه">

                    <label for="edit-mod-file">رفع ملف مود جديد (اختياري، يستبدل الرابط الحالي):</label>
                    <input type="file" id="edit-mod-file" name="mod_file" accept=".mcaddon,.mcpack,.zip">
                    <small>إذا قمت برفع ملف جديد، سيحل رابطه محل الرابط الحالي.</small>

                    <label>الصور الحالية:</label>
                    <div id="edit-current-images" class="image-previews"></div>

                    <label for="edit-new-images">رفع صور جديدة (اختياري، يستبدل الصور الحالية):</label>
                    <input type="file" id="edit-new-images" name="new_images" multiple accept="image/*">
                    <small>يمكنك النقر على هذا الزر عدة مرات لإضافة المزيد من الصور. انقر على زر X لإزالة أي صورة.</small>
                    <div id="edit-new-image-previews" class="image-previews"></div>

                    <div class="form-checkbox-group">
                        <input type="checkbox" id="edit-is-required" name="is_required">
                        <label for="edit-is-required">مطلوب</label>
                        <small>حدد هذا الخيار إذا كان هذا المود مطلوب من قبل المستخدمين</small>
                    </div>

                    <button type="submit" class="action-button">حفظ التغييرات</button>
                    <button type="button" id="cancel-edit-btn" class="cancel-button">إلغاء</button>
                    <button type="button" id="delete-mod-btn" class="delete-button">حذف المود</button>
                </form>
            </div>

            <div class="mod-list-controls">
                <input type="text" id="search-input" placeholder="ابحث عن المودات بالاسم...">
                <select id="category-filter">
                    <option value="All">جميع الفئات</option>
                    <option value="Addons">إضافات</option>
                    <option value="Texture">خامات</option>
                    <option value="Shaders">تظليل</option>
                    <!-- أضف فئات أخرى إذا لزم الأمر -->
                </select>
            </div>

            <div id="mod-list-container">
                <h3>كل المودات</h3>
                <table id="mods-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>النقرات</th>
                            <th>الإعجابات</th>
                            <th>التحميلات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="mods-table-body">
                        <!-- صفوف المودات سيتم تحميلها هنا -->
                        <tr><td colspan="6">جاري تحميل المودات...</td></tr>
                    </tbody>
                </table>
            </div>
        </section>

        <hr>

        <section id="manage-drawer-links-section">
            <h2>إدارة روابط القائمة الجانبية</h2>
            <div class="form-container">
                <h3>إضافة/تعديل رابط</h3>
                <form id="drawer-link-form">
                    <input type="hidden" id="drawer-link-id" name="id">

                    <label for="drawer-link-text">نص الرابط:</label>
                    <input type="text" id="drawer-link-text" name="text" required>

                    <label for="drawer-link-url">URL الرابط:</label>
                    <input type="url" id="drawer-link-url" name="url" required placeholder="https://example.com">

                    <label for="drawer-link-social-icon">اختر أيقونة تواصل اجتماعي (اختياري):</label>
                    <select id="drawer-link-social-icon" name="social_icon_select">
                        <option value="">-- اختر أيقونة --</option>
                        <option value="fab fa-youtube">يوتيوب</option>
                        <option value="fab fa-telegram-plane">تيليجرام</option>
                        <option value="fab fa-twitter">تويتر</option>
                        <option value="fab fa-discord">ديسكورد</option>
                        <option value="fab fa-facebook">فيسبوك</option>
                        <option value="fab fa-instagram">إنستغرام</option>
                        <option value="fab fa-whatsapp">واتساب</option>
                        <option value="fas fa-globe">موقع ويب</option>
                        <option value="fas fa-link">رابط عام</option>
                        <option value="NO_ICON">بدون أيقونة محددة</option>
                    </select>

                    <label for="drawer-link-icon">أو أدخل رمز أيقونة Font Awesome يدوياً:</label>
                    <input type="text" id="drawer-link-icon" name="icon_class" placeholder="fas fa-user">
                    <small>إذا اخترت أيقونة من القائمة أعلاه، سيتم ملء هذا الحقل تلقائياً. يمكنك أيضاً إدخال فئة Font Awesome مخصصة هنا أو تركها فارغة.</small>

                    <label for="drawer-link-order">ترتيب الظهور:</label>
                    <input type="number" id="drawer-link-order" name="order" value="0" required>

                    <label for="drawer-link-active">نشط:</label>
                    <input type="checkbox" id="drawer-link-active" name="is_active" checked>

                    <div class="form-actions">
                        <button type="submit" class="action-button">حفظ الرابط</button>
                        <button type="button" id="cancel-drawer-link-edit-btn" class="cancel-button" style="display: none;">إلغاء التعديل</button>
                    </div>
                </form>
            </div>

            <div id="drawer-links-list-container">
                <h3>الروابط الحالية</h3>
                <table id="drawer-links-table">
                    <thead>
                        <tr>
                            <th>النص</th>
                            <th>URL</th>
                            <th>الأيقونة</th>
                            <th>الترتيب</th>
                            <th>نشط</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="drawer-links-table-body">
                        <!-- سيتم تحميل الروابط هنا -->
                        <tr><td colspan="6">جاري تحميل الروابط...</td></tr>
                    </tbody>
                </table>
            </div>
        </section>

        <hr>

        <section id="manage-suggested-mods-section">
            <h2>إدارة المودات المقترحة</h2>

            <div id="available-mods-for-suggestion-container">
                <h3>المودات المتاحة للإقتراح</h3>
                <div class="mod-list-controls">
                    <input type="text" id="search-available-suggest-input" placeholder="ابحث عن مود لإضافته كمقترح...">
                    <select id="category-filter-available-suggest">
                        <option value="All">جميع الفئات</option>
                        <option value="Addons">إضافات</option>
                        <option value="Texture">خامات</option>
                        <option value="Shaders">تظليل</option>
                        <!-- Add other categories as needed -->
                    </select>
                </div>
                <table id="available-mods-for-suggestion-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>الإجراء</th>
                        </tr>
                    </thead>
                    <tbody id="available-mods-for-suggestion-body">
                        <!-- Mod rows will be loaded here by admin_script.js -->
                        <tr><td colspan="3">جاري تحميل المودات المتاحة...</td></tr>
                    </tbody>
                </table>
            </div>

            <hr style="margin: 30px 0;">

            <div id="current-suggested-mods-container">
                <h3>المودات المقترحة حالياً</h3>
                <table id="current-suggested-mods-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>ترتيب العرض</th>
                            <th>مطلوب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="current-suggested-mods-body">
                        <!-- Suggested mod rows will be loaded here by admin_script.js -->
                        <tr><td colspan="5">جاري تحميل المودات المقترحة...</td></tr>
                    </tbody>
                </table>
            </div>
        </section>

        <hr>

        <section id="manage-categories-section">
            <h2>إدارة الأقسام</h2>
            <div class="form-container">
                <h3>إضافة/تعديل قسم</h3>
                <form id="category-form">
                    <input type="hidden" id="category-id" name="id">

                    <label for="category-name">اسم القسم:</label>
                    <input type="text" id="category-name" name="name" required>

                    <label for="category-icon">رمز الأيقونة (Font Awesome):</label>
                    <input type="text" id="category-icon" name="icon_class" placeholder="fas fa-folder">
                    <small>أدخل فئة Font Awesome مخصصة هنا (مثال: fas fa-folder, fas fa-image, fas fa-palette).</small>

                    <div class="form-actions">
                        <button type="submit" class="action-button">حفظ القسم</button>
                        <button type="button" id="cancel-category-edit-btn" class="cancel-button" style="display: none;">إلغاء التعديل</button>
                    </div>
                </form>
            </div>

            <div id="categories-list-container">
                <h3>الأقسام الحالية</h3>
                <table id="categories-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الأيقونة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="categories-table-body">
                        <!-- سيتم تحميل الأقسام هنا -->
                        <tr><td colspan="3">جاري تحميل الأقسام...</td></tr>
                    </tbody>
                </table>
            </div>
        </section>

        <hr>

        <!-- Removed delete-files-section as it's not used -->
        <!-- Removed manage-ads-section -->
    </main>

    <footer>
        <p>&copy; 2025 مشرف Modetaris</p>
    </footer>

    <script src="admin_script.js"></script>
</body>
</html>
