// إدارة حقوق الطبع والنشر المخصصة
let supabaseClient;
let allMods = [];
let selectedMods = new Set();
let currentPage = 1;
let totalMods = 0;
const modsPerPage = 6; // تقليل أكثر لتسريع التحميل
let loadingCache = new Map(); // cache للنتائج

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // انتظار تحميل Supabase Manager
        await waitForSupabaseManager();
        supabaseClient = supabaseManager.getMainClient();

        // تحميل المودات المحددة مسبقاً
        await loadSelectedMods();

        // تحميل جميع المودات
        await loadMods();

        // إعداد البحث
        setupSearch();

    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        showMessage('خطأ في تحميل التطبيق', 'error');
    }
});

// انتظار تحميل Supabase Manager (محسن للسرعة)
function waitForSupabaseManager() {
    return new Promise((resolve) => {
        if (window.supabaseManager) {
            console.log('✅ Supabase Manager already available');
            resolve();
        } else {
            console.log('⏳ Waiting for Supabase Manager...');
            let attempts = 0;
            const maxAttempts = 50; // 5 ثوان كحد أقصى

            const checkInterval = setInterval(() => {
                attempts++;
                if (window.supabaseManager) {
                    console.log('✅ Supabase Manager loaded');
                    clearInterval(checkInterval);
                    resolve();
                } else if (attempts >= maxAttempts) {
                    console.warn('⚠️ Timeout waiting for Supabase Manager, proceeding anyway');
                    clearInterval(checkInterval);
                    // إنشاء instance جديد إذا لم يكن متوفر
                    if (typeof SupabaseManager !== 'undefined') {
                        window.supabaseManager = new SupabaseManager();
                        console.log('✅ Created new Supabase Manager instance');
                    }
                    resolve();
                }
            }, 100);
        }
    });
}

// تحميل المودات المحددة مسبقاً
async function loadSelectedMods() {
    try {
        const { data, error } = await supabaseClient
            .from('custom_copyright_mods')
            .select(`
                mod_id,
                mods (
                    id,
                    name,
                    image_urls,
                    category,
                    downloads,
                    likes
                )
            `)
            .eq('is_active', true);

        if (error) {
            throw error;
        }

        selectedMods.clear();
        data.forEach(item => {
            if (item.mods) {
                selectedMods.add(item.mod_id);
            }
        });

        updateSelectedModsDisplay();

    } catch (error) {
        console.error('خطأ في تحميل المودات المحددة:', error);
        showMessage('خطأ في تحميل المودات المحددة: ' + error.message, 'error');
    }
}

// تحميل المودات مع pagination محسن
async function loadMods() {
    try {
        const searchTerm = document.getElementById('searchBox').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;

        // عرض مؤشر التحميل
        document.getElementById('modsGrid').innerHTML = '<div class="loading">جاري تحميل المودات...</div>';

        console.log(`🔄 Loading page ${currentPage} with ${modsPerPage} mods per page`);

        // استعلام مبسط جداً
        const startTime = Date.now();
        const { data, error } = await supabaseClient
            .from('mods')
            .select('id, name, image_urls, category, downloads, likes')
            .order('id', { ascending: false })
            .limit(modsPerPage);

        console.log(`✅ Query completed in ${Date.now() - startTime}ms, got ${data?.length || 0} mods`);

        if (error) {
            throw error;
        }

        // تحديث البيانات
        allMods = data || [];
        totalMods = allMods.length; // مؤقت - فقط للاختبار

        displayMods();
        updatePagination();

    } catch (error) {
        console.error('❌ خطأ في تحميل المودات:', error);
        document.getElementById('modsGrid').innerHTML =
            `<div class="error-message">خطأ في تحميل المودات: ${error.message}</div>`;
    }
}

// عرض المودات
function displayMods() {
    const container = document.getElementById('modsGrid');

    if (allMods.length === 0) {
        container.innerHTML = '<p style="color: #888; text-align: center; grid-column: 1 / -1;">لا توجد مودات</p>';
        return;
    }

    const modsHTML = allMods.map(mod => {
        const isSelected = selectedMods.has(mod.id);
        const mainImage = getMainImage(mod.image_urls);

        return `
            <div class="mod-item ${isSelected ? 'selected' : ''}"
                 onclick="toggleModSelection('${mod.id}')"
                 data-mod-id="${mod.id}">
                <img src="${mainImage}" alt="${mod.name}" class="mod-image"
                     onerror="this.src='../image/placeholder.png'">
                <div class="mod-name">${escapeHtml(mod.name)}</div>
                <div class="mod-stats">
                    📥 ${formatCount(mod.downloads || 0)} |
                    ❤️ ${formatCount(mod.likes || 0)}
                </div>
                <div class="mod-category">${mod.category}</div>
                <div class="mod-status ${isSelected ? 'status-custom' : 'status-normal'}">
                    ${isSelected ? 'حقوق طبع مخصصة' : 'حقوق طبع عادية'}
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = modsHTML;
}

// تبديل تحديد المود
function toggleModSelection(modId) {
    if (selectedMods.has(modId)) {
        selectedMods.delete(modId);
    } else {
        selectedMods.add(modId);
    }

    // تحديث عرض المود في الشبكة
    const modElement = document.querySelector(`[data-mod-id="${modId}"]`);
    if (modElement) {
        const isSelected = selectedMods.has(modId);
        modElement.classList.toggle('selected', isSelected);

        const statusElement = modElement.querySelector('.mod-status');
        if (statusElement) {
            statusElement.className = `mod-status ${isSelected ? 'status-custom' : 'status-normal'}`;
            statusElement.textContent = isSelected ? 'حقوق طبع مخصصة' : 'حقوق طبع عادية';
        }
    }

    updateSelectedModsDisplay();
}

// تحديث عرض المودات المحددة
async function updateSelectedModsDisplay() {
    const container = document.getElementById('selectedModsList');
    const countElement = document.getElementById('selectedCount');

    countElement.textContent = selectedMods.size;

    if (selectedMods.size === 0) {
        container.innerHTML = '<p style="color: #888; text-align: center;">لم يتم تحديد أي مودات بعد</p>';
        return;
    }

    try {
        const { data, error } = await supabaseClient
            .from('mods')
            .select('id, name, image_urls')
            .in('id', Array.from(selectedMods));

        if (error) {
            throw error;
        }

        const selectedHTML = data.map(mod => {
            const mainImage = getMainImage(mod.image_urls);

            return `
                <div class="selected-mod-item">
                    <img src="${mainImage}" alt="${mod.name}" class="selected-mod-image"
                         onerror="this.src='../image/placeholder.png'">
                    <div class="selected-mod-info">
                        <div class="selected-mod-name">${escapeHtml(mod.name)}</div>
                        <div style="color: #888; font-size: 0.9rem;">ID: ${mod.id}</div>
                        <div style="color: #ff6b6b; font-size: 0.8rem; font-weight: bold;">
                            <i class="fa-solid fa-exclamation-triangle"></i>
                            سيظهر وصف حقوق الطبع المخصص
                        </div>
                    </div>
                    <button class="remove-btn" onclick="toggleModSelection('${mod.id}')">
                        إزالة
                    </button>
                </div>
            `;
        }).join('');

        container.innerHTML = selectedHTML;

    } catch (error) {
        console.error('خطأ في تحديث عرض المودات المحددة:', error);
    }
}

// حفظ المودات المحددة
async function saveCustomCopyrightMods() {
    try {
        // حذف الربطات الموجودة
        const { error: deleteError } = await supabaseClient
            .from('custom_copyright_mods')
            .delete()
            .neq('id', 0); // حذف جميع السجلات

        if (deleteError) {
            throw deleteError;
        }

        // إضافة الربطات الجديدة
        if (selectedMods.size > 0) {
            const insertData = Array.from(selectedMods).map(modId => ({
                mod_id: modId,
                is_active: true
            }));

            const { error: insertError } = await supabaseClient
                .from('custom_copyright_mods')
                .insert(insertData);

            if (insertError) {
                throw insertError;
            }
        }

        showMessage(`تم حفظ ${selectedMods.size} مود بنجاح`, 'success');

    } catch (error) {
        console.error('خطأ في حفظ المودات:', error);
        showMessage('خطأ في حفظ المودات: ' + error.message, 'error');
    }
}

// إعداد البحث
function setupSearch() {
    const searchBox = document.getElementById('searchBox');
    const categoryFilter = document.getElementById('categoryFilter');

    let searchTimeout;

    searchBox.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1; // إعادة تعيين الصفحة عند البحث
            loadMods();
        }, 300); // تقليل وقت الانتظار
    });

    categoryFilter.addEventListener('change', function() {
        currentPage = 1; // إعادة تعيين الصفحة عند تغيير الفئة
        loadMods();
    });
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const container = document.getElementById('pagination');
    const totalPages = Math.ceil(totalMods / modsPerPage);

    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // عرض معلومات الصفحة
    const startItem = (currentPage - 1) * modsPerPage + 1;
    const endItem = Math.min(currentPage * modsPerPage, totalMods);
    paginationHTML += `
        <div style="color: #888; margin-bottom: 10px; text-align: center;">
            عرض ${startItem}-${endItem} من ${totalMods} مود
        </div>
    `;

    // زر السابق
    paginationHTML += `
        <button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            السابق
        </button>
    `;

    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            paginationHTML += `
                <button onclick="changePage(${i})" ${i === currentPage ? 'class="active"' : ''}>
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            paginationHTML += '<span>...</span>';
        }
    }

    // زر التالي
    paginationHTML += `
        <button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            التالي
        </button>
    `;

    container.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(totalMods / modsPerPage);
    if (page < 1 || page > totalPages) return;

    currentPage = page;
    loadMods(); // إعادة تحميل البيانات للصفحة الجديدة
}

// الحصول على الصورة الرئيسية
function getMainImage(imageUrls) {
    if (!imageUrls) return '../image/placeholder.png';

    try {
        const urls = typeof imageUrls === 'string' ? JSON.parse(imageUrls) : imageUrls;
        return Array.isArray(urls) && urls.length > 0 ? urls[0] : '../image/placeholder.png';
    } catch {
        return '../image/placeholder.png';
    }
}

// تنسيق الأرقام
function formatCount(count) {
    if (count >= 1000000) {
        return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
}

// تأمين النص من HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// عرض الرسائل
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;

    container.innerHTML = '';
    container.appendChild(messageDiv);

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}
