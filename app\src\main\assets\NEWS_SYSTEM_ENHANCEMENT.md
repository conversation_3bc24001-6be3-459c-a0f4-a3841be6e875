# News System Enhancement - New Mods Management

## Overview

تم تحسين نظام المودات الجديدة في قسم News ليصبح أكثر دقة وفعالية، مع إدارة ديناميكية للمودات الجديدة وأيقونة NEW التي تختفي تلقائياً بعد 3 أيام.

## Changes Made

### 1. Updated Recent Mod Detection (`isRecentMod`)

**Before**: 24 hours (1 day)
**After**: 3 days

```javascript
// Helper function to check if a mod is recent (less than 3 days old)
function isRecentMod(createdAt) {
    if (!createdAt) return false;

    try {
        const createdDate = new Date(createdAt);
        const now = new Date();
        const diffTime = Math.abs(now - createdDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays <= 3; // Consider mods less than 3 days old as "new"
    } catch (e) {
        console.error("Error checking if mod is recent:", e);
        return false;
    }
}
```

### 2. NEW Badge Display Logic

**Before**: Only showed in 'All' category
**After**: Shows in all categories and sections

```javascript
// Horizontal cards
${isNew ? '<div class="new-badge">NEW</div>' : ''}

// Vertical cards  
${isNew ? '<div class="new-badge">NEW</div>' : ''}
```

### 3. New Function: `fetchNewModsFromSupabase`

Created a dedicated function to fetch only new mods from the last 3 days:

```javascript
async function fetchNewModsFromSupabase(limit = 10) {
    const cacheKey = `new_mods_${limit || 'none'}`;
    const cachedData = localStorage.getItem(cacheKey);

    // Check cache first (shorter cache time for new mods)
    if (cachedData) {
        try {
            const parsed = JSON.parse(cachedData);
            const cacheAge = Date.now() - parsed.timestamp;
            if (cacheAge < 180000) { // 3 minutes cache for new mods
                console.log(`Using cached new mods data (age: ${Math.round(cacheAge / 1000)}s)`);
                return parsed.data;
            }
        } catch (e) {
            console.error('Error parsing cached new mods data:', e);
        }
    }

    console.log('Fetching fresh new mods data from Supabase');
    try {
        // Calculate date 3 days ago
        const threeDaysAgo = new Date();
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
        const threeDaysAgoISO = threeDaysAgo.toISOString();

        let query = supabaseClient
            .from('mods')
            .select('*')
            .gte('created_at', threeDaysAgoISO) // Greater than or equal to 3 days ago
            .order('created_at', { ascending: false }); // Newest first

        if (limit && typeof limit === 'number' && limit > 0) {
            query = query.limit(limit);
        }

        const { data: newModsData, error } = await query;

        if (error) {
            console.error('Error fetching new mods from Supabase:', error);
            return null;
        }

        if (!newModsData || newModsData.length === 0) {
            console.log('No new mods found in the last 3 days.');
            return [];
        }

        // Cache the new mods data
        try {
            const dataToCache = { timestamp: Date.now(), data: newModsData };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`New mods data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error('Error storing new mods data in localStorage:', e);
        }

        return newModsData;

    } catch (error) {
        console.error('Unexpected error in fetchNewModsFromSupabase:', error);
        return null;
    }
}
```

### 4. Updated `displayModsBySection`

**Before**: Used general mod fetching for News
**After**: Uses dedicated new mods fetching

```javascript
// جلب البيانات - ترتيب جديد: News, Addons (مع Free Addons), Suggested, Shaders, Texture Pack, Seeds, Maps
const fetchPromises = [
    fetchNewModsFromSupabase(10),                          // News - المودات الجديدة (آخر 3 أيام)
    fetchModsFromSupabase('Addons', null, false, 10),      // Addons - القسم الثاني
    fetchFreeAddonsFromSupabase(20),                       // Free Addons - سيتم دمجها مع Addons
    fetchSuggestedModsFromSupabase(10),                    // Suggested - القسم الثالث
    fetchModsFromSupabase('Shaders', null, false, 10),     // Shaders - القسم الرابع
    fetchModsFromSupabase('Texture', null, false, 10),     // Texture Packs - القسم الخامس
    fetchModsFromSupabase('Seeds', null, false, 10),       // Seeds - القسم السادس
    fetchModsFromSupabase('Maps', null, false, 10),        // Maps - القسم السابع
    fetchBannerAds(),                                      // Banner Ads
    fetchFeaturedAddons()                                  // Featured Addons with special effects
];
```

### 5. Enhanced `displaySingleCategory` for News

Added special handling for News category in single view:

```javascript
let items;
if (category === 'News') {
    // Fetch all new mods from the last 3 days, sorted from newest to oldest
    items = await fetchNewModsFromSupabase(null); // null limit to fetch all new mods
    console.log(`Fetched ${items ? items.length : 0} new mods for News category`);
} else if (category === 'Suggested') {
    // ... existing code
}
```

## Features

### 1. News Section Behavior

#### In "All" Category View
- **Shows**: 10 newest mods from the last 3 days
- **Order**: Newest to oldest
- **NEW Badge**: Visible on all mods in this section

#### When Clicking "see all" on News
- **Redirects to**: News category (single view)
- **Shows**: All mods from the last 3 days
- **Order**: Newest to oldest (can be changed with sort buttons)
- **NEW Badge**: Visible on all mods

### 2. NEW Badge Management

#### Automatic Display
- **Appears**: On all mods less than 3 days old
- **Visible**: In all categories and sections
- **Disappears**: Automatically after 3 days

#### Visual Consistency
- **Same styling**: Across horizontal and vertical cards
- **Always visible**: Not limited to 'All' category anymore
- **Dynamic**: Updates based on mod creation date

### 3. Caching Strategy

#### News-Specific Caching
- **Cache Duration**: 3 minutes (shorter than other content)
- **Cache Key**: `new_mods_${limit}`
- **Reason**: News content changes more frequently

#### Performance Benefits
- **Faster Loading**: Cached results for repeated requests
- **Reduced Server Load**: Less frequent database queries
- **Fresh Content**: Short cache ensures recent updates

### 4. Database Query Optimization

#### Efficient Filtering
```sql
SELECT * FROM mods 
WHERE created_at >= '2024-01-XX' 
ORDER BY created_at DESC 
LIMIT 10;
```

#### Benefits
- **Targeted Results**: Only fetches relevant new mods
- **Optimized Performance**: Uses database indexes on created_at
- **Scalable**: Works efficiently as database grows

## User Experience Improvements

### 1. Clear News Identification
- **10 Mods Limit**: Prevents overwhelming users in main view
- **See All Option**: Allows viewing complete new mods list
- **Consistent Sorting**: Always newest first for news content

### 2. Automatic Badge Management
- **No Manual Intervention**: NEW badges appear/disappear automatically
- **Accurate Timing**: Based on exact 3-day calculation
- **Visual Clarity**: Users immediately identify new content

### 3. Seamless Navigation
- **All Category**: Quick overview of latest 10 mods
- **News Category**: Complete list of all new mods
- **Sort Options**: Available in single category view

## Technical Benefits

### 1. Maintainable Code
- **Dedicated Function**: `fetchNewModsFromSupabase` for news-specific logic
- **Clear Separation**: News logic separated from general mod fetching
- **Consistent API**: Same pattern as other specialized functions

### 2. Performance Optimized
- **Targeted Queries**: Only fetch what's needed
- **Smart Caching**: Appropriate cache duration for content type
- **Efficient Updates**: Automatic badge management without manual intervention

### 3. Scalable Architecture
- **Database Efficient**: Uses proper date filtering
- **Memory Efficient**: Limited result sets
- **Network Efficient**: Cached responses reduce bandwidth

This enhancement provides a robust, user-friendly news system that automatically manages new content identification and provides clear navigation between overview and detailed views.
