<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير إشعارات الإصدارات</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .form-section {
            margin-bottom: 30px;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
        input[type="text"], input[type="url"], input[type="number"], textarea, select {
            width: calc(100% - 22px);
            padding: 10px;
            margin-top: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .checkbox-group {
            margin-top: 15px;
        }
        input[type="checkbox"] {
            margin-left: 10px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 20px;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.edit-btn {
            background-color: #ffc107;
            color: #333;
        }
        button.edit-btn:hover {
            background-color: #e0a800;
        }
        button.delete-btn {
            background-color: #dc3545;
        }
        button.delete-btn:hover {
            background-color: #c82333;
        }
        .notifications-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .notifications-table th, .notifications-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .notifications-table th {
            background-color: #f0f0f0;
            position: sticky;
            top: 0;
        }
        .notifications-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .notifications-table tr:hover {
            background-color: #f1f1f1;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        .status-active {
            color: green;
            font-weight: bold;
        }
        .status-inactive {
            color: #999;
        }
        .preview-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            background-color: #f9f9f9;
        }
        .preview-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .preview-notification {
            background-color: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #FFD700;
            max-width: 350px;
            margin: 0 auto;
        }
        .preview-notification h3 {
            color: #ffcc00;
            margin-top: 0;
        }
        .preview-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }
        .preview-update-btn {
            background: linear-gradient(to right, #FFD700, #FFC300);
            color: #333;
            border: none;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .preview-cancel-btn {
            background: transparent;
            color: #ccc;
            border: 1px solid #ccc;
            padding: 8px;
            border-radius: 5px;
            text-align: center;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-left: 5px;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .error {
            color: #dc3545;
            padding: 10px;
            background-color: #f8d7da;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #28a745;
            padding: 10px;
            background-color: #d4edda;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>مدير إشعارات الإصدارات</h1>

        <div class="tabs">
            <div class="tab active" data-tab="create">إنشاء إشعار جديد</div>
            <div class="tab" data-tab="list">قائمة الإشعارات</div>
        </div>

        <div id="createTab" class="tab-content active">
            <form id="notificationForm">
                <input type="hidden" id="notificationId">

                <div class="form-section">
                    <h2>معلومات الإصدار</h2>

                    <label for="version_name">اسم الإصدار المستهدف:</label>
                    <input type="text" id="version_name" placeholder="مثال: 1.2" required>

                    <label for="version_targeting_type">نوع استهداف الإصدار:</label>
                    <select id="version_targeting_type" required>
                        <option value="OLDER_THAN_TARGET">الإصدارات الأقدم من المستهدف</option>
                        <option value="EXACTLY_TARGET">الإصدار المستهدف فقط</option>
                        <option value="ALL_BUT_TARGET">كل الإصدارات ما عدا المستهدف</option>
                        <option value="ALL_VERSIONS">كل الإصدارات</option>
                    </select>

                    <label for="priority">الأولوية (رقم أعلى = أولوية أكبر):</label>
                    <input type="number" id="priority" value="0" min="0" required>
                </div>

                <div class="form-section">
                    <h2>محتوى الإشعار</h2>

                    <label for="title">عنوان الإشعار:</label>
                    <input type="text" id="title" placeholder="مثال: تحديث جديد متوفر!">

                    <label for="message_ar">الرسالة (بالعربية):</label>
                    <textarea id="message_ar" rows="3" placeholder="مثال: يتوفر إصدار جديد من التطبيق. قم بالتحديث الآن!" required></textarea>

                    <label for="message_en">الرسالة (بالإنجليزية):</label>
                    <textarea id="message_en" rows="3" placeholder="مثال: A new version of the app is available. Update now!" required></textarea>

                    <label for="download_url">رابط التحميل:</label>
                    <input type="url" id="download_url" placeholder="مثال: https://play.google.com/store/apps/details?id=com.example.modetaris">

                    <label for="image_url">رابط الصورة (اختياري):</label>
                    <input type="url" id="image_url" placeholder="مثال: https://example.com/image.jpg">
                </div>

                <div class="form-section">
                    <h2>إعدادات إضافية</h2>

                    <div class="checkbox-group">
                        <input type="checkbox" id="is_active" checked>
                        <label for="is_active" style="display: inline;">نشط؟</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="is_exclusive">
                        <label for="is_exclusive" style="display: inline;">حصري؟ (يمنع الإشعارات الأخرى الأقل أولوية)</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="show_cancel_button" checked>
                        <label for="show_cancel_button" style="display: inline;">إظهار زر الإلغاء؟</label>
                    </div>
                </div>

                <div class="preview-container">
                    <div class="preview-title">معاينة الإشعار:</div>
                    <div class="preview-notification">
                        <h3 id="preview-title">تحديث جديد متوفر!</h3>
                        <p id="preview-message">يتوفر إصدار جديد من التطبيق. قم بالتحديث الآن!</p>
                        <div class="preview-buttons">
                            <div class="preview-update-btn">تحديث الآن</div>
                            <div class="preview-cancel-btn" id="preview-cancel">تخطي</div>
                        </div>
                    </div>
                </div>

                <div id="formMessage"></div>

                <button type="submit" id="saveButton">حفظ الإشعار</button>
                <button type="button" id="resetButton">إعادة تعيين النموذج</button>
            </form>
        </div>

        <div id="listTab" class="tab-content">
            <h2>قائمة الإشعارات</h2>
            <div id="notificationsTableContainer">
                <div class="loading">جاري تحميل الإشعارات...</div>
            </div>
        </div>
    </div>

    <script>
        // سيتم إضافة JavaScript هنا في الخطوة التالية
        // Supabase Configuration
        const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
        // Inicializar el cliente de Supabase
        const sbClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Table name - can be changed to use either the old or new table
        const NOTIFICATIONS_TABLE = 'update_notifications';

        // DOM Elements
        const form = document.getElementById('notificationForm');
        const notificationIdInput = document.getElementById('notificationId');
        const formMessage = document.getElementById('formMessage');
        const resetButton = document.getElementById('resetButton');
        const notificationsTableContainer = document.getElementById('notificationsTableContainer');

        // Preview elements
        const previewTitle = document.getElementById('preview-title');
        const previewMessage = document.getElementById('preview-message');
        const previewCancel = document.getElementById('preview-cancel');

        // Tab functionality
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');

                // Update active tab
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // Show active content
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabId + 'Tab').classList.add('active');

                // If list tab is activated, load notifications
                if (tabId === 'list') {
                    loadNotifications();
                }
            });
        });

        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await saveNotification();
        });

        // Reset button
        resetButton.addEventListener('click', () => {
            resetForm();
        });

        // Live preview updates
        document.getElementById('title').addEventListener('input', updatePreview);
        document.getElementById('message_ar').addEventListener('input', updatePreview);
        document.getElementById('show_cancel_button').addEventListener('change', updatePreview);

        function updatePreview() {
            const title = document.getElementById('title').value || 'تحديث جديد متوفر!';
            const messageAr = document.getElementById('message_ar').value || 'يتوفر إصدار جديد من التطبيق. قم بالتحديث الآن!';
            const showCancelButton = document.getElementById('show_cancel_button').checked;

            previewTitle.textContent = title;
            previewMessage.textContent = messageAr;
            previewCancel.style.display = showCancelButton ? 'block' : 'none';
        }

        async function saveNotification() {
            const notificationId = notificationIdInput.value;

            // Crear objeto con solo los campos que existen en la tabla
            const data = {
                version_name: document.getElementById('version_name').value,
                title: document.getElementById('title').value || 'تحديث جديد متوفر!',
                description: document.getElementById('message_ar').value, // Usar description en lugar de message_ar
                update_url: document.getElementById('download_url').value || null, // Usar update_url en lugar de download_url
                image_url: document.getElementById('image_url').value || null,
                version_targeting_type: document.getElementById('version_targeting_type').value,
                priority: parseInt(document.getElementById('priority').value, 10),
                is_active: document.getElementById('is_active').checked,
                is_exclusive: document.getElementById('is_exclusive').checked,
                show_cancel_button: document.getElementById('show_cancel_button').checked
            };

            // Guardar message_en en localStorage para referencia futura
            localStorage.setItem('last_message_en', document.getElementById('message_en').value);

            try {
                let result;

                if (notificationId) {
                    // Update existing notification
                    result = await sbClient
                        .from(NOTIFICATIONS_TABLE)
                        .update(data)
                        .eq('id', notificationId);
                } else {
                    // Create new notification
                    data.created_at = new Date().toISOString();
                    result = await sbClient
                        .from(NOTIFICATIONS_TABLE)
                        .insert([data]);
                }

                if (result.error) {
                    showMessage('error', 'حدث خطأ أثناء حفظ الإشعار: ' + result.error.message);
                } else {
                    showMessage('success', 'تم حفظ الإشعار بنجاح!');
                    resetForm();

                    // Switch to list tab and refresh
                    document.querySelector('.tab[data-tab="list"]').click();
                }
            } catch (error) {
                showMessage('error', 'حدث خطأ غير متوقع: ' + error.message);
            }
        }

        function resetForm() {
            notificationIdInput.value = '';
            form.reset();
            updatePreview();
            showMessage('', '');
            document.getElementById('saveButton').textContent = 'حفظ الإشعار';
        }

        function showMessage(type, message) {
            formMessage.className = type;
            formMessage.textContent = message;
        }

        async function loadNotifications() {
            notificationsTableContainer.innerHTML = '<div class="loading">جاري تحميل الإشعارات...</div>';

            try {
                const { data, error } = await sbClient
                    .from(NOTIFICATIONS_TABLE)
                    .select('*')
                    .order('priority', { ascending: false })
                    .order('created_at', { ascending: false });

                if (error) {
                    notificationsTableContainer.innerHTML = `<div class="error">حدث خطأ أثناء تحميل الإشعارات: ${error.message}</div>`;
                    return;
                }

                if (!data || data.length === 0) {
                    notificationsTableContainer.innerHTML = '<p>لا توجد إشعارات حالياً.</p>';
                    return;
                }

                // Create table
                let tableHTML = `
                    <table class="notifications-table">
                        <thead>
                            <tr>
                                <th>الإصدار</th>
                                <th>العنوان</th>
                                <th>الرسالة (عربي)</th>
                                <th>نوع الاستهداف</th>
                                <th>الأولوية</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                data.forEach(notification => {
                    const statusClass = notification.is_active ? 'status-active' : 'status-inactive';
                    const statusText = notification.is_active ? 'نشط' : 'غير نشط';

                    tableHTML += `
                        <tr>
                            <td>${notification.version_name || ''}</td>
                            <td>${notification.title || 'تحديث جديد متوفر!'}</td>
                            <td>${(notification.description || '').substring(0, 50)}${notification.description && notification.description.length > 50 ? '...' : ''}</td>
                            <td>${getTargetingTypeText(notification.version_targeting_type)}</td>
                            <td>${notification.priority}</td>
                            <td class="${statusClass}">${statusText}</td>
                            <td>${formatDate(notification.created_at)}</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="edit-btn" onclick="editNotification('${notification.id}')">تعديل</button>
                                    <button class="delete-btn" onclick="deleteNotification('${notification.id}')">حذف</button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                tableHTML += '</tbody></table>';
                notificationsTableContainer.innerHTML = tableHTML;

            } catch (error) {
                notificationsTableContainer.innerHTML = `<div class="error">حدث خطأ غير متوقع: ${error.message}</div>`;
            }
        }

        function getTargetingTypeText(type) {
            switch (type) {
                case 'OLDER_THAN_TARGET':
                    return 'الإصدارات الأقدم';
                case 'EXACTLY_TARGET':
                    return 'الإصدار المحدد فقط';
                case 'ALL_BUT_TARGET':
                    return 'كل الإصدارات ما عدا المحدد';
                case 'ALL_VERSIONS':
                    return 'كل الإصدارات';
                default:
                    return type || 'غير محدد';
            }
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG') + ' ' + date.toLocaleTimeString('ar-EG');
        }

        // Edit notification function (will be called from the table)
        window.editNotification = async function(id) {
            try {
                const { data, error } = await sbClient
                    .from(NOTIFICATIONS_TABLE)
                    .select('*')
                    .eq('id', id)
                    .single();

                if (error) {
                    showMessage('error', 'حدث خطأ أثناء تحميل بيانات الإشعار: ' + error.message);
                    return;
                }

                if (!data) {
                    showMessage('error', 'لم يتم العثور على الإشعار!');
                    return;
                }

                // Fill form with notification data
                notificationIdInput.value = data.id;
                document.getElementById('version_name').value = data.version_name || '';
                document.getElementById('title').value = data.title || '';
                document.getElementById('message_ar').value = data.description || ''; // Usar description en lugar de message_ar

                // Intentar recuperar message_en del localStorage o usar un valor predeterminado
                const lastMessageEn = localStorage.getItem('last_message_en') || '';
                document.getElementById('message_en').value = lastMessageEn;

                document.getElementById('download_url').value = data.update_url || ''; // Usar update_url en lugar de download_url
                document.getElementById('image_url').value = data.image_url || '';
                document.getElementById('version_targeting_type').value = data.version_targeting_type || 'OLDER_THAN_TARGET';
                document.getElementById('priority').value = data.priority || 0;
                document.getElementById('is_active').checked = data.is_active !== false;
                document.getElementById('is_exclusive').checked = data.is_exclusive === true;
                document.getElementById('show_cancel_button').checked = data.show_cancel_button !== false;

                // Update preview
                updatePreview();

                // Change button text
                document.getElementById('saveButton').textContent = 'تحديث الإشعار';

                // Switch to create tab
                document.querySelector('.tab[data-tab="create"]').click();

                // Scroll to top
                window.scrollTo(0, 0);

            } catch (error) {
                showMessage('error', 'حدث خطأ غير متوقع: ' + error.message);
            }
        };

        // Delete notification function (will be called from the table)
        window.deleteNotification = async function(id) {
            if (!confirm('هل أنت متأكد أنك تريد حذف هذا الإشعار؟')) {
                return;
            }

            try {
                const { error } = await sbClient
                    .from(NOTIFICATIONS_TABLE)
                    .delete()
                    .eq('id', id);

                if (error) {
                    showMessage('error', 'حدث خطأ أثناء حذف الإشعار: ' + error.message);
                } else {
                    // Refresh notifications list
                    loadNotifications();
                }
            } catch (error) {
                showMessage('error', 'حدث خطأ غير متوقع: ' + error.message);
            }
        };

        // Initialize preview
        updatePreview();
    </script>
</body>
</html>
