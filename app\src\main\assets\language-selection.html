<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Selection - Mod Etaris</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="language-selection-overlay" id="languageSelectionOverlay">
        <div class="language-selection-container">
            <div class="language-selection-title">
                🌍 Choose Your Language
            </div>
            <div class="language-selection-subtitle">
                اختر لغتك المفضلة / Choose your preferred language
            </div>

            <div class="language-options">
                <button class="language-option" onclick="selectLanguage('ar')" data-lang="ar">
                    <span class="language-flag">🇸🇦</span>
                    العربية
                </button>

                <button class="language-option" onclick="selectLanguage('en')" data-lang="en">
                    <span class="language-flag">🇺🇸</span>
                    English
                </button>
            </div>

            <div style="font-size: 0.9rem; color: #ccc; margin-top: 20px;">
                يمكنك تغيير اللغة لاحقاً من الإعدادات<br>
                You can change the language later from settings
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-manager.js"></script>
    <script src="translations.js"></script>
    <script>
        // Initialize Supabase
        let supabaseClient;

        async function initializeSupabase() {
            try {
                const manager = new SupabaseManager();
                await manager.initialize();
                supabaseClient = manager.getClient('language-selection');
                console.log('Supabase initialized for language selection');
            } catch (error) {
                console.error('Error initializing Supabase:', error);
            }
        }

        // Function to select language
        async function selectLanguage(language) {
            try {
                // Save language preference
                localStorage.setItem('selectedLanguage', language);
                localStorage.setItem('languageSelected', 'true');

                // Update document direction for Arabic
                if (language === 'ar') {
                    document.documentElement.dir = 'rtl';
                    document.documentElement.lang = 'ar';
                } else {
                    document.documentElement.dir = 'ltr';
                    document.documentElement.lang = 'en';
                }

                // Save language statistics to database
                await saveLanguageStats(language);

                // Show loading animation
                showLoadingAnimation();

                // Redirect to main app after a short delay
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);

            } catch (error) {
                console.error('Error selecting language:', error);
                // Still redirect even if stats saving fails
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }
        }

        // Function to save language statistics
        async function saveLanguageStats(language) {
            if (!supabaseClient) {
                console.warn('Supabase not initialized, skipping language stats');
                return;
            }

            try {
                // Generate a unique user identifier (or use existing one)
                let userId = localStorage.getItem('userId');
                if (!userId) {
                    userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
                    localStorage.setItem('userId', userId);
                }

                // Get device info
                const deviceInfo = {
                    device: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
                    browser: getBrowserName(),
                    screen: `${screen.width}x${screen.height}`,
                    language: navigator.language
                };

                // Insert or update language preference
                const { error } = await supabaseClient
                    .from('user_languages')
                    .upsert([
                        {
                            user_id: userId,
                            selected_language: language,
                            device_info: deviceInfo,
                            user_agent: navigator.userAgent,
                            updated_at: new Date().toISOString()
                        }
                    ], {
                        onConflict: 'user_id'
                    });

                if (error) {
                    console.error('Error saving language preference:', error);
                } else {
                    console.log('Language preference saved successfully');
                }
            } catch (error) {
                console.error('Error in saveLanguageStats:', error);
            }
        }

        // Helper function to get browser name
        function getBrowserName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) return 'chrome';
            if (userAgent.includes('Firefox')) return 'firefox';
            if (userAgent.includes('Safari')) return 'safari';
            if (userAgent.includes('Edge')) return 'edge';
            return 'unknown';
        }

        // Function to show loading animation
        function showLoadingAnimation() {
            const container = document.querySelector('.language-selection-container');
            container.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 20px;">⚡</div>
                    <div style="color: #ffcc00; font-size: 1.5rem; margin-bottom: 10px;">
                        جاري التحميل... / Loading...
                    </div>
                    <div style="color: #ccc; font-size: 1rem;">
                        مرحباً بك في Mod Etaris / Welcome to Mod Etaris
                    </div>
                </div>
            `;
        }

        // Check if language is already selected
        function checkLanguageSelection() {
            const languageSelected = localStorage.getItem('languageSelected');
            if (languageSelected === 'true') {
                // Language already selected, redirect to main app
                window.location.href = 'index.html';
                return;
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', async () => {
            checkLanguageSelection();
            await initializeSupabase();
        });
    </script>
</body>
</html>
