# نظام المربعات المخصصة (Custom Dialogs System)

## نظرة عامة
تم إنشاء نظام المربعات المخصصة لعرض إشعارات وتنبيهات مهمة للمستخدمين قبل عرض تفاصيل المودات. يمكن للمدير إنشاء مربعات مخصصة بصور ونصوص وأزرار مخصصة وربطها بمودات محددة.

## المميزات الرئيسية

### 1. إنشاء المربعات المخصصة
- عنوان مخصص للمربع
- وصف اختياري
- صورة مخصصة (اختيارية)
- نص زر مخصص (افتراضي: "تم")
- خيار إظهار "عدم الظهور مجدداً"
- إمكانية تفعيل/إلغاء تفعيل المربع

### 2. ربط المربعات بالمودات
- تحديد المودات التي ستظهر لها المربعات
- بحث وتصفية المودات
- إدارة المودات المحددة
- معاينة المربعات قبل الحفظ

### 3. عرض المربعات للمستخدمين
- ظهور المربع قبل عرض تفاصيل المود
- تصميم جذاب مع أنيميشن
- حفظ تفضيلات "عدم الظهور مجدداً"
- إغلاق المربع والانتقال لتفاصيل المود

## هيكل قاعدة البيانات

### جدول custom_mod_dialogs
```sql
CREATE TABLE custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100) DEFAULT 'تم',
    show_dont_show_again BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### جدول custom_dialog_mods (جدول الربط)
```sql
CREATE TABLE custom_dialog_mods (
    id SERIAL PRIMARY KEY,
    dialog_id INTEGER NOT NULL,
    mod_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_dialog_mods_dialog_id FOREIGN KEY (dialog_id) REFERENCES custom_mod_dialogs(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_dialog_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_dialog_mod UNIQUE (dialog_id, mod_id)
);
```

## الملفات المضافة

### 1. صفحات الإدارة
- `admin/custom_dialogs.html` - صفحة إدارة المربعات المخصصة
- `admin/custom_dialogs.js` - منطق إدارة المربعات
- `admin/dialog_mods.html` - صفحة إدارة المودات المرتبطة
- `admin/dialog_mods.js` - منطق إدارة المودات

### 2. التعديلات على الملفات الموجودة
- `supabase-manager.js` - إضافة جداول قاعدة البيانات الجديدة
- `script.js` - إضافة منطق فحص وعرض المربعات المخصصة
- `style.css` - إضافة أنيميشن المربعات المخصصة
- `admin/index.html` - إضافة رابط إدارة المربعات المخصصة

## كيفية الاستخدام

### للمدير:
1. الدخول إلى لوحة الإدارة
2. اختيار "إدارة المربعات المخصصة"
3. إنشاء مربع جديد بالمعلومات المطلوبة
4. تحديد المودات التي ستظهر لها المربع
5. حفظ التغييرات

### للمستخدم:
1. النقر على أي مود مرتبط بمربع مخصص
2. ظهور المربع المخصص أولاً
3. قراءة المعلومات والنقر على الزر
4. اختيار "عدم الظهور مجدداً" إذا رغب
5. الانتقال لعرض تفاصيل المود

## التخزين المحلي
يتم حفظ تفضيلات "عدم الظهور مجدداً" في localStorage بالمفتاح:
```
custom_dialog_{dialog_id}_dont_show
```

## الأمان والتحقق
- تأمين النصوص من HTML injection
- التحقق من صحة البيانات قبل الحفظ
- معالجة الأخطاء بشكل مناسب
- fallback في حالة فشل تحميل البيانات

## التصميم والأنيميشن
- تصميم متجاوب مع جميع الأجهزة
- أنيميشن سلس للظهور والاختفاء
- تأثيرات hover للأزرار
- ألوان متناسقة مع تصميم التطبيق

## الصيانة والتطوير
- كود منظم ومعلق باللغة العربية
- دوال منفصلة لكل وظيفة
- معالجة شاملة للأخطاء
- إمكانية التوسع المستقبلي

## ملاحظات مهمة
- يظهر مربع واحد فقط لكل مود (الأحدث والمفعل)
- يمكن ربط مربع واحد بعدة مودات
- لا يمكن ربط مود واحد بعدة مربعات نشطة
- يتم فحص المربعات قبل فحص تحذيرات الشادر

## استكشاف الأخطاء
- التحقق من console للأخطاء
- التأكد من اتصال قاعدة البيانات
- فحص صحة روابط الصور
- التحقق من صلاحيات المدير
