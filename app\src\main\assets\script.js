const drawer = document.querySelector(".drawer");
const drawerBtn = document.querySelector(".drawer-btn");
const overlay = document.querySelector(".drawer-overlay");

// Global state for current view
let currentCategory = 'All';
let currentSortBy = 'created_at'; // Default sort column
let currentSortAscending = false; // Default sort order (false = descending/newest)
// Removed displayedModsData variable

// Function to get localized description based on user's language preference
function getLocalizedDescription(item) {
    const currentLanguage = localStorage.getItem('selectedLanguage') || 'en';

    if (currentLanguage === 'ar') {
        // Return Arabic description if available, otherwise fallback to English
        return item.description_ar || item.description || t('no_description');
    } else {
        // Return English description
        return item.description || t('no_description');
    }
}

// Function to save user language preference to database
async function saveUserLanguagePreference(language) {
    try {
        // Use the global supabaseClient
        if (!supabaseClient) {
            console.warn('Supabase not available, skipping language save');
            return false;
        }

        // Get or generate user ID
        let userId = localStorage.getItem('userId');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
            localStorage.setItem('userId', userId);
        }

        // Prepare device info
        const deviceInfo = {
            device: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
            browser: getBrowserName(),
            screen: `${screen.width}x${screen.height}`,
            language: navigator.language,
            timestamp: new Date().toISOString()
        };

        // Save to database
        const { error } = await supabaseClient
            .from('user_languages')
            .upsert([
                {
                    user_id: userId,
                    selected_language: language,
                    device_info: deviceInfo,
                    user_agent: navigator.userAgent,
                    updated_at: new Date().toISOString()
                }
            ], {
                onConflict: 'user_id'
            });

        if (error) {
            console.error('Error saving user language preference:', error);
            return false;
        } else {
            console.log('User language preference saved successfully');
            return true;
        }
    } catch (error) {
        console.error('Error in saveUserLanguagePreference:', error);
        return false;
    }
}

// Helper function to get browser name
function getBrowserName() {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'chrome';
    if (userAgent.includes('Firefox')) return 'firefox';
    if (userAgent.includes('Safari')) return 'safari';
    if (userAgent.includes('Edge')) return 'edge';
    return 'unknown';
}

// Function to show language selection modal
function showLanguageSelectionModal() {
    // Remove existing modal if any
    const existingModal = document.getElementById('language-selection-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'language-selection-modal';
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100010;
        animation: fadeIn 0.5s ease;
    `;

    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.style.cssText = `
        background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        border: 3px solid #ffcc00;
        box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
        max-width: 400px;
        width: 90%;
        animation: slideInUp 0.6s ease;
    `;

    // Create title
    const title = document.createElement('h2');
    title.textContent = t('choose_language');
    title.style.cssText = `
        font-size: 2rem;
        color: #ffcc00;
        margin-bottom: 10px;
        font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    `;

    // Create subtitle
    const subtitle = document.createElement('p');
    subtitle.textContent = t('language_subtitle');
    subtitle.style.cssText = `
        font-size: 1.2rem;
        color: #ffffff;
        margin-bottom: 30px;
        opacity: 0.9;
    `;

    // Create language options container
    const optionsContainer = document.createElement('div');
    optionsContainer.style.cssText = `
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 20px;
    `;

    // Create Arabic option
    const arabicOption = document.createElement('button');
    arabicOption.innerHTML = '<span style="font-size: 2rem; margin-right: 10px; vertical-align: middle;">🇸🇦</span>العربية';
    arabicOption.style.cssText = `
        background: linear-gradient(45deg, #ffcc00, #ff9800);
        color: #000;
        border: none;
        padding: 15px 30px;
        border-radius: 15px;
        font-size: 1.3rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
        font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace;
    `;
    arabicOption.onmouseover = () => {
        arabicOption.style.transform = 'translateY(-3px)';
        arabicOption.style.boxShadow = '0 6px 20px rgba(255, 204, 0, 0.5)';
        arabicOption.style.background = 'linear-gradient(45deg, #ffd700, #ffb300)';
    };
    arabicOption.onmouseout = () => {
        arabicOption.style.transform = 'translateY(0)';
        arabicOption.style.boxShadow = '0 4px 15px rgba(255, 204, 0, 0.3)';
        arabicOption.style.background = 'linear-gradient(45deg, #ffcc00, #ff9800)';
    };
    arabicOption.onclick = () => changeLanguage('ar');

    // Create English option
    const englishOption = document.createElement('button');
    englishOption.innerHTML = '<span style="font-size: 2rem; margin-right: 10px; vertical-align: middle;">🇺🇸</span>English';
    englishOption.style.cssText = `
        background: linear-gradient(45deg, #ffcc00, #ff9800);
        color: #000;
        border: none;
        padding: 15px 30px;
        border-radius: 15px;
        font-size: 1.3rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
        font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace;
    `;
    englishOption.onmouseover = () => {
        englishOption.style.transform = 'translateY(-3px)';
        englishOption.style.boxShadow = '0 6px 20px rgba(255, 204, 0, 0.5)';
        englishOption.style.background = 'linear-gradient(45deg, #ffd700, #ffb300)';
    };
    englishOption.onmouseout = () => {
        englishOption.style.transform = 'translateY(0)';
        englishOption.style.boxShadow = '0 4px 15px rgba(255, 204, 0, 0.3)';
        englishOption.style.background = 'linear-gradient(45deg, #ffcc00, #ff9800)';
    };
    englishOption.onclick = () => changeLanguage('en');

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.textContent = '✕';
    closeButton.style.cssText = `
        position: absolute;
        top: 15px;
        right: 15px;
        background: transparent;
        border: none;
        color: #ffcc00;
        font-size: 1.5rem;
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
    `;
    closeButton.onmouseover = () => closeButton.style.backgroundColor = 'rgba(255, 204, 0, 0.2)';
    closeButton.onmouseout = () => closeButton.style.backgroundColor = 'transparent';
    closeButton.onclick = () => modalOverlay.remove();

    // Add elements to containers
    optionsContainer.appendChild(arabicOption);
    optionsContainer.appendChild(englishOption);

    modalContainer.appendChild(closeButton);
    modalContainer.appendChild(title);
    modalContainer.appendChild(subtitle);
    modalContainer.appendChild(optionsContainer);

    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);

    // Close modal when clicking outside
    modalOverlay.onclick = (e) => {
        if (e.target === modalOverlay) {
            modalOverlay.remove();
        }
    };
}

// Function to change language
async function changeLanguage(language) {
    try {
        // Update translation manager
        if (window.translationManager) {
            window.translationManager.setLanguage(language);
        }

        // Save to localStorage
        localStorage.setItem('selectedLanguage', language);
        localStorage.setItem('languageSelected', 'true');

        // Save to database
        await saveUserLanguagePreference(language);

        // Close modal
        const modal = document.getElementById('language-selection-modal');
        if (modal) {
            modal.remove();
        }

        // Show success message
        showLanguageChangeSuccess(language);

        // Reload page to apply language changes
        setTimeout(() => {
            location.reload();
        }, 1500);

    } catch (error) {
        console.error('Error changing language:', error);
        // Still reload even if saving fails
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

// Function to show language change success message
function showLanguageChangeSuccess(language) {
    const successMessage = document.createElement('div');
    const messageText = t('language_changed_success');

    successMessage.textContent = messageText;
    successMessage.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg, #4ade80, #22c55e);
        color: white;
        padding: 20px 30px;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: bold;
        z-index: 100011;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        animation: fadeIn 0.3s ease;
    `;

    document.body.appendChild(successMessage);

    // Remove message after 1.2 seconds
    setTimeout(() => {
        if (successMessage.parentNode) {
            successMessage.remove();
        }
    }, 1200);
}

// Function to update language link text
function updateLanguageLinkText() {
    const languageLink = document.getElementById('language-link');
    if (languageLink) {
        const currentLang = localStorage.getItem('selectedLanguage') || 'en';
        const languageText = currentLang === 'ar' ? 'تعديل اللغة' : 'Change Language';
        const languageIcon = '🌍';
        languageLink.innerHTML = `${languageIcon} ${languageText}`;
    }
}

// --- Function called by Kotlin after onPageFinished ---
// Moved function definition higher up to ensure it's defined when called
function attachDelayedEventListeners() {
    console.log(">>> attachDelayedEventListeners() called from Kotlin.");
    const categoriesContainer = document.getElementById("categories");

    if (categoriesContainer) {
        // Check if listener already attached to prevent duplicates if onPageFinished fires multiple times
        if (!categoriesContainer.dataset.listenerAttached) {
            console.log("Attaching delegated category click listener immediately.");
            // Attach listener immediately when called
            categoriesContainer.addEventListener("click", function(event) {
                // Check if the clicked element is a category button
                if (event.target.matches('.category-btn')) {
                    const category = event.target.getAttribute('data-category');
                    if (category) {
                        console.log(`>>> Category button clicked (delegated via onPageFinished): ${category}`);
                        filterItems(category); // Call filterItems directly

                        // Update active button styling
                        document.querySelectorAll('#categories .category-btn').forEach(btn => {
                            btn.classList.remove('active-category');
                        });
                        event.target.classList.add('active-category');
                    }
                }
            });
            categoriesContainer.dataset.listenerAttached = 'true'; // Mark as attached
        } else {
             console.log("Delegated category click listener already attached.");
        }
    } else {
        console.error("Categories container not found in attachDelayedEventListeners!");
    }
}


// --- UI: Drawer ---
// Function called by onclick attribute in HTML
function toggleDrawer() {
    console.log("toggleDrawer() function called."); // Added log
    if (drawer) drawer.classList.add("active");
    if (overlay) overlay.classList.add("active");
}

// Add null checks for elements that might not exist on all pages (like search.html)
// REMOVED: Direct event listener for drawerBtn as onclick is used in HTML
// if (drawerBtn) { ... }

if (overlay) {
    overlay.addEventListener("click", () => {
        if (drawer) drawer.classList.remove("active");
        overlay.classList.remove("active");
    });
} else {
    // console.log("Drawer overlay (.drawer-overlay) not found on this page."); // Optional log
}

if (drawer) {
    drawer.addEventListener("click", (event) => {
        if (event.target.tagName === "A") {
            console.log("Link clicked: " + event.target.href);
            // Keep drawer open or close based on preference
            // drawer.classList.remove("active");
            // overlay.classList.remove("active");
        }
    });
} else {
    // console.log("Drawer (.drawer) not found on this page."); // Optional log
}

// --- UI: Modal ---
function closeModal() {
    const modal = document.getElementById('modal'); // Reverted ID
    const bottomBar = document.querySelector('.bottom-fixed-bar'); // Get bottom bar
    if (modal) {
        console.log("Closing modal."); // Debug log
        modal.style.display = 'none';
        document.documentElement.classList.remove('modal-open'); // Remove class from HTML
        document.body.classList.remove('modal-open'); // Remove class when modal closes
        if (bottomBar) bottomBar.classList.remove('hidden'); // Show bottom bar
    } else {
        console.error("Modal element not found.");
    }
}

// Close modal if clicking outside the content
window.onclick = (e) => {
    const modal = document.getElementById('modal'); // Reverted ID
    if (modal && e.target === modal) { // Check if modal exists before comparing
        console.log("Clicked outside modal content."); // Debug log
        closeModal();
    }
};

// --- Navigation ---
function navigateToSearchPage() {
    console.log("Navigate to search page called."); // Added log
    window.location.href = 'search.html'; // Assuming search.html exists
}

// --- Supabase Configuration (Centralized) ---
// Configuration moved to supabase-manager.js to prevent multiple client instances

// Enhanced function to check network connectivity with multiple fallbacks
async function checkNetworkConnectivity() {
    // Use the network handler if available
    if (typeof networkHandler !== 'undefined' && networkHandler.checkNetworkConnectivity) {
        return await networkHandler.checkNetworkConnectivity();
    }

    // Fallback to original implementation
    const testUrls = [
        'https://www.google.com/favicon.ico',
        'https://httpbin.org/status/200',
        'https://jsonplaceholder.typicode.com/posts/1'
    ];

    for (const url of testUrls) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(url, {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache',
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            return true;
        } catch (error) {
            console.warn(`Network check failed with ${url}:`, error.message);
            continue;
        }
    }

    console.error('All network connectivity checks failed');
    return false;
}

// Function to show network error message
function showNetworkError() {
    const errorContainer = document.createElement('div');
    errorContainer.id = 'network-error-message';
    errorContainer.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        z-index: 10000;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 90%;
    `;
    errorContainer.innerHTML = `
        <h3 style="margin-bottom: 10px;">مشكلة في الاتصال</h3>
        <p style="margin-bottom: 15px;">تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.</p>
        <button onclick="location.reload()" style="
            background: white;
            color: #ee5a24;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
        ">إعادة المحاولة</button>
    `;

    // Remove existing error message if any
    const existingError = document.getElementById('network-error-message');
    if (existingError) {
        existingError.remove();
    }

    document.body.appendChild(errorContainer);

    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (errorContainer.parentNode) {
            errorContainer.remove();
        }
    }, 10000);
}

// Get Supabase client from centralized manager
let supabaseClient;
let tableNames;

// Initialize client and table names
function initializeSupabase() {
    if (typeof supabaseManager !== 'undefined') {
        supabaseClient = supabaseManager.getMainClient();
        tableNames = supabaseManager.getTableNames();
        console.log('Using centralized Supabase client');
    } else {
        console.warn('Supabase manager not available, falling back to direct client');
        // Fallback for backward compatibility
        const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
        supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        tableNames = {
            MODS: 'mods',
            SUGGESTED_MODS: 'suggested_mods',
            FEATURED_ADDONS: 'featured_addons',
            FREE_ADDONS: 'free_addons',
            BANNER_ADS: 'banner_ads',
            UPDATE_NOTIFICATIONS: 'update_notifications',
            APP_ANNOUNCEMENTS: 'app_announcements',
            DRAWER_LINKS: 'drawer_links'
        };
    }
}

// Legacy table constants for backward compatibility
const UPDATE_NOTIFICATIONS_TABLE = 'update_notifications';
const APP_ANNOUNCEMENTS_TABLE = 'app_announcements';
const DRAWER_LINKS_TABLE = 'drawer_links';
const SUGGESTED_MODS_TABLE = 'suggested_mods';
const FEATURED_ADDONS_TABLE = 'featured_addons';

// Initialize app with update check
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Supabase client first
    initializeSupabase();

    // Check network connectivity first
    setTimeout(async () => {
        const isConnected = await checkNetworkConnectivity();
        if (!isConnected) {
            console.error('No network connectivity detected on app start');
            showNetworkError();
            return;
        }

        // Check for update notifications after a short delay
        fetchAndDisplayUpdateNotification();
    }, 2000); // 2 seconds delay to allow app to load first
});

// --- Cache Configuration ---
const CACHE_DURATION_MS = 60 * 60 * 1000; // 1 hour cache validity

// --- Helper Functions ---
// Function to shuffle an array (Fisher-Yates shuffle)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

function generateUserId() {
    let userId = localStorage.getItem('userId');
    if (!userId) {
        userId = `user_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        localStorage.setItem('userId', userId);
    }
    return userId;
}

// Unified function to format large numbers (Likes, Downloads, etc.)
function formatCount(count) {
    const num = Number(count); // Ensure it's a number
    if (isNaN(num)) return '0'; // Return '0' if not a valid number

    if (num >= 1_000_000) return (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + "M";
    if (num >= 1_000) return (num / 1_000).toFixed(1).replace(/\.0$/, '') + "K";
    return num.toString();
}

// Helper function to check if a mod is recent (less than 3 days old)
function isRecentMod(createdAt) {
    if (!createdAt) return false;

    try {
        const createdDate = new Date(createdAt);
        const now = new Date();
        const diffTime = Math.abs(now - createdDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays <= 3; // Consider mods less than 3 days old as "new"
    } catch (e) {
        console.error("Error checking if mod is recent:", e);
        return false;
    }
}

// --- UI Update Helper ---
// Updates the like or download count in the UI for a specific mod
function updateCountUI(modId, type, count) {
    const formattedCount = formatCount(count);
    // This selector targets elements with class e.g. "download-count" AND the attribute data-mod-id="<modId>"
    // It should find the count spans in both the cards (list/grid) and the modal footer button.
    const selector = `.${type}-count[data-mod-id="${modId}"]`;

    console.log(`Updating UI elements matching selector: ${selector} to count: ${formattedCount}`); // Debug log

    const elementsToUpdate = document.querySelectorAll(selector);

    if (elementsToUpdate.length === 0) {
        console.warn(`No UI elements found for selector: ${selector}`);
    } else {
        elementsToUpdate.forEach(elem => {
            console.log(`Updating element:`, elem); // Debug log
            elem.textContent = formattedCount;
        });
    }

    // No separate modal logic needed here, as the querySelectorAll should cover it.
}

// --- Banner Ad Variables ---
const BANNER_ADS_TABLE = 'banner_ads';
let bannerAds = [];
let currentBannerIndex = 0;
let bannerInterval = null;

// --- Banner Ad Functions ---
async function fetchBannerAds() {
    const cacheKey = 'banner_ads';
    const cachedData = localStorage.getItem(cacheKey);

    if (cachedData) {
        try {
            const { timestamp, data } = JSON.parse(cachedData);
            if (Date.now() - timestamp < CACHE_DURATION_MS) {
                console.log(`Using cached banner ads data`);
                return data;
            } else {
                console.log(`Cache expired for banner ads`);
                localStorage.removeItem(cacheKey);
            }
        } catch (e) {
            console.error("Error parsing cached banner ads data:", e);
            localStorage.removeItem(cacheKey);
        }
    }

    console.log(`Fetching fresh banner ads from Supabase`);
    try {
        const { data, error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('*')
            .eq('is_active', true)
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error fetching banner ads:', error);
            return null;
        }

        if (!data || data.length === 0) {
            console.log('No banner ads found.');
            return [];
        }

        // Cache the banner ads
        try {
            const dataToCache = { timestamp: Date.now(), data: data };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`Banner ads data cached`);
        } catch (e) {
            console.error("Error storing banner ads data in localStorage:", e);
        }

        return data;
    } catch (error) {
        console.error('Unexpected error in fetchBannerAds:', error);
        return null;
    }
}

function displayBannerAds() {
    if (!bannerAds || bannerAds.length === 0) {
        console.log('No banner ads to display');
        return;
    }

    // Create banner container if it doesn't exist
    let bannerContainer = document.getElementById('banner-ad-container');
    if (!bannerContainer) {
        bannerContainer = document.createElement('div');
        bannerContainer.id = 'banner-ad-container';
        bannerContainer.className = 'banner-ad-container';

        // Create a wrapper div for better spacing
        const bannerWrapper = document.createElement('div');
        bannerWrapper.id = 'banner-ad-wrapper';
        bannerWrapper.style.cssText = 'padding: 10px 0; width: 100%;';
        bannerWrapper.appendChild(bannerContainer);

        // Insert before news section
        const newsSection = document.getElementById('news-section');
        if (newsSection && newsSection.parentNode) {
            newsSection.parentNode.insertBefore(bannerWrapper, newsSection);
        } else {
            console.error('Could not find news section to insert banner before');
            return;
        }
    }

    // Clear existing content
    bannerContainer.innerHTML = '';

    // Create slides for each banner
    bannerAds.forEach((ad, index) => {
        const slide = document.createElement('div');
        slide.className = `banner-ad-slide ${index === 0 ? 'active' : ''}`;
        slide.setAttribute('data-ad-id', ad.id);

        const img = document.createElement('img');
        img.className = 'banner-ad-image';
        img.src = ad.image_url;
        img.alt = ad.title || 'Banner Advertisement';
        img.onclick = () => handleBannerClick(ad);

        // Add title overlay if title exists
        if (ad.title) {
            const titleOverlay = document.createElement('div');
            titleOverlay.className = 'banner-ad-title-overlay';
            titleOverlay.textContent = ad.title;
            titleOverlay.style.cssText = `
                position: absolute;
                bottom: 10px;
                left: 10px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                z-index: 2;
            `;
            slide.appendChild(titleOverlay);
        }

        slide.appendChild(img);
        bannerContainer.appendChild(slide);
    });

    // Start rotation if more than one banner
    if (bannerAds.length > 1) {
        startBannerRotation();
    }
}

function startBannerRotation() {
    // Clear any existing interval
    if (bannerInterval) {
        clearInterval(bannerInterval);
    }

    // Set interval to rotate banners every 5 seconds
    bannerInterval = setInterval(() => {
        const slides = document.querySelectorAll('.banner-ad-slide');
        if (slides.length <= 1) return;

        // Hide current slide
        slides[currentBannerIndex].classList.remove('active');

        // Move to next slide
        currentBannerIndex = (currentBannerIndex + 1) % slides.length;

        // Show new slide
        slides[currentBannerIndex].classList.add('active');
    }, 5000); // 5 seconds
}

// Banner click handler - Updated for subscription campaigns
function handleBannerClick(ad) {
    if (ad.banner_type === 'subscription' && ad.campaign_id) {
        // Show subscription campaign modal
        showSubscriptionCampaignModal(ad.campaign_id);
    } else {
        // Show regular banner modal
        showBannerAdModal(ad);
    }
}

function showBannerAdModal(ad) {
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'banner-ad-modal';

    // Create modal content
    modal.innerHTML = `
        <div class="banner-ad-modal-content">
            <button class="banner-ad-modal-close">&times;</button>
            <img class="banner-ad-modal-image" src="${ad.image_url}" alt="${ad.title || 'Advertisement'}">
            <h2 class="banner-ad-modal-title">${ad.title || 'Advertisement'}</h2>
            <p class="banner-ad-modal-description">${ad.description || ''}</p>
            <button class="banner-ad-modal-button">متابعة</button>
        </div>
    `;

    // Add event listeners
    modal.querySelector('.banner-ad-modal-close').addEventListener('click', () => {
        modal.remove();
    });

    modal.querySelector('.banner-ad-modal-button').addEventListener('click', () => {
        if (ad.click_url) {
            // Open URL in new tab
            window.open(ad.click_url, '_blank');
        }
        modal.remove();
    });

    // Close modal when clicking outside content
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });

    // Add to document
    document.body.appendChild(modal);
}

// Show subscription campaign modal
async function showSubscriptionCampaignModal(campaignId) {
    try {
        // Fetch campaign details
        const { data: campaign, error } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('*')
            .eq('id', campaignId)
            .eq('is_active', true)
            .single();

        if (error || !campaign) {
            console.error('Error fetching campaign:', error);
            return;
        }

        // Check if user already has an active subscription
        const userId = generateUserId();
        const { data: existingSubscription } = await supabaseClient
            .from('user_subscriptions')
            .select('*')
            .eq('user_id', userId)
            .eq('campaign_id', campaignId)
            .eq('status', 'active')
            .single();

        if (existingSubscription) {
            showMessage('لديك اشتراك مجاني نشط بالفعل!', 'success');
            return;
        }

        // Create subscription modal
        const modal = document.createElement('div');
        modal.className = 'subscription-campaign-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        modal.innerHTML = `
            <div class="subscription-modal-content" style="
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border-radius: 20px;
                padding: 30px;
                max-width: 90%;
                max-height: 90%;
                overflow-y: auto;
                text-align: center;
                border: 2px solid #ffd700;
                box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
                position: relative;
            ">
                <button class="close-subscription-modal" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    z-index: 10001;
                ">&times;</button>

                ${campaign.popup_image_url ? `
                    <img src="${campaign.popup_image_url}" style="
                        width: 100%;
                        max-width: 200px;
                        border-radius: 15px;
                        margin-bottom: 20px;
                        border: 2px solid #ffd700;
                    ">
                ` : ''}

                <h2 style="
                    color: #ffd700;
                    margin-bottom: 15px;
                    font-size: 1.8rem;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
                ">${campaign.title_ar}</h2>

                <p style="
                    color: white;
                    margin-bottom: 20px;
                    line-height: 1.6;
                    font-size: 1.1rem;
                ">${campaign.description_ar}</p>

                <div style="
                    background: rgba(255, 215, 0, 0.1);
                    border-radius: 15px;
                    padding: 20px;
                    margin-bottom: 25px;
                    border: 1px solid rgba(255, 215, 0, 0.3);
                ">
                    <h3 style="color: #ffd700; margin-bottom: 10px;">
                        ⭐ مدة الاشتراك المجاني
                    </h3>
                    <p style="color: white; font-size: 1.2rem; font-weight: bold;">
                        ${campaign.subscription_duration_days} يوم
                    </p>
                </div>

                <div style="
                    display: flex;
                    gap: 15px;
                    justify-content: center;
                    flex-wrap: wrap;
                ">
                    <button class="start-tasks-btn" style="
                        background: linear-gradient(45deg, #ffd700, #ffcc00);
                        color: black;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                        transition: all 0.3s ease;
                        box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
                    ">
                        🚀 ابدأ المهام
                    </button>

                    <button class="cancel-subscription-btn" style="
                        background: rgba(255, 255, 255, 0.1);
                        color: white;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                        transition: all 0.3s ease;
                    ">
                        إلغاء
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-subscription-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.cancel-subscription-btn').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.start-tasks-btn').addEventListener('click', () => {
            modal.remove();
            showTasksModal(campaignId);
        });

        // Close modal when clicking outside content
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Add to document
        document.body.appendChild(modal);

    } catch (error) {
        console.error('Error showing subscription campaign modal:', error);
        showMessage('حدث خطأ أثناء تحميل تفاصيل الحملة', 'error');
    }
}

// Show tasks modal for subscription campaign
async function showTasksModal(campaignId) {
    try {
        // Fetch campaign tasks
        const { data: tasks, error } = await supabaseClient
            .from('campaign_tasks')
            .select(`
                *,
                task_types (
                    display_name_ar,
                    display_name_en,
                    icon
                )
            `)
            .eq('campaign_id', campaignId)
            .eq('is_required', true)
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error fetching campaign tasks:', error);
            showMessage('حدث خطأ أثناء تحميل المهام', 'error');
            return;
        }

        if (!tasks || tasks.length === 0) {
            showMessage('لا توجد مهام متاحة لهذه الحملة', 'warning');
            return;
        }

        // Create tasks modal
        const modal = document.createElement('div');
        modal.className = 'tasks-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        const tasksHTML = tasks.map((task, index) => `
            <div class="task-item" style="
                background: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 15px;
                border-left: 4px solid #ffd700;
                transition: all 0.3s ease;
            ">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <i class="${task.task_types?.icon || 'fas fa-tasks'}" style="
                        color: #ffd700;
                        font-size: 1.5rem;
                        margin-left: 15px;
                    "></i>
                    <h4 style="color: white; margin: 0; flex: 1;">
                        ${index + 1}. ${task.title_ar}
                    </h4>
                    <span class="task-status" style="
                        background: rgba(255, 215, 0, 0.2);
                        color: #ffd700;
                        padding: 5px 10px;
                        border-radius: 20px;
                        font-size: 0.8rem;
                        font-weight: bold;
                    ">
                        مطلوب
                    </span>
                </div>

                ${task.description_ar ? `
                    <p style="color: #ccc; margin-bottom: 15px; line-height: 1.5;">
                        ${task.description_ar}
                    </p>
                ` : ''}

                <button class="complete-task-btn" data-task-id="${task.id}" style="
                    background: linear-gradient(45deg, #ffd700, #ffcc00);
                    color: black;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 20px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">
                    ${task.task_type === 'app_download' ? 'تحميل التطبيق' :
                      task.task_type === 'mod_download' ? 'تحميل المود' :
                      task.task_type === 'telegram_subscribe' ? 'اشتراك في التيليجرام' :
                      task.task_type === 'youtube_subscribe' ? 'اشتراك في اليوتيوب' :
                      'إكمال المهمة'}
                </button>
            </div>
        `).join('');

        modal.innerHTML = `
            <div class="tasks-modal-content" style="
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border-radius: 20px;
                padding: 30px;
                max-width: 90%;
                max-height: 90%;
                overflow-y: auto;
                border: 2px solid #ffd700;
                box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
                position: relative;
            ">
                <button class="close-tasks-modal" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    z-index: 10001;
                ">&times;</button>

                <h2 style="
                    color: #ffd700;
                    text-align: center;
                    margin-bottom: 25px;
                    font-size: 1.8rem;
                ">🎯 المهام المطلوبة</h2>

                <p style="
                    color: white;
                    text-align: center;
                    margin-bottom: 30px;
                    line-height: 1.6;
                ">أكمل جميع المهام التالية للحصول على الاشتراك المجاني</p>

                <div class="tasks-list">
                    ${tasksHTML}
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="check-completion-btn" style="
                        background: linear-gradient(45deg, #22c55e, #16a34a);
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                        margin-left: 15px;
                    ">
                        ✅ تحقق من الإكمال
                    </button>

                    <button class="close-tasks-btn" style="
                        background: rgba(255, 255, 255, 0.1);
                        color: white;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-weight: bold;
                        cursor: pointer;
                        font-size: 1.1rem;
                    ">
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-tasks-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('.close-tasks-btn').addEventListener('click', () => {
            modal.remove();
        });

        // Add listeners for task completion buttons
        modal.querySelectorAll('.complete-task-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.getAttribute('data-task-id');
                const task = tasks.find(t => t.id === taskId);
                if (task) {
                    handleTaskCompletion(task);
                }
            });
        });

        modal.querySelector('.check-completion-btn').addEventListener('click', () => {
            checkAllTasksCompletion(campaignId);
        });

        // Close modal when clicking outside content
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Add to document
        document.body.appendChild(modal);

    } catch (error) {
        console.error('Error showing tasks modal:', error);
        showMessage('حدث خطأ أثناء تحميل المهام', 'error');
    }
}

// Handle task completion with smart verification
async function handleTaskCompletion(task) {
    try {
        // Initialize smart verification system if not already done
        if (!window.smartVerificationSystem) {
            window.smartVerificationSystem = new SmartVerificationSystem(supabaseClient);
        }

        // Use smart verification system
        const userId = generateUserId();
        const result = await window.smartVerificationSystem.handleTaskClick(task.id, task);

        if (result.success) {
            // Task verified successfully
            console.log('Task verified successfully:', result);
        } else {
            // Task verification failed
            console.log('Task verification failed:', result);

            // Fallback to manual completion for backward compatibility
            if (task.target_url) {
                if (typeof Android !== 'undefined' && Android.openUrl) {
                    Android.openUrl(task.target_url);
                } else {
                    window.open(task.target_url, '_blank');
                }
                markTaskAsCompleted(task.id);
            }
        }
    } catch (error) {
        console.error('Error in smart task completion:', error);

        // Fallback to original method
        if (task.target_url) {
            if (typeof Android !== 'undefined' && Android.openUrl) {
                Android.openUrl(task.target_url);
            } else {
                window.open(task.target_url, '_blank');
            }
            markTaskAsCompleted(task.id);
        }
    }
}

// Mark task as completed
async function markTaskAsCompleted(taskId) {
    try {
        const userId = generateUserId();

        const { error } = await supabaseClient
            .from('user_task_progress')
            .upsert({
                user_id: userId,
                task_id: taskId,
                status: 'completed',
                completed_at: new Date().toISOString()
            });

        if (error) {
            console.error('Error marking task as completed:', error);
        } else {
            showMessage('تم إكمال المهمة بنجاح!', 'success');
        }
    } catch (error) {
        console.error('Error in markTaskAsCompleted:', error);
    }
}

// Check if all tasks are completed with smart verification
async function checkAllTasksCompletion(campaignId) {
    try {
        // Initialize smart verification system if not already done
        if (!window.smartVerificationSystem) {
            window.smartVerificationSystem = new SmartVerificationSystem(supabaseClient);
        }

        const userId = generateUserId();

        // Use smart verification system for comprehensive check
        const result = await window.smartVerificationSystem.checkAllTasksCompletion(userId, campaignId);

        if (result.success) {
            // All tasks completed and subscription activated
            console.log('Subscription activated successfully:', result);
        } else {
            // Some tasks still need completion
            if (result.remaining_tasks) {
                showMessage(`يجب إكمال ${result.remaining_tasks} مهمة أخرى للحصول على الاشتراك المجاني`, 'warning');
            } else if (result.error) {
                showMessage(`خطأ: ${result.error}`, 'error');
            }
        }

    } catch (error) {
        console.error('Error in smart task completion check:', error);

        // Fallback to original method
        try {
            const userId = generateUserId();

            // Get all required tasks for this campaign
            const { data: requiredTasks, error: tasksError } = await supabaseClient
                .from('campaign_tasks')
                .select('id')
                .eq('campaign_id', campaignId)
                .eq('is_required', true);

            if (tasksError) {
                console.error('Error fetching required tasks:', tasksError);
                return;
            }

            // Get user's completed tasks (check for both 'completed' and 'verified' status)
            const { data: completedTasks, error: progressError } = await supabaseClient
                .from('user_task_progress')
                .select('task_id')
                .eq('user_id', userId)
                .in('status', ['completed', 'verified'])
                .in('task_id', requiredTasks.map(t => t.id));

            if (progressError) {
                console.error('Error fetching user progress:', progressError);
                return;
            }

            if (completedTasks.length === requiredTasks.length) {
                // All tasks completed - activate subscription
                await activateUserSubscription(userId, campaignId);
            } else {
                const remaining = requiredTasks.length - completedTasks.length;
                showMessage(`يجب إكمال ${remaining} مهمة أخرى للحصول على الاشتراك المجاني`, 'warning');
            }

        } catch (fallbackError) {
            console.error('Error in fallback task completion check:', fallbackError);
            showMessage('حدث خطأ أثناء التحقق من المهام', 'error');
        }
    }
}

// Activate user subscription
async function activateUserSubscription(userId, campaignId) {
    try {
        // Get campaign details
        const { data: campaign, error: campaignError } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('subscription_duration_days')
            .eq('id', campaignId)
            .single();

        if (campaignError) {
            console.error('Error fetching campaign:', campaignError);
            return;
        }

        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + campaign.subscription_duration_days);

        // Create user subscription
        const { error: subscriptionError } = await supabaseClient
            .from('user_subscriptions')
            .upsert({
                user_id: userId,
                campaign_id: campaignId,
                status: 'active',
                started_at: startDate.toISOString(),
                expires_at: endDate.toISOString()
            });

        if (subscriptionError) {
            console.error('Error creating subscription:', subscriptionError);
            showMessage('حدث خطأ أثناء تفعيل الاشتراك', 'error');
            return;
        }

        // Show success message
        showMessage(`🎉 تهانينا! تم تفعيل اشتراكك المجاني لمدة ${campaign.subscription_duration_days} يوم`, 'success');

        // Close all modals
        document.querySelectorAll('.tasks-modal, .subscription-campaign-modal').forEach(modal => {
            modal.remove();
        });

    } catch (error) {
        console.error('Error activating subscription:', error);
        showMessage('حدث خطأ أثناء تفعيل الاشتراك', 'error');
    }
}

// Show message function
function showMessage(message, type = 'info') {
    // Remove existing messages
    document.querySelectorAll('.app-message').forEach(msg => msg.remove());

    const messageDiv = document.createElement('div');
    messageDiv.className = 'app-message';
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? 'linear-gradient(45deg, #22c55e, #16a34a)' :
                     type === 'error' ? 'linear-gradient(45deg, #ef4444, #dc2626)' :
                     type === 'warning' ? 'linear-gradient(45deg, #f59e0b, #d97706)' :
                     'linear-gradient(45deg, #3b82f6, #2563eb)'};
        color: white;
        padding: 15px 25px;
        border-radius: 25px;
        font-weight: bold;
        z-index: 10002;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        animation: slideInDown 0.3s ease-out;
    `;

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 5000);
}

// --- Fetch Featured Addons ---
async function fetchFeaturedAddons() {
    const cacheKey = 'featured_addons';
    const cachedData = localStorage.getItem(cacheKey);

    // Check if we have valid cached data
    if (cachedData) {
        try {
            const parsedData = JSON.parse(cachedData);
            // Check if the cache is still valid (less than 1 hour old)
            if (Date.now() - parsedData.timestamp < CACHE_DURATION_MS) {
                console.log('Using cached featured addons data');
                return parsedData.data;
            }
        } catch (e) {
            console.error('Error parsing cached featured addons data:', e);
            // Continue to fetch fresh data if there's an error parsing the cache
        }
    }

    console.log('Fetching fresh featured addons data from Supabase');
    try {
        // First, check if the featured_addons table exists
        const { error: tableCheckError } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .select('id', { count: 'exact', head: true })
            .limit(1);

        if (tableCheckError) {
            console.warn('Featured Addons table not found or not accessible:', tableCheckError.message);
            // Return empty array instead of null to avoid breaking the UI
            return [];
        }

        // Fetch the featured addon IDs from the featured_addons table
        const { data: featuredEntries, error: featuredError } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .select('*')
            .eq('is_active', true);

        if (featuredError) {
            console.error('Error fetching featured addons:', featuredError);
            return [];
        }

        if (!featuredEntries || featuredEntries.length === 0) {
            console.log('No featured addons found');
            return [];
        }

        // Extract the mod IDs
        const modIds = featuredEntries.map(entry => entry.mod_id);

        // Fetch the actual mod details from the mods table
        const { data: mods, error: modsError } = await supabaseClient
            .from('mods')
            .select('*')
            .in('id', modIds);

        if (modsError) {
            console.error('Error fetching mod details for featured addons:', modsError);
            return [];
        }

        if (!mods || mods.length === 0) {
            console.log('No mod details found for the featured addon IDs');
            return [];
        }

        // Cache the data
        try {
            const dataToCache = {
                timestamp: Date.now(),
                data: mods
            };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log('Featured addons data cached');
        } catch (e) {
            console.error('Error storing featured addons data in localStorage:', e);
        }

        return mods;
    } catch (error) {
        console.error('Unexpected error in fetchFeaturedAddons:', error);
        return [];
    }
}

// --- Supabase Data Fetching with Caching ---
async function fetchModsFromSupabase(category = 'All', sortBy = 'created_at', ascending = false, limit = null) { // Added limit parameter
    const cacheKey = `mods_${category}_${sortBy}_${ascending}_${limit || 'none'}`; // Added limit to cache key
    const cachedData = localStorage.getItem(cacheKey);

    if (cachedData) {
        try {
            const { timestamp, data } = JSON.parse(cachedData);
            if (Date.now() - timestamp < CACHE_DURATION_MS) {
                console.log(`Using cached data for key: ${cacheKey}`);
                return data; // Return valid cached data
            } else {
                console.log(`Cache expired for key: ${cacheKey}`);
                localStorage.removeItem(cacheKey); // Remove expired cache
            }
        } catch (e) {
            console.error("Error parsing cached data:", e);
            localStorage.removeItem(cacheKey); // Remove corrupted cache
        }
    }

    console.log(`Fetching fresh data from Supabase for key: ${cacheKey}`);
    try {
        let query = supabaseClient
            .from('mods')
            .select('*');

        // Filter by category if it's not 'All', 'News', or 'Suggested' (handled separately)
        if (category !== 'All' && category !== 'News' && category !== 'Suggested') {
            query = query.eq('category', category);
        }

        // Apply sorting only if sortBy is provided and category is not 'Suggested'
        // For 'Suggested', sorting is handled by `fetchSuggestedModsFromSupabase`
        if (category !== 'Suggested' && sortBy && typeof sortBy === 'string' && sortBy.trim() !== '') {
            query = query.order(sortBy, { ascending: ascending });
        } else if (category !== 'Suggested') {
            console.log(`No sortBy provided for category "${category}" or it's 'Suggested', fetching in default/specific order.`);
        }


        // Apply limit if provided
        if (limit && typeof limit === 'number' && limit > 0) {
            query = query.limit(limit);
            console.log(`Applying limit: ${limit}`); // Debug log
        }

        const { data: items, error } = await query;

        if (error) {
            console.error(`Error fetching data from Supabase for category "${category}" sorted by ${sortBy}:`, error);

            // Enhanced network error detection
            const isNetworkError = error.message && (
                error.message.includes('Failed to fetch') ||
                error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                error.message.includes('ERR_NETWORK') ||
                error.message.includes('ERR_INTERNET_DISCONNECTED') ||
                error.message.includes('TypeError: Failed to fetch') ||
                error.message.includes('NetworkError') ||
                error.code === 'PGRST301' || // Supabase connection error
                error.code === 'PGRST116'    // Supabase timeout error
            );

            if (isNetworkError) {
                console.log('Network error detected, showing error message...');

                // Use network handler if available
                if (typeof networkHandler !== 'undefined') {
                    networkHandler.showNetworkStatus('مشكلة في الاتصال بالخادم', 'error', 5000);
                    networkHandler.isOnline = false;
                    networkHandler.showOfflineMode();
                } else {
                    showNetworkError();
                }

                // Try to return cached data
                try {
                    const cacheKey = `mods_${category}_${sortBy}`;
                    const cachedDataStr = localStorage.getItem(cacheKey);
                    if (cachedDataStr) {
                        const cachedDataObj = JSON.parse(cachedDataStr);
                        if (cachedDataObj && cachedDataObj.data && cachedDataObj.data.length > 0) {
                            console.log(`Using cached data for category "${category}"`);
                            return cachedDataObj.data;
                        }
                    }
                } catch (cacheError) {
                    console.warn('Error accessing cached data:', cacheError);
                }
            }

            return null; // Return null on Supabase error
        }

        if (!items) {
            console.log(`No items found in Supabase for category "${category}".`);
            return []; // Return empty array if no items found
        }

        // Store fetched data in cache
        try {
            const dataToCache = {
                timestamp: Date.now(),
                data: items
            };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`Data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error("Error storing data in localStorage:", e);
            // Consider clearing some old cache if storage is full
        }

        return items; // Return the freshly fetched items

    } catch (error) {
        console.error(`Unexpected error fetching/caching data for category "${category}":`, error);
        // Check if it's a network-related error
        if (error.message && (error.message.includes('Failed to fetch') ||
                              error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                              error.message.includes('NetworkError') ||
                              error.message.includes('TypeError: Failed to fetch'))) {
            showNetworkError();
        }
        return null; // Return null on unexpected error
    }
}


// --- Supabase Data Interaction ---

async function toggleLike(modId, modName, button) {
    console.log(`>>> toggleLike called for mod ID: ${modId}`); // Added entry log
    const userId = generateUserId();
    console.log(`Toggling like for mod ID: ${modId} by user: ${userId}`); // Debug log

    try {
        const { data: rpcResult, error: rpcError } = await supabaseClient.rpc('toggle_like', {
            mod_id_in: modId,
            user_id_in: userId
        });

        if (rpcError) {
            console.error("Error calling toggle_like RPC:", rpcError);
            alert("Error updating like.");
            return;
        }
        if (!rpcResult) {
            console.error("RPC function 'toggle_like' did not return expected data.");
            alert("Unexpected error updating like.");
            return;
        }

        const { liked, new_count: finalLikes } = rpcResult;
        console.log(`Like status for ${modId}: ${liked}, New count: ${finalLikes}`);

        updateCountUI(modId, 'like', finalLikes);

        document.querySelectorAll(`.like-button[onclick*="'${modId}'"]`).forEach(btn => {
             const heartIcon = btn.querySelector(".heart-icon");
             if (heartIcon) {
                 if (liked) {
                    heartIcon.classList.remove("animate-like");
                    void heartIcon.offsetWidth;
                    heartIcon.classList.add("animate-like");
                    heartIcon.style.color = "red";
                    btn.classList.add("liked");
                 } else {
                    heartIcon.classList.remove("animate-like");
                    heartIcon.style.color = "#f1f5f9";
                    btn.classList.remove("liked");
                 }
             }
        });

    } catch (error) {
        console.error("Error in toggleLike function:", error);
        alert("An unexpected error occurred.");
    }
}

// --- Function called by Android to update download progress ---
// Now accepts an additional 'status' string parameter
function updateDownloadProgress(modId, progress, status) {
    console.log(`>>> updateDownloadProgress called for mod ID: ${modId}, Progress: ${progress}%, Status: ${status}`);
    if (!modId || progress === undefined || progress === null) {
        console.error("updateDownloadProgress called with invalid arguments", modId, progress, status);
        return;
    }

    // Find the progress bar and text elements within the modal (assuming modal is open)
    // We target elements specifically associated with the modId
    const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
    const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
    const downloadButtonText = document.querySelector(`.download-btn[data-mod-id="${modId}"] .download-btn-text`); // Find text inside button

    if (progressBar && progressText) {
        const percentage = Math.max(0, Math.min(100, parseInt(progress, 10))); // Clamp between 0-100
        progressBar.style.width = `${percentage}%`;

        // Update the text content to include the status if provided
        if (status && status.trim() !== "") {
            progressText.textContent = `${status} (${percentage}%)`;
        } else {
            // Fallback to just percentage if status is empty or null
            progressText.textContent = `${percentage}%`;
        }


        // Optionally hide the main "Download" text inside the button while progress shows
        if (downloadButtonText) {
            downloadButtonText.style.display = 'none';
        }

        // Make sure the container is visible
        const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
        if (progressContainer) {
            progressContainer.style.display = 'flex'; // Or 'block' depending on styling
        }
    } else {
        // This might happen if the modal is closed while download is in progress
        console.warn(`Progress elements not found for mod ID: ${modId} in updateDownloadProgress. Modal might be closed.`);
    }
}


// --- Function called by Android after successful download to mark it ---
function markModAsDownloaded(modId) {
    console.log(`>>> markModAsDownloaded called for mod ID: ${modId}`); // Added log
    if (!modId) {
        console.error("markModAsDownloaded called with invalid modId");
        return;
    }
    const key = `downloaded_${modId}`;
    try {
        localStorage.setItem(key, 'true');
        console.log(`Mod ${modId} marked as downloaded in localStorage.`);

        // --- Update UI to reflect downloaded state ---
        const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
        const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
        const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
        const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
        const downloadButtonText = downloadButton?.querySelector('.download-btn-text'); // Find text inside button

        if (progressContainer) {
            progressContainer.style.display = 'none'; // Hide progress container
        }
        if (progressBar) {
            progressBar.style.width = '0%'; // Reset progress bar
        }
        if (progressText) {
            progressText.textContent = ''; // Clear progress text
        }
        if (downloadButtonText) {
            downloadButtonText.textContent = 'Open'; // Change button text to "Open"
            downloadButtonText.style.display = 'inline'; // Ensure text is visible
        }
         if (downloadButton) {
            // Add class to change style for downloaded state
            downloadButton.classList.add('downloaded');
            // REMOVED: Do not replace the onclick handler.
            // The original handleDownload function will correctly route to openDownloadedMod
            // when it checks localStorage again.
            downloadButton.disabled = false; // Ensure button is enabled after download
        }
        // --- End UI Update ---

    } catch (e) {
        console.error(`Error setting localStorage item for key ${key}:`, e);
    }
}

async function handleDownload(modId, modName, downloadLink) {
    console.log(`>>> handleDownload called for mod ID: ${modId}, Name: ${modName}`);
    const isAlreadyDownloaded = localStorage.getItem(`downloaded_${modId}`) === 'true';

    // --- Find UI elements for progress ---
    const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
    const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
    const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
    const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
    const downloadButtonText = downloadButton?.querySelector('.download-btn-text');
    // --- End Find UI elements ---


    if (isAlreadyDownloaded) {
        console.log(`Mod ${modId} (${modName}) is already downloaded. Attempting to open.`);
        // Ensure button text is 'Open' if modal was re-opened for an already downloaded mod
        if (downloadButtonText) downloadButtonText.textContent = 'Open';
        if (downloadButton) downloadButton.classList.add('downloaded');

        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.openDownloadedMod) {
            // The onclick handler should already be set correctly by markModAsDownloaded
            // or during modal creation if it was already downloaded.
            // Call the native function to open the downloaded mod file.
            AndroidInterface.openDownloadedMod(modId, modName || 'mod', downloadLink);
            console.log(`Called AndroidInterface.openDownloadedMod for mod ID: ${modId}`);
        } else {
            console.warn('AndroidInterface.openDownloadedMod not found. Cannot open file.');
            alert('Cannot open the already downloaded file. Please find it in your downloads folder.');
        }
        return; // Stop here if already downloaded
    }

    // --- Check if download is already in progress (progress container is visible) ---
    if (progressContainer && progressContainer.style.display !== 'none') {
        console.log(`Download for mod ${modId} (${modName}) is already in progress. Ignoring click.`);
        return; // Do nothing if progress is already showing
    }
    // --- End Check ---

    // --- If not downloaded and not in progress, proceed with download ---
    console.log(`Mod ${modId} (${modName}) not downloaded yet. Starting download process.`);

    // --- Show and initialize progress UI ---
    if (progressContainer) progressContainer.style.display = 'flex'; // Show container
    if (progressBar) progressBar.style.width = '0%'; // Reset bar
    if (progressText) progressText.textContent = '0%'; // Set initial text
    if (downloadButtonText) downloadButtonText.style.display = 'none'; // Hide "Download" text
    // REMOVED: Do not disable the button; the overlay indicates activity.
    // if (downloadButton) downloadButton.disabled = true;
    // --- End Progress UI Init ---

    downloadModFile(modId, modName, downloadLink);
}

// Helper function to contain the actual download logic
function downloadModFile(modId, modName, downloadLink) {
    const encodedLink = encodeURI(downloadLink || '');
    if (!encodedLink || !encodedLink.startsWith('http')) {
        console.warn('Invalid or missing download link for mod:', modName);
        alert('Download link is unavailable or invalid!');
        // --- Reset UI if download fails early ---
        resetDownloadButtonUI(modId);
        // --- End Reset UI ---
        return;
    }

    if (typeof AndroidInterface !== 'undefined' && AndroidInterface.startModDownload) {
        console.log(`Calling native download for ${modName} from ${encodedLink}`);
        try {
            // Use the original function that might trigger an ad request first
            AndroidInterface.requestModDownloadWithAd(modId, modName || 'mod', encodedLink); // Re-enabled ad request
            // Or call startModDownload directly if requestModDownloadWithAd handles the check
            // AndroidInterface.startModDownload(modId, modName || 'mod', encodedLink); // Disabled direct download
        } catch (error) {
            console.error("Error calling AndroidInterface download function:", error);
            alert('Error starting download.');
            // --- Reset UI on error ---
            resetDownloadButtonUI(modId);
            // --- End Reset UI ---
        }
    } else {
        console.warn('AndroidInterface not found. Opening link in new tab as fallback.');
        alert('Automatic download cannot be started. The link will be opened in the browser.');
        // --- Reset UI if using fallback ---
        resetDownloadButtonUI(modId);
        // --- End Reset UI ---
        window.open(encodedLink, '_blank');
    }

    // REMOVED: Immediate RPC call to increment downloads.
    // The native code will now handle incrementing after successful download
    // and call androidDidIncrementDownloadCount to update the UI.
}

// --- Helper to reset button UI if download fails or is cancelled ---
function resetDownloadButtonUI(modId) {
    const progressContainer = document.querySelector(`.download-progress-container[data-mod-id="${modId}"]`);
    const progressBar = document.querySelector(`.download-progress-bar[data-mod-id="${modId}"]`);
    const progressText = document.querySelector(`.download-progress-text[data-mod-id="${modId}"]`);
    const downloadButton = document.querySelector(`.download-btn[data-mod-id="${modId}"]`);
    const downloadButtonText = downloadButton?.querySelector('.download-btn-text');

    if (progressContainer) progressContainer.style.display = 'none';
    if (progressBar) progressBar.style.width = '0%';
    if (progressText) progressText.textContent = '';
    if (downloadButtonText) {
        downloadButtonText.textContent = 'Download'; // Restore original text
        downloadButtonText.style.display = 'inline';
    }
    if (downloadButton) {
        downloadButton.disabled = false; // Ensure button is re-enabled
        downloadButton.classList.remove('downloaded'); // Ensure downloaded state is removed
        // No need to restore onclick, as we didn't replace it.
        // The original handleDownload attached in showModal remains.
        // We need the original modName and downloadLink here.
        // This suggests we might need to store them temporarily or fetch them again.
        // For now, let's assume they are available in scope or we fetch item data again.
        // A simpler approach might be to just re-enable the button and let the user click again.
        // Let's stick to re-enabling for now. The original onclick might still be attached
        // if we didn't overwrite it, just disabled the button.
    }
}


// --- Native Callback Handler ---
// Called by Android native code after a download is successfully completed AND counted (once per user/mod)
function androidDidIncrementDownloadCount(modId) {
    console.log(`Native code confirmed download increment for mod ID: ${modId}`);

    // Find *one* of the count elements for this mod to get the current value
    const countElement = document.querySelector(`.download-count[data-mod-id="${modId}"]`);

    if (countElement) {
        const currentCountText = countElement.textContent || '0';
        // Attempt to parse the current count, handling K/M suffixes if necessary
        let currentCount = 0;
        if (currentCountText.endsWith('K')) {
            currentCount = parseFloat(currentCountText.replace('K', '')) * 1000;
        } else if (currentCountText.endsWith('M')) {
            currentCount = parseFloat(currentCountText.replace('M', '')) * 1000000;
        } else {
            currentCount = parseInt(currentCountText, 10);
        }

        if (!isNaN(currentCount)) {
            const newCount = currentCount + 1;
            console.log(`Updating UI for mod ${modId} to new download count: ${newCount}`);
            // Use the existing UI update function
            updateCountUI(modId, 'download', newCount);
        } else {
            console.warn(`Could not parse current download count '${currentCountText}' for mod ${modId}. Updating UI to '1'.`);
            // Fallback if parsing fails (e.g., initial state was not a number)
             updateCountUI(modId, 'download', 1);
        }
    } else {
        console.warn(`Could not find download count element for mod ID: ${modId} in script.js to update count.`);
    }
}

// --- Native Callback Handler for Persistence ---
// Called by Android native code after a download is successfully counted locally,
// instructing the JS to update the count in the database.
async function androidShouldPersistDownloadIncrement(modId) {
    console.log(`Native code requested persistence for download increment for mod ID: ${modId}`);

    // التحقق من وجود supabaseClient
    if (!supabaseClient) {
        console.warn('Supabase client not available, skipping download increment persistence');
        return;
    }

    try {
        // Call the Supabase RPC function to increment the count, using the correct parameter name
        const { error: rpcError } = await supabaseClient.rpc('increment_downloads', { mod_id_in: modId });

        if (rpcError) {
            // Log the error, providing more detail if possible
            console.error(`Error calling Supabase RPC 'increment_downloads' for mod ${modId}:`, JSON.stringify(rpcError, null, 2));

            // إذا كانت المشكلة أن الدالة غير موجودة، اعرض رسالة مفيدة
            if (rpcError.code === '42883' || rpcError.message?.includes('function') || rpcError.message?.includes('does not exist')) {
                console.warn('⚠️ increment_downloads function not found in database. Please execute database/QUICK_FIX_RPC_FUNCTIONS.sql');
            }

            // Also log the message property if it exists
            if (rpcError.message) {
                console.error("RPC Error Message:", rpcError.message);
            }
        } else {
            console.log(`✅ Successfully called Supabase RPC 'increment_downloads' for mod ${modId}.`);
            // Optional: Invalidate cache for this mod or category if needed,
            // although the UI is already updated optimistically.
        }
    } catch (error) {
        console.error(`💥 Unexpected error in androidShouldPersistDownloadIncrement for mod ${modId}:`, error);

        // لا نوقف التطبيق بسبب خطأ في الإحصائيات
        console.warn('🔄 Continuing app execution despite download tracking error');
    }
}


// --- Element Creation Helper ---
function createModElement(item, elementType = 'item') { // Default to 'item'
    const element = document.createElement('div');
    element.className = elementType; // 'item' or 'mod-card'

    element.setAttribute('data-id', item.id);
    element.setAttribute('data-likes', item.likes || 0);
    element.setAttribute('data-downloads', item.downloads || 0);
    element.setAttribute('data-date', item.created_at || new Date(0).toISOString());
    if (elementType === 'item') { // Add name attribute only for horizontal items if needed
        element.setAttribute('data-name', item.name || 'Unnamed Mod');
    }

    // Check if mod is new (less than 7 days old)
    const isNew = isRecentMod(item.created_at);

    const altText = (item.name && item.name.toLowerCase() !== 'all') ? item.name : 'Mod Image';

    let mainImage = 'image/placeholder.png';
    if (item.image_urls) {
        if (Array.isArray(item.image_urls)) {
            const validUrls = item.image_urls.filter(url => typeof url === 'string' && url.startsWith('http'));
            if (validUrls.length > 0) mainImage = validUrls[0];
        } else if (typeof item.image_urls === 'string') {
            try {
                const parsedImages = JSON.parse(item.image_urls);
                if (Array.isArray(parsedImages)) {
                    const validUrls = parsedImages.filter(url => typeof url === 'string' && url.startsWith('http'));
                    if (validUrls.length > 0) mainImage = validUrls[0];
                } else if (item.image_urls.startsWith('http')) {
                    mainImage = item.image_urls;
                }
            } catch (e) {
                if (item.image_urls.startsWith('http')) {
                    mainImage = item.image_urls;
                }
            }
        }
    }

     // Generate Inner HTML based on elementType
     if (elementType === 'item') { // Horizontal scroll item
         // Use data-src for lazy loading
         element.innerHTML = `
            <div class="mod-image-container">
                <img data-src="${mainImage}" alt="${altText}" class="mod-image lazy-load">
                ${isNew ? '<div class="new-badge">NEW</div>' : ''}
                ${item.is_free_addon ? '<div class="free-addon-icon">FREE ADDON</div>' : ''}
            </div>
            <h3 class="mod-name">${item.name || 'Unnamed Mod'}</h3>
            <div class="mod-actions">
                <div class="action-item">
                    <img src="image/Download icon.png" alt="Download Icon" class="action-icon">
                    <span class="action-count download-count" data-mod-id="${item.id}">${formatCount(item.downloads || 0)}</span>
                </div>
                <div class="action-item">
                    <img src="image/minecraft-icon.png" alt="Minecraft Icon" class="action-icon">
                    <p class="platform-label" style="font-size: 0.8rem; margin-top: 2px;">Bedrock</p>
                </div>
                <div class="action-item like-action">
                    <button class="like-button" onclick="event.stopPropagation(); toggleLike('${item.id}', '${item.name || 'Unnamed Mod'}', this)" title="Like this mod" style="background: transparent; border: none; padding: 0; cursor: pointer;">
                        <img src="image/heart.png" alt="Like Icon" class="heart-icon action-icon">
                    </button>
                    <span class="like-count action-count" data-mod-id="${item.id}">${formatCount(item.likes || 0)}</span>
                </div>
            </div>
        `;
     } else { // elementType === 'mod-card' - Vertical list/grid card
         // Use data-src for lazy loading
         element.innerHTML = `
            <div class="mod-image-container">
                <img data-src="${mainImage}" alt="${altText}" class="mod-image lazy-load">
                ${isNew ? '<div class="new-badge">NEW</div>' : ''}
                ${item.is_free_addon ? '<div class="free-addon-icon">FREE ADDON</div>' : ''}
            </div>
            <div class="mod-info">
                <h3 class="mod-name">${item.name || 'Unnamed Mod'}</h3>
                <div class="mod-actions">
                    <div class="action-item">
                        <img src="image/Download icon.png" alt="Download Icon" class="action-icon">
                        <span class="action-count download-count" data-mod-id="${item.id}">${formatCount(item.downloads || 0)}</span>
                    </div>
                    <div class="action-item">
                        <img src="image/minecraft-icon.png" alt="Minecraft Icon" class="action-icon">
                        <p class="platform-label" style="font-size: 0.8rem; margin-top: 2px;">Bedrock</p>
                    </div>
                    <div class="action-item like-action">
                        <button class="like-button" onclick="event.stopPropagation(); toggleLike('${item.id}', '${item.name || 'Unnamed Mod'}', this)" title="Like this mod" style="background: transparent; border: none; padding: 0; cursor: pointer;">
                            <img src="image/heart.png" alt="Like Icon" class="heart-icon action-icon">
                        </button>
                        <span class="like-count action-count" data-mod-id="${item.id}">${formatCount(item.likes || 0)}</span>
                    </div>
                </div>
            </div>
         `;
     }

    // Add click listener directly to the element to show the modal
    element.addEventListener('click', (event) => {
        // Check if the click originated from a button inside the element
        if (event.target.closest('button')) {
            console.log(`Click detected on a button inside mod item (ID: ${item.id}). Stopping modal opening.`);
            // event.stopPropagation(); // Stop propagation if needed, but onclick on buttons should handle their actions
            return; // Don't open the modal if a button was clicked
        }

        // If the click was not on a button, proceed to show the modal
        console.log(`Mod item area clicked (ID: ${item.id}). Attempting to show modal.`);
        if (typeof showModal === 'function') {
            // Ensure 'item' is valid before calling
            if (item && item.id) {
                 console.log("Calling showModal with item:", item); // Log before calling
                 showModal(item);
            } else {
                 console.error("Invalid 'item' data passed to click listener for element:", element);
            }
        } else {
            console.error("showModal function is not defined.");
        }
    });

    return element;
}


// --- Display Logic ---

// في ملف JavaScript الخاص بالنسخة السابقة

// ... (الكود الحالي لدالة displayModsBySection) ...

// New function to fetch suggested mods
async function fetchSuggestedModsFromSupabase(limit = 10) {
    const cacheKey = `suggested_mods_${limit}`;
    const cachedData = localStorage.getItem(cacheKey);

    if (cachedData) {
        try {
            const { timestamp, data } = JSON.parse(cachedData);
            if (Date.now() - timestamp < CACHE_DURATION_MS) {
                console.log(`Using cached suggested mods data for key: ${cacheKey}`);
                return data;
            } else {
                console.log(`Cache expired for suggested mods key: ${cacheKey}`);
                localStorage.removeItem(cacheKey);
            }
        } catch (e) {
            console.error("Error parsing cached suggested_mods data:", e);
            localStorage.removeItem(cacheKey);
        }
    }

    console.log(`Fetching fresh suggested mods from Supabase (limit: ${limit})`);
    try {
        // Step 1: Fetch mod_ids and their display_order from suggested_mods table
        const { data: suggestedEntries, error: suggestedError } = await supabaseClient
            .from(SUGGESTED_MODS_TABLE)
            .select('mod_id, display_order')
            .order('display_order', { ascending: true })
            .limit(limit);

        if (suggestedError) {
            console.error('Error fetching from suggested_mods table:', suggestedError);
            return null;
        }

        if (!suggestedEntries || suggestedEntries.length === 0) {
            console.log('No entries found in suggested_mods table.');
            return [];
        }

        const modIds = suggestedEntries.map(entry => entry.mod_id);
        const orderMap = new Map(suggestedEntries.map(entry => [entry.mod_id, entry.display_order]));

        // Step 2: Fetch mod details from the mods table for the fetched mod_ids
        const { data: mods, error: modsError } = await supabaseClient
            .from('mods')
            .select('*')
            .in('id', modIds);

        if (modsError) {
            console.error('Error fetching mod details for suggested mods:', modsError);
            return null;
        }

        if (!mods) {
            console.log('No mod details found for the suggested mod IDs.');
            return [];
        }

        // Step 3: Sort the fetched mods based on the original display_order
        // The .in() filter does not guarantee order, so we re-sort.
        const sortedMods = mods.sort((a, b) => {
            const orderA = orderMap.get(a.id) ?? Infinity;
            const orderB = orderMap.get(b.id) ?? Infinity;
            return orderA - orderB;
        });

        // Cache the final sorted suggested mods
        try {
            const dataToCache = { timestamp: Date.now(), data: sortedMods };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`Suggested mods data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error("Error storing suggested_mods data in localStorage:", e);
        }

        return sortedMods;

    } catch (error) {
        console.error('Unexpected error in fetchSuggestedModsFromSupabase:', error);
        return null;
    }
}

// Function to fetch new mods (last 3 days) from Supabase
async function fetchNewModsFromSupabase(limit = 10) {
    const cacheKey = `new_mods_${limit || 'none'}`;
    const cachedData = localStorage.getItem(cacheKey);

    // Check cache first (shorter cache time for new mods)
    if (cachedData) {
        try {
            const parsed = JSON.parse(cachedData);
            const cacheAge = Date.now() - parsed.timestamp;
            if (cacheAge < 180000) { // 3 minutes cache for new mods
                console.log(`Using cached new mods data (age: ${Math.round(cacheAge / 1000)}s)`);
                return parsed.data;
            }
        } catch (e) {
            console.error('Error parsing cached new mods data:', e);
        }
    }

    console.log('Fetching fresh new mods data from Supabase');
    try {
        // Calculate date 3 days ago
        const threeDaysAgo = new Date();
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
        const threeDaysAgoISO = threeDaysAgo.toISOString();

        let query = supabaseClient
            .from('mods')
            .select('*')
            .gte('created_at', threeDaysAgoISO) // Greater than or equal to 3 days ago
            .order('created_at', { ascending: false }); // Newest first

        if (limit && typeof limit === 'number' && limit > 0) {
            query = query.limit(limit);
        }

        const { data: newModsData, error } = await query;

        if (error) {
            console.error('Error fetching new mods from Supabase:', error);
            return null;
        }

        if (!newModsData || newModsData.length === 0) {
            console.log('No new mods found in the last 3 days.');
            return [];
        }

        // Cache the new mods data
        try {
            const dataToCache = { timestamp: Date.now(), data: newModsData };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`New mods data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error('Error storing new mods data in localStorage:', e);
        }

        return newModsData;

    } catch (error) {
        console.error('Unexpected error in fetchNewModsFromSupabase:', error);
        return null;
    }
}

// Function to fetch Free Addons from Supabase
async function fetchFreeAddonsFromSupabase(limit = 20) {
    const cacheKey = `free_addons_${limit || 'none'}`;
    const cachedData = localStorage.getItem(cacheKey);

    // Check cache first
    if (cachedData) {
        try {
            const parsed = JSON.parse(cachedData);
            const cacheAge = Date.now() - parsed.timestamp;
            if (cacheAge < 300000) { // 5 minutes cache
                console.log(`Using cached Free Addons data (age: ${Math.round(cacheAge / 1000)}s)`);
                return parsed.data;
            }
        } catch (e) {
            console.error('Error parsing cached Free Addons data:', e);
        }
    }

    console.log('Fetching fresh Free Addons data from Supabase');
    try {
        // First, check if the free_addons table exists by trying a simple count query
        const { error: tableCheckError } = await supabaseClient
            .from('free_addons')
            .select('id', { count: 'exact', head: true })
            .limit(1);

        if (tableCheckError) {
            console.warn('Free Addons table not found or not accessible:', tableCheckError.message);
            // Return empty array instead of null to avoid breaking the UI
            return [];
        }

        // Now get the free_addons entries with mod details
        let query = supabaseClient
            .from('free_addons')
            .select(`
                *,
                mods (*)
            `)
            .eq('is_active', true)
            .order('display_order', { ascending: true });

        if (limit && typeof limit === 'number' && limit > 0) {
            query = query.limit(limit);
        }

        const { data: freeAddonsData, error } = await query;

        if (error) {
            console.error('Error fetching Free Addons from Supabase:', error);
            return [];
        }

        if (!freeAddonsData || freeAddonsData.length === 0) {
            console.log('No Free Addons found in Supabase.');
            return [];
        }

        // Extract the mods data and sort by display_order
        const sortedMods = freeAddonsData
            .filter(item => item.mods) // Ensure mod data exists
            .map(item => item.mods)
            .slice(0, limit || 20); // Ensure we don't exceed the limit

        // Cache the final sorted Free Addons
        try {
            const dataToCache = { timestamp: Date.now(), data: sortedMods };
            localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
            console.log(`Free Addons data cached for key: ${cacheKey}`);
        } catch (e) {
            console.error('Error storing Free Addons data in localStorage:', e);
        }

        return sortedMods;

    } catch (error) {
        console.error('Unexpected error in fetchFreeAddonsFromSupabase:', error);
        return null;
    }
}


async function displayModsBySection() {
    console.log("[DEBUG] displayModsBySection START - النسخة الجديدة");
    const newsSection = document.getElementById('news-section');
    const suggestedModsSection = document.getElementById('suggested-mods-section');
    const addonsSection = document.getElementById('addons-section');
    const texturePackSection = document.getElementById('texture-pack-section');
    const shadersSection = document.getElementById('shaders-section');
    const mapsSection = document.getElementById('maps-section');
    const seedsSection = document.getElementById('seeds-section');
    const singleCategoryContainer = document.getElementById('singleCategoryContainer');
    const sortButtons = document.getElementById('sortButtons');

    const newsContainer = document.getElementById('news-mods');
    const suggestedModsContainer = document.getElementById('suggested-mods');
    const addonsContainer = document.getElementById('addons-mods');
    const texturePackContainer = document.getElementById('texture-pack-mods');
    const shadersContainer = document.getElementById('shaders-mods');
    const mapsContainer = document.getElementById('maps-mods');
    const seedsContainer = document.getElementById('seeds-mods');

    // إخفاء حاوية الفئة الواحدة وأزرار الفرز
    if (singleCategoryContainer) singleCategoryContainer.style.display = 'none';
    if (sortButtons) {
        sortButtons.style.display = 'none';
    }

    // إظهار أقسام "All" بالترتيب الجديد: News, Addons, Suggested, Shaders, Texture Pack, Seeds, Maps
    if (newsSection) newsSection.style.display = 'block'; // News - أول قسم
    if (addonsSection) addonsSection.style.display = 'block'; // Addons - القسم الثاني (يتضمن Free Addons)
    if (suggestedModsSection) suggestedModsSection.style.display = 'block'; // Suggested - القسم الثالث
    if (shadersSection) shadersSection.style.display = 'block'; // Shaders - القسم الرابع
    if (texturePackSection) texturePackSection.style.display = 'block'; // Texture Pack - القسم الخامس
    if (seedsSection) seedsSection.style.display = 'block'; // Seeds - القسم السادس
    if (mapsSection) mapsSection.style.display = 'block'; // Maps - القسم السابع

    // وضع مؤشر تحميل للأقسام الجديدة
    [newsContainer, suggestedModsContainer, addonsContainer, shadersContainer, texturePackContainer,
     seedsContainer, mapsContainer].forEach(container => {
        if (container) {
            container.innerHTML = '<div class="loading-indicator" style="display:flex; justify-content:center; padding: 20px;"><div class="loading-spinner"></div> Loading...</div>';
        }
    });

    // جلب البيانات - ترتيب جديد: News, Addons (مع Free Addons), Suggested, Shaders, Texture Pack, Seeds, Maps
    const fetchPromises = [
        fetchNewModsFromSupabase(10),                          // News - المودات الجديدة (آخر 3 أيام)
        fetchModsFromSupabase('Addons', null, false, 10),      // Addons - القسم الثاني
        fetchFreeAddonsFromSupabase(20),                       // Free Addons - سيتم دمجها مع Addons
        fetchSuggestedModsFromSupabase(10),                    // Suggested - القسم الثالث
        fetchModsFromSupabase('Shaders', null, false, 10),     // Shaders - القسم الرابع
        fetchModsFromSupabase('Texture', null, false, 10),     // Texture Packs - القسم الخامس
        fetchModsFromSupabase('Seeds', null, false, 10),       // Seeds - القسم السادس
        fetchModsFromSupabase('Maps', null, false, 10),        // Maps - القسم السابع
        fetchBannerAds(),                                      // Banner Ads
        fetchFeaturedAddons()                                  // Featured Addons with special effects
    ];

    try {
        let [newsItems, addonsItems, freeAddonsItems, suggestedItems, shadersItems, textureItems, seedsItems, mapsItems, bannerAdsItems, featuredAddonsItems] = await Promise.all(fetchPromises);

        // Store banner ads in global variable and display them
        bannerAds = bannerAdsItems || [];
        displayBannerAds();

        // مسح مؤشرات التحميل للأقسام الجديدة
        [newsContainer, suggestedModsContainer, addonsContainer, shadersContainer, texturePackContainer,
         seedsContainer, mapsContainer].forEach(container => {
            if (container) container.innerHTML = '';
        });

        // دمج Free Addons مع Addons - Free Addons تظهر أولاً
        if (freeAddonsItems && freeAddonsItems.length > 0) {
            // إضافة علامة free_addon للمودات المجانية
            freeAddonsItems.forEach(item => {
                item.is_free_addon = true;
            });

            // دمج Free Addons مع Addons - Free Addons أولاً
            if (addonsItems) {
                addonsItems = [...freeAddonsItems, ...addonsItems];
            } else {
                addonsItems = freeAddonsItems;
            }
        }

        // Shuffle for categories (no need to slice as we already limited to 10)
        if (shadersItems) {
            shadersItems = shuffleArray(shadersItems);
        }
        if (textureItems) {
            textureItems = shuffleArray(textureItems);
        }
        if (seedsItems) {
            seedsItems = shuffleArray(seedsItems);
        }
        if (mapsItems) {
            mapsItems = shuffleArray(mapsItems);
        }
        // لا نخلط Addons لأن Free Addons يجب أن تبقى في المقدمة
        // if (addonsItems) {
        //     addonsItems = shuffleArray(addonsItems);
        // }

        // Create a map of featured addon IDs for quick lookup
        const featuredAddonIds = new Set();
        if (featuredAddonsItems && featuredAddonsItems.length > 0) {
            featuredAddonsItems.forEach(item => {
                featuredAddonIds.add(item.id);
            });
        }


        // Function to add animated orbs to featured addons
        function addAnimatedOrbs(element) {
            // Create 5 orbs with random positions
            for (let i = 0; i < 5; i++) {
                const orb = document.createElement('div');
                orb.className = 'orb';

                // Random position within the element
                const leftPos = Math.random() * 100; // Random percentage across width
                orb.style.left = `${leftPos}%`;
                orb.style.bottom = '0';

                // Random animation delay and duration for more natural effect
                const delay = Math.random() * 2; // 0-2s delay
                const duration = 2 + Math.random() * 2; // 2-4s duration

                orb.style.animation = `orbFloat ${duration}s ease-in-out ${delay}s infinite`;

                element.appendChild(orb);
            }
        }

        // دالة مساعدة لملء الحاويات
        const populateContainer = (container, items, categoryName) => {
            if (!container) return;
            if (items === null) {
                container.innerHTML = `<p style="text-align: center; padding: 20px; color: #f87171;">Error fetching ${categoryName}.</p>`;
            } else if (items.length === 0) {
                container.innerHTML = `<p style="text-align: center; padding: 20px;">No ${categoryName} mods to display.</p>`;
            } else {
                items.forEach(item => {
                    const modElement = createModElement(item, 'item'); // 'item' للنمط الأفقي

                    // إضافة تأثير التوهج العام لجميع الكاردات في تصنيف All
                    modElement.classList.add('all-category-glow');

                    // If this is the Addons container and the item is in the featured list
                    if (categoryName === 'Addons' && featuredAddonIds.has(item.id)) {
                        // Add the featured class for styling
                        modElement.classList.add('featured-addon');

                        // Add animated orbs
                        addAnimatedOrbs(modElement);
                    }

                    // إضافة تأثيرات للمودات الشعبية (نظام ديناميكي)
                    if (isModPopular(item)) {
                        modElement.classList.add('popular-mod');
                        addPixelParticles(modElement);

                        // إضافة أيقونة Popular في الصورة
                        const imageContainer = modElement.querySelector('.mod-image-container');
                        if (imageContainer) {
                            const popularIcon = document.createElement('div');
                            popularIcon.className = 'popular-icon';
                            popularIcon.textContent = 'POPULAR';
                            imageContainer.appendChild(popularIcon);
                        }
                    }

                    // إضافة تأثيرات خاصة لمودات Free Addons
                    if (item.is_free_addon) {
                        modElement.classList.add('free-addon-mod');
                        addFreeAddonPixelParticles(modElement);
                    }

                    container.appendChild(modElement);
                });
            }
        };

        // Function to populate Free Addons container (special effects removed, now generic)
        const populateFreeAddonsContainer = (container, items, categoryName) => {
            if (!container) return;
            if (items === null) {
                container.innerHTML = `<p style="text-align: center; padding: 20px; color: #f87171;">Error fetching ${categoryName}.</p>`;
            } else if (items.length === 0) {
                container.innerHTML = `<p style="text-align: center; padding: 20px;">No ${categoryName} mods to display.</p>`;
            } else {
                items.forEach(item => {
                    const modElement = createModElement(item, 'item'); // 'item' for horizontal style

                    // إضافة class free-addon-mod لتطبيق تأثير اللمعان الأبيض
                    modElement.classList.add('free-addon-mod');

                    container.appendChild(modElement);
                });
            }
        };

        // Function to add floating orbs animation
        const addFloatingOrbs = (element) => {
            setInterval(() => {
                if (Math.random() < 0.3) { // 30% chance every interval
                    const orb = document.createElement('div');
                    orb.className = 'floating-orb';

                    // Random position within the element
                    orb.style.left = Math.random() * (element.offsetWidth - 10) + 'px';
                    orb.style.bottom = '0px';

                    element.appendChild(orb);

                    // Remove orb after animation completes
                    setTimeout(() => {
                        if (orb.parentNode) {
                            orb.parentNode.removeChild(orb);
                        }
                    }, 3000);
                }
            }, 2000); // Check every 2 seconds
        };

        // Function to add sparkle effects
        const addSparkleEffects = (element) => {
            setInterval(() => {
                if (Math.random() < 0.4) { // 40% chance every interval
                    const sparkle = document.createElement('div');
                    sparkle.className = 'sparkle';

                    // Random position within the element
                    sparkle.style.left = Math.random() * element.offsetWidth + 'px';
                    sparkle.style.top = Math.random() * element.offsetHeight + 'px';

                    element.appendChild(sparkle);

                    // Remove sparkle after animation completes
                    setTimeout(() => {
                        if (sparkle.parentNode) {
                            sparkle.parentNode.removeChild(sparkle);
                        }
                    }, 2000);
                }
            }, 1500); // Check every 1.5 seconds
        };

        // Function to add pixel particles for popular mods
        const addPixelParticles = (element) => {
            setInterval(() => {
                if (Math.random() < 0.6) { // 60% chance every interval
                    const particle = document.createElement('div');
                    particle.className = 'pixel-particle';

                    // Random position within the element
                    const leftPos = Math.random() * (element.offsetWidth - 4);
                    const bottomPos = Math.random() * 20; // Start from bottom area

                    particle.style.left = leftPos + 'px';
                    particle.style.bottom = bottomPos + 'px';

                    element.appendChild(particle);

                    // Remove particle after animation completes
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 3000);
                }
            }, 800); // Check every 0.8 seconds for more frequent particles
        };

        // Function to add pixel particles for Free Addons
        const addFreeAddonPixelParticles = (element) => {
            setInterval(() => {
                if (Math.random() < 0.7) { // 70% chance every interval for more particles
                    const particle = document.createElement('div');
                    particle.className = 'free-addon-pixel-particle';

                    // Random position within the element
                    const leftPos = Math.random() * (element.offsetWidth - 4);
                    const bottomPos = Math.random() * 20; // Start from bottom area

                    particle.style.left = leftPos + 'px';
                    particle.style.bottom = bottomPos + 'px';

                    element.appendChild(particle);

                    // Remove particle after animation completes
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 3000);
                }
            }, 700); // Check every 0.7 seconds for more frequent particles
        };

        // ملء كل قسم
        const containersToAnimate = [];
        if (newsContainer) containersToAnimate.push(newsContainer);
        if (suggestedModsContainer) containersToAnimate.push(suggestedModsContainer);
        if (addonsContainer) containersToAnimate.push(addonsContainer);
        if (texturePackContainer) containersToAnimate.push(texturePackContainer);
        if (shadersContainer) containersToAnimate.push(shadersContainer);
        if (seedsContainer) containersToAnimate.push(seedsContainer);
        if (mapsContainer) containersToAnimate.push(mapsContainer);

        containersToAnimate.forEach(container => {
            container.classList.remove('animate-visible'); // Reset animation
            container.classList.add('initial-hidden-animate');
        });

        // ملء الحاويات بالترتيب الجديد: News, Addons (مع Free Addons), Suggested, Shaders, Texture Pack, Seeds, Maps
        populateContainer(newsContainer, newsItems, 'News'); // News - أول قسم
        populateContainer(addonsContainer, addonsItems, 'Addons'); // Addons - القسم الثاني (يتضمن Free Addons)
        populateContainer(suggestedModsContainer, suggestedItems, 'Suggested'); // Suggested - القسم الثالث
        populateContainer(shadersContainer, shadersItems, 'Shaders'); // Shaders - القسم الرابع
        populateContainer(texturePackContainer, textureItems, 'Texture'); // Texture Pack - القسم الخامس
        populateContainer(seedsContainer, seedsItems, 'Seeds'); // Seeds - القسم السادس
        populateContainer(mapsContainer, mapsItems, 'Maps'); // Maps - القسم السابع

        // تهيئة التحميل الكسول للصور الجديدة
        initializeLazyLoading('.lazy-load');

        // Trigger animation for each container after a short delay
        containersToAnimate.forEach(container => {
            setTimeout(() => {
                if (container.classList.contains('initial-hidden-animate')) {
                    container.classList.add('animate-visible');
                }
            }, 50);
        });

    } catch (error) {
        console.error("[DEBUG] Error in displayModsBySection (modified):", error);
        const errorMsg = '<p style="text-align: center; padding: 20px; color: #f87171;">Error loading mods.</p>';
        if (newsContainer) newsContainer.innerHTML = errorMsg;
        if (suggestedModsContainer) suggestedModsContainer.innerHTML = errorMsg;
        if (addonsContainer) addonsContainer.innerHTML = errorMsg;
        if (shadersContainer) shadersContainer.innerHTML = errorMsg;
        if (texturePackContainer) texturePackContainer.innerHTML = errorMsg;
        if (seedsContainer) seedsContainer.innerHTML = errorMsg;
        if (mapsContainer) mapsContainer.innerHTML = errorMsg;
    }
    console.log("[DEBUG] displayModsBySection END - النسخة المعدلة");
}

// Function to display mods for a single category (vertical list)
async function displaySingleCategory(category, sortBy = currentSortBy, ascending = currentSortAscending) {
    console.log(`Displaying single category: ${category}, Sort: ${sortBy}, Asc: ${ascending}`);
    const newsSection = document.getElementById('news-section');
    const suggestedModsSection = document.getElementById('suggested-mods-section');
    const addonsSection = document.getElementById('addons-section');
    const texturePackSection = document.getElementById('texture-pack-section');
    const shadersSection = document.getElementById('shaders-section');
    const mapsSection = document.getElementById('maps-section');
    const seedsSection = document.getElementById('seeds-section');
    const singleCategoryContainer = document.getElementById('singleCategoryContainer');
    const sortButtons = document.getElementById('sortButtons');

    // Hide section containers, show single category container and sort buttons
    if (newsSection) newsSection.style.display = 'none';
    if (suggestedModsSection) suggestedModsSection.style.display = 'none';
    if (addonsSection) addonsSection.style.display = 'none';
    if (shadersSection) shadersSection.style.display = 'none';
    if (texturePackSection) texturePackSection.style.display = 'none';
    if (seedsSection) seedsSection.style.display = 'none';
    if (mapsSection) mapsSection.style.display = 'none';
    if (singleCategoryContainer) singleCategoryContainer.style.display = 'block';

    if (sortButtons) {
        // For "Suggested" category, we might not want to show all sort buttons,
        // as it's pre-sorted by admin. For now, show them for all.
        sortButtons.style.display = 'flex';
        sortButtons.classList.remove('animate-visible');
        sortButtons.classList.add('initial-hidden-animate');
        setTimeout(() => {
            if (sortButtons.classList.contains('initial-hidden-animate')) {
                sortButtons.classList.add('animate-visible');
            }
        }, 50);
    }

    if (singleCategoryContainer) {
        singleCategoryContainer.innerHTML = '<div class="loading-indicator" style="display:flex; justify-content:center; padding: 20px;"><div class="loading-spinner"></div></div>';
    }

    let items;
    if (category === 'News') {
        // Fetch all new mods from the last 3 days, sorted from newest to oldest
        items = await fetchNewModsFromSupabase(null); // null limit to fetch all new mods
        console.log(`Fetched ${items ? items.length : 0} new mods for News category`);
    } else if (category === 'Suggested') {
        // Fetch all suggested mods (or a larger limit if pagination is desired later)
        // For "see all Suggested", we sort by display_order. Other sort buttons might not be relevant.
        items = await fetchSuggestedModsFromSupabase(null); // null limit to fetch all, or a larger number like 100
    } else if (category === 'Addons') {
        // For Addons category, fetch both regular addons and free addons, then merge them
        const [regularAddons, freeAddons] = await Promise.all([
            fetchModsFromSupabase('Addons', sortBy, ascending),
            fetchFreeAddonsFromSupabase(null) // Fetch all free addons
        ]);

        // Mark free addons and merge with regular addons - Free Addons أولاً
        if (freeAddons && freeAddons.length > 0) {
            freeAddons.forEach(item => {
                item.is_free_addon = true;
            });
            items = [...(freeAddons || []), ...(regularAddons || [])];
        } else {
            items = regularAddons;
        }
    } else {
        items = await fetchModsFromSupabase(category, sortBy, ascending);
    }

    if (singleCategoryContainer) singleCategoryContainer.innerHTML = '';

    if (items === null) {
        if (singleCategoryContainer) singleCategoryContainer.innerHTML = '<p style="text-align: center; padding: 20px; color: #f87171;">Error fetching data.</p>';
        return;
    }
    if (items.length === 0) {
        if (singleCategoryContainer) singleCategoryContainer.innerHTML = `<p style="text-align: center; padding: 20px;">No ${category} mods to display.</p>`;
        return;
    }

    // Removed assignment to displayedModsData

    // Apply initial hidden class
    if (singleCategoryContainer) {
        singleCategoryContainer.classList.remove('animate-visible'); // Reset animation
        singleCategoryContainer.classList.add('initial-hidden-animate');
    }

    // If this is the Addons category, fetch featured addons to apply special effects
    let featuredAddonIds = new Set();
    if (category === 'Addons') {
        try {
            const featuredAddons = await fetchFeaturedAddons();
            if (featuredAddons && featuredAddons.length > 0) {
                featuredAddons.forEach(addon => {
                    featuredAddonIds.add(addon.id);
                });
            }
        } catch (error) {
            console.error('Error fetching featured addons for single category view:', error);
        }
    }

    items.forEach(item => {
        // Use 'mod-card' style for vertical list items
        const modElement = createModElement(item, 'mod-card');

        // If this is the Addons category and the item is in the featured list
        if (category === 'Addons' && featuredAddonIds.has(item.id)) {
            // Add the featured class for styling
            modElement.classList.add('featured-addon');

            // Add animated orbs
            addAnimatedOrbs(modElement);
        }

        // إضافة تأثيرات للمودات الشعبية (نظام ديناميكي)
        if (isModPopular(item)) {
            modElement.classList.add('popular-mod');
            addPixelParticlesGlobal(modElement);

            // إضافة أيقونة Popular في الصورة
            const imageContainer = modElement.querySelector('.mod-image-container');
            if (imageContainer) {
                const popularIcon = document.createElement('div');
                popularIcon.className = 'popular-icon';
                popularIcon.textContent = 'POPULAR';
                imageContainer.appendChild(popularIcon);
            }
        }

        // إضافة تأثيرات خاصة لمودات Free Addons
        if (item.is_free_addon) {
            modElement.classList.add('free-addon-mod');
            addFreeAddonPixelParticlesGlobal(modElement);
        }

        if (singleCategoryContainer) singleCategoryContainer.appendChild(modElement);
    });

    // Trigger animation after a short delay
    if (singleCategoryContainer) {
        setTimeout(() => {
            if (singleCategoryContainer.classList.contains('initial-hidden-animate')) { // Ensure it's still meant to be animated
                singleCategoryContainer.classList.add('animate-visible');
            }
        }, 50); // Small delay to ensure elements are rendered
    }
    // applyFadeInAnimation('#singleCategoryContainer .mod-card'); // Removed old animation call
    initializeLazyLoading('#singleCategoryContainer .lazy-load'); // Initialize lazy loading for new elements
}

// نظام ديناميكي لتحديد معايير الشعبية
let popularityThresholds = {
    downloads: 1000,
    likes: 50,
    lastUpdated: null
};

// دالة لحساب معايير الشعبية بشكل ديناميكي
async function calculateDynamicPopularityThresholds() {
    try {
        console.log("Calculating dynamic popularity thresholds...");

        // جلب إحصائيات عامة من قاعدة البيانات
        const { data: stats, error } = await supabaseClient
            .from('mods')
            .select('downloads, likes')
            .not('downloads', 'is', null)
            .not('likes', 'is', null);

        if (error) {
            console.error('Error fetching mod statistics:', error);
            return popularityThresholds; // استخدام القيم الافتراضية
        }

        if (!stats || stats.length === 0) {
            console.log("No mod statistics found, using default thresholds");
            return popularityThresholds;
        }

        // حساب المتوسطات والمئينات
        const downloads = stats.map(mod => mod.downloads || 0).sort((a, b) => b - a);
        const likes = stats.map(mod => mod.likes || 0).sort((a, b) => b - a);

        // حساب المئين 80 (أعلى 20% من المودات)
        const downloadsP80 = downloads[Math.floor(downloads.length * 0.2)] || 1000;
        const likesP80 = likes[Math.floor(likes.length * 0.2)] || 50;

        // تطبيق حد أدنى وأقصى للمعايير
        const newDownloadsThreshold = Math.max(500, Math.min(downloadsP80, 10000));
        const newLikesThreshold = Math.max(25, Math.min(likesP80, 1000));

        popularityThresholds = {
            downloads: newDownloadsThreshold,
            likes: newLikesThreshold,
            lastUpdated: new Date().toISOString()
        };

        console.log("Updated popularity thresholds:", popularityThresholds);

        // حفظ المعايير في localStorage للاستخدام السريع
        localStorage.setItem('popularityThresholds', JSON.stringify(popularityThresholds));

        return popularityThresholds;

    } catch (error) {
        console.error('Error calculating dynamic popularity thresholds:', error);
        return popularityThresholds; // استخدام القيم الافتراضية
    }
}

// دالة للتحقق من شعبية المود
function isModPopular(mod) {
    const downloads = mod.downloads || 0;
    const likes = mod.likes || 0;

    // المود شعبي إذا تجاوز أي من المعايير
    return downloads >= popularityThresholds.downloads || likes >= popularityThresholds.likes;
}

// تحديث المعايير عند بدء التطبيق
async function initializePopularitySystem() {
    // محاولة تحميل المعايير المحفوظة
    const savedThresholds = localStorage.getItem('popularityThresholds');
    if (savedThresholds) {
        try {
            const parsed = JSON.parse(savedThresholds);
            const lastUpdated = new Date(parsed.lastUpdated);
            const now = new Date();
            const hoursSinceUpdate = (now - lastUpdated) / (1000 * 60 * 60);

            // تحديث المعايير كل 24 ساعة
            if (hoursSinceUpdate < 24) {
                popularityThresholds = parsed;
                console.log("Using cached popularity thresholds:", popularityThresholds);
                return;
            }
        } catch (error) {
            console.error('Error parsing saved thresholds:', error);
        }
    }

    // حساب معايير جديدة
    await calculateDynamicPopularityThresholds();
}

// Global functions for Free Addons effects (used in both section and single category views)
function addFloatingOrbsToElement(element) {
    setInterval(() => {
        if (Math.random() < 0.3) { // 30% chance every interval
            const orb = document.createElement('div');
            orb.className = 'floating-orb';

            // Random position within the element
            orb.style.left = Math.random() * (element.offsetWidth - 10) + 'px';
            orb.style.bottom = '0px';

            element.appendChild(orb);

            // Remove orb after animation completes
            setTimeout(() => {
                if (orb.parentNode) {
                    orb.parentNode.removeChild(orb);
                }
            }, 3000);
        }
    }, 2000); // Check every 2 seconds
}

// Global function for popular mod pixel particles (used in both section and single category views)
function addPixelParticlesGlobal(element) {
    setInterval(() => {
        if (Math.random() < 0.6) { // 60% chance every interval
            const particle = document.createElement('div');
            particle.className = 'pixel-particle';

            // Random position within the element
            const leftPos = Math.random() * (element.offsetWidth - 4);
            const bottomPos = Math.random() * 20; // Start from bottom area

            particle.style.left = leftPos + 'px';
            particle.style.bottom = bottomPos + 'px';

            element.appendChild(particle);

            // Remove particle after animation completes
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 3000);
        }
    }, 800); // Check every 0.8 seconds for more frequent particles
}

// Global function for Free Addons pixel particles (used in both section and single category views)
function addFreeAddonPixelParticlesGlobal(element) {
    setInterval(() => {
        if (Math.random() < 0.7) { // 70% chance every interval for more particles
            const particle = document.createElement('div');
            particle.className = 'free-addon-pixel-particle';

            // Random position within the element
            const leftPos = Math.random() * (element.offsetWidth - 4);
            const bottomPos = Math.random() * 20; // Start from bottom area

            particle.style.left = leftPos + 'px';
            particle.style.bottom = bottomPos + 'px';

            element.appendChild(particle);

            // Remove particle after animation completes
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 3000);
        }
    }, 700); // Check every 0.7 seconds for more frequent particles
}

function addSparkleEffectsToElement(element) {
    setInterval(() => {
        if (Math.random() < 0.4) { // 40% chance every interval
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle';

            // Random position within the element
            sparkle.style.left = Math.random() * element.offsetWidth + 'px';
            sparkle.style.top = Math.random() * element.offsetHeight + 'px';

            element.appendChild(sparkle);

            // Remove sparkle after animation completes
            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 2000);
        }
    }, 1500); // Check every 1.5 seconds
}


// --- Modal Display ---
let currentModalModId = null;

// Function to increment clicks via Supabase RPC
async function incrementModClicks(modId) {
    if (!modId) return;
    console.log(`Incrementing clicks for mod ID: ${modId}`);

    // التحقق من وجود supabaseClient
    if (!supabaseClient) {
        console.warn('Supabase client not available, skipping click increment');
        return;
    }

    try {
        const { error } = await supabaseClient.rpc('increment_clicks', { mod_id_in: modId });
        if (error) {
            console.error(`Error calling increment_clicks RPC for mod ${modId}:`, error);

            // إذا كانت المشكلة أن الدالة غير موجودة، اعرض رسالة مفيدة
            if (error.code === '42883' || error.message?.includes('function') || error.message?.includes('does not exist')) {
                console.warn('⚠️ increment_clicks function not found in database. Please execute database/QUICK_FIX_RPC_FUNCTIONS.sql');
            }
        } else {
            console.log(`✅ Successfully incremented clicks for mod ${modId}.`);
        }
    } catch (error) {
        console.error(`💥 Unexpected error incrementing clicks for mod ${modId}:`, error);

        // لا نوقف التطبيق بسبب خطأ في الإحصائيات
        console.warn('🔄 Continuing app execution despite click tracking error');
    }
}

function showShaderPatchWarningDialog(item, proceedCallback) {
    const warningDismissedKey = 'shader_patch_warning_dismissed';
    if (localStorage.getItem(warningDismissedKey) === 'true') {
        if (typeof proceedCallback === 'function') {
            proceedCallback(item);
        }
        return;
    }

    const existingDialog = document.getElementById('shader-patch-warning-overlay');
    if (existingDialog) existingDialog.remove();

    const overlay = document.createElement('div');
    overlay.id = 'shader-patch-warning-overlay';
    // انسخ أنماط CSS اللازمة لـ .shader-warning-overlay و .shader-warning-dialog من النسخة الحالية
    // أو أنشئها. مثال للأنماط الأساسية:
    overlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background-color: rgba(0, 0, 0, 0.7); display: flex;
        justify-content: center; align-items: center; z-index: 100007; /* أعلى من الإعلانات */
        padding: 20px; box-sizing: border-box;
    `;

    const dialog = document.createElement('div');
    dialog.className = 'shader-warning-dialog'; // ستحتاج لتعريف هذا الكلاس في CSS
    dialog.style.cssText = `
        background-color: #333; color: white; padding: 20px; border-radius: 10px;
        width: 100%; max-width: 350px; text-align: center;
        border: 2px solid #ffcc00; /* Changed to yellow */
        /* box-shadow: 0 0 15px rgba(255, 165, 0, 0.5); */ /* تجاهل الظل مبدئيًا */
    `;

    const icon = document.createElement('img');
    icon.src = 'image/mineraft_patch.jpg'; // تأكد من صحة المسار
    icon.alt = 'Important';
    icon.style.cssText = `width: 100%; aspect-ratio: 16 / 9; max-height: 200px; object-fit: cover; margin-bottom: 15px; border-radius: 8px; border: 3px solid #ffcc00;`; /* Yellow border, 16:9 aspect ratio, larger */

    const title = document.createElement('h2');
    title.textContent = t('shader_warning_title');
    title.style.cssText = `color: orange; margin-bottom: 10px; font-size: 1.4em;`;

    const description = document.createElement('p');
    description.textContent = t('shader_warning_description');
    description.style.cssText = `margin-bottom: 20px; font-size: 0.95em; line-height: 1.5;`;

    const translationButtonsDiv = document.createElement('div');
    translationButtonsDiv.style.cssText = `margin-bottom: 20px; display: flex; justify-content: center; gap: 10px;`;

    const translateArButton = document.createElement('button');
    translateArButton.textContent = t('shader_warning_translate_ar');

    const translateEnButton = document.createElement('button'); // تعريفها هنا
    translateEnButton.textContent = t('shader_warning_translate_en');

    // ... (انسخ بقية محتوى وخصائص أزرار الترجمة من النسخة الحالية)
    // أنماط أساسية للأزرار:
    [translateArButton, translateEnButton].forEach(btn => {
        btn.style.cssText = `
            background-color: #ffcc00; color: black; border: 1px solid #cc9900; /* Yellow background, black text, darker yellow border */
            padding: 8px 12px; border-radius: 5px; cursor: pointer; font-family: 'VT323', monospace; /* Pixel font */
        `;
        // btn.classList.add('active'); // لأحد الأزرار
    });
    // ... (نسخ منطق setLanguage وربط الأحداث لأزرار الترجمة)

    const actionButtonsDiv = document.createElement('div');
    actionButtonsDiv.style.cssText = `display: flex; flex-direction: column; gap: 10px;`;

    const doneButton = document.createElement('button');
    doneButton.textContent = t('shader_warning_understand');

    const dismissButton = document.createElement('button'); // تعريفها هنا
    dismissButton.textContent = t('shader_warning_dont_show');
    // ... (انسخ خصائص doneButton و dismissButton من النسخة الحالية)
    // أنماط أساسية للأزرار:
    [doneButton, dismissButton].forEach(btn => {
        btn.style.cssText = `
            background-color: #ffcc00; color: black; border: none; /* Yellow background, black text */
            padding: 10px 15px; border-radius: 5px; cursor: pointer; font-weight: bold; font-family: 'VT323', monospace; /* Pixel font */
        `;
    });
    // ... (نسخ ربط الأحداث لـ doneButton و dismissButton)

    // Language switching functionality using the global translation system
    const updateDialogLanguage = () => {
        const currentLang = translationManager.getCurrentLanguage();
        title.textContent = t('shader_warning_title');
        description.textContent = t('shader_warning_description');
        translateArButton.textContent = t('shader_warning_translate_ar');
        translateEnButton.textContent = t('shader_warning_translate_en');
        doneButton.textContent = t('shader_warning_understand');
        dismissButton.textContent = t('shader_warning_dont_show');

        // Update button styles based on current language
        if (currentLang === 'ar') {
            translateArButton.style.backgroundColor = 'orange';
            translateEnButton.style.backgroundColor = '#555';
            dialog.setAttribute('dir', 'rtl');
        } else {
            translateEnButton.style.backgroundColor = 'orange';
            translateArButton.style.backgroundColor = '#555';
            dialog.setAttribute('dir', 'ltr');
        }
    };

    translateArButton.onclick = () => {
        translationManager.setLanguage('ar');
        updateDialogLanguage();
    };
    translateEnButton.onclick = () => {
        translationManager.setLanguage('en');
        updateDialogLanguage();
    };

    // Initialize with current language
    updateDialogLanguage();

    doneButton.onclick = () => {
        overlay.remove(); document.body.style.overflow = '';
        if (typeof proceedCallback === 'function') proceedCallback(item);
    };
    dismissButton.onclick = () => {
        localStorage.setItem(warningDismissedKey, 'true');
        overlay.remove(); document.body.style.overflow = '';
        if (typeof proceedCallback === 'function') proceedCallback(item);
    };
    // --- نهاية نسخ منطق الترجمة ---


    translationButtonsDiv.appendChild(translateArButton);
    translationButtonsDiv.appendChild(translateEnButton);
    actionButtonsDiv.appendChild(doneButton);
    actionButtonsDiv.appendChild(dismissButton);

    dialog.appendChild(icon);
    dialog.appendChild(title);
    dialog.appendChild(description);
    dialog.appendChild(translationButtonsDiv);
    dialog.appendChild(actionButtonsDiv);
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);
    document.body.style.overflow = 'hidden';
}

// فحص المربعات المخصصة
async function checkCustomDialog(item, callback) {
    try {
        // التحقق من وجود مربع مخصص لهذا المود
        const { data: dialogMods, error } = await supabaseClient
            .from('custom_dialog_mods')
            .select(`
                dialog_id,
                custom_mod_dialogs (
                    id,
                    title,
                    title_en,
                    description,
                    description_en,
                    image_url,
                    button_text,
                    button_text_en,
                    show_dont_show_again,
                    is_active
                )
            `)
            .eq('mod_id', item.id);

        if (error) {
            console.error('خطأ في فحص المربعات المخصصة:', error);
            callback(item); // متابعة بدون مربع مخصص
            return;
        }

        // البحث عن مربع مفعل
        const activeDialog = dialogMods?.find(dm =>
            dm.custom_mod_dialogs && dm.custom_mod_dialogs.is_active
        );

        if (activeDialog && activeDialog.custom_mod_dialogs) {
            const dialog = activeDialog.custom_mod_dialogs;

            // التحقق من إعداد "عدم الظهور مجدداً"
            const dontShowKey = `custom_dialog_${dialog.id}_dont_show`;
            if (localStorage.getItem(dontShowKey) === 'true') {
                callback(item); // تم اختيار عدم الظهور مجدداً
                return;
            }

            // عرض المربع المخصص
            showCustomDialog(dialog, item, callback);
        } else {
            // لا يوجد مربع مخصص، متابعة عادية
            callback(item);
        }

    } catch (error) {
        console.error('خطأ في فحص المربعات المخصصة:', error);
        callback(item); // متابعة بدون مربع مخصص
    }
}

// عرض المربع المخصص
function showCustomDialog(dialog, item, callback) {
    // الحصول على لغة المستخدم
    const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
    const isArabic = userLanguage === 'ar';

    console.log(`🌍 Showing custom dialog in language: ${userLanguage}`);

    // إنشاء overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100000;
        animation: fadeIn 0.3s ease;
    `;

    // إنشاء محتوى المربع
    const dialogContent = document.createElement('div');
    dialogContent.style.cssText = `
        background: #000000;
        border-radius: 20px;
        padding: 30px;
        max-width: 500px;
        width: 90%;
        text-align: center;
        border: 3px solid #ffcc00;
        box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
        animation: slideInUp 0.4s ease;
        position: relative;
        direction: ${isArabic ? 'rtl' : 'ltr'};
    `;

    // تحديد النصوص بناءً على اللغة
    let dialogTitle, dialogDescription, dialogButtonText, dontShowAgainText;

    if (isArabic) {
        dialogTitle = dialog.title || dialog.title_en || 'عنوان غير محدد';
        dialogDescription = dialog.description || dialog.description_en || '';
        dialogButtonText = dialog.button_text || dialog.button_text_en || 'تم';
        dontShowAgainText = 'عدم الظهور مجدداً';
    } else {
        dialogTitle = dialog.title_en || dialog.title || 'No title specified';
        dialogDescription = dialog.description_en || dialog.description || '';
        dialogButtonText = dialog.button_text_en || dialog.button_text || 'OK';
        dontShowAgainText = "Don't show again";
    }

    // إنشاء محتوى HTML
    let contentHTML = '';

    // إضافة الصورة إذا كانت موجودة
    if (dialog.image_url) {
        contentHTML += `
            <img src="${dialog.image_url}" alt="${dialogTitle}"
                 style="max-width: 100%; max-height: 200px; border-radius: 10px; margin-bottom: 20px;"
                 onerror="this.style.display='none'">
        `;
    }

    // إضافة العنوان
    contentHTML += `
        <h2 style="color: #ffffff; font-size: 1.5rem; font-weight: bold; margin-bottom: 15px; font-family: Arial, sans-serif;">
            ${escapeHtml(dialogTitle)}
        </h2>
    `;

    // إضافة الوصف إذا كان موجود
    if (dialogDescription) {
        contentHTML += `
            <p style="color: #ffffff; margin-bottom: 20px; line-height: 1.6; font-family: Arial, sans-serif;">
                ${escapeHtml(dialogDescription)}
            </p>
        `;
    }

    // إضافة الأزرار
    contentHTML += `
        <div style="display: flex; gap: 15px; justify-content: center; margin-bottom: 20px;">
            <button id="customDialogOkBtn" style="
                background: linear-gradient(45deg, #ffcc00, #ff9800);
                color: #000;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 1rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;
                font-family: Arial, sans-serif;
            ">
                ${escapeHtml(dialogButtonText)}
            </button>
        </div>
    `;

    // إضافة خيار "عدم الظهور مجدداً" إذا كان مفعل
    if (dialog.show_dont_show_again) {
        contentHTML += `
            <div style="margin-top: 15px;">
                <label style="color: #ffffff; font-size: 0.9rem; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 8px; font-family: Arial, sans-serif;">
                    <input type="checkbox" id="dontShowAgainCheckbox" style="margin: 0;">
                    ${dontShowAgainText}
                </label>
            </div>
        `;
    }

    // زر الإغلاق
    contentHTML += `
        <button id="customDialogCloseBtn" style="
            position: absolute;
            top: 15px;
            ${isArabic ? 'left' : 'right'}: 15px;
            background: transparent;
            border: none;
            color: #ffffff;
            font-size: 1.5rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
            font-family: Arial, sans-serif;
        ">&times;</button>
    `;

    dialogContent.innerHTML = contentHTML;
    overlay.appendChild(dialogContent);
    document.body.appendChild(overlay);

    // منع التمرير في الخلفية
    document.body.style.overflow = 'hidden';

    // إضافة مستمعي الأحداث
    const okBtn = document.getElementById('customDialogOkBtn');
    const closeBtn = document.getElementById('customDialogCloseBtn');
    const dontShowCheckbox = document.getElementById('dontShowAgainCheckbox');

    function closeDialog() {
        // حفظ إعداد "عدم الظهور مجدداً" إذا تم تحديده
        if (dontShowCheckbox && dontShowCheckbox.checked) {
            localStorage.setItem(`custom_dialog_${dialog.id}_dont_show`, 'true');
        }

        // إزالة المربع
        overlay.remove();
        document.body.style.overflow = '';

        // متابعة عرض تفاصيل المود
        callback(item);
    }

    okBtn.addEventListener('click', closeDialog);
    closeBtn.addEventListener('click', closeDialog);

    // إغلاق عند النقر خارج المربع
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeDialog();
        }
    });

    // تأثيرات hover للأزرار
    okBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 4px 15px rgba(255, 204, 0, 0.4)';
    });

    okBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'none';
    });

    closeBtn.addEventListener('mouseenter', function() {
        this.style.backgroundColor = 'rgba(255, 204, 0, 0.2)';
    });

    closeBtn.addEventListener('mouseleave', function() {
        this.style.backgroundColor = 'transparent';
    });
}

// دالة مساعدة لتأمين النص من HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// في دالة showModal (النسخة السابقة)
function showModal(item) {
    console.log("[DEBUG] showModal called for item:", item);

    // >>> إضافة التحقق من المربع المخصص هنا <<<
    checkCustomDialog(item, (confirmedItem) => {
        // >>> إضافة التحقق من الشادر هنا <<<
        if (confirmedItem.category === 'Shaders' || confirmedItem.category === 'shaders') { // تحقق من اسم الفئة بدقة
            showShaderPatchWarningDialog(confirmedItem, (shaderConfirmedItem) => {
                // هذا الكود سيتم تنفيذه بعد أن يضغط المستخدم "تم" أو "عدم الظهور مجددًا"
                // استمر في عرض الـ modal كالمعتاد
                displayModalContent(shaderConfirmedItem); // استدعاء دالة جديدة أو باقي كود showModal
            });
            return; // لا تعرض الـ modal مباشرة، انتظر نتيجة التحذير
        }
        // >>> نهاية إضافة التحقق <<<

        // إذا لم يكن شادر، أو إذا تم تأكيد التحذير، اعرض الـ modal
        displayModalContent(confirmedItem);
    });
}

// أنشئ دالة جديدة أو انقل باقي محتوى showModal الأصلي إليها
function displayModalContent(item) {
    const modal = document.getElementById('modal');
    const modalContent = document.getElementById('modalContent');

    if (!modal || !modalContent) {
        console.error("Modal or ModalContent element not found!");
        return;
    }

    if (!item || !item.id) {
        console.error("Invalid item data for modal or incrementing clicks.");
        return;
    }

    incrementModClicks(item.id); // <<< أضف هذا الاستدعاء هنا

    currentModalModId = item.id; // يجب أن يكون هذا السطر موجودًا
    let mainImageIndex = 0;

    // --- Define isDownloaded based on localStorage ---
    const isDownloaded = localStorage.getItem(`downloaded_${item.id}`) === 'true';
    console.log(`showModal: Mod ${item.id} isDownloaded status: ${isDownloaded}`); // Debug log
    // --- End Define isDownloaded ---

    let allImages = ['image/placeholder.png'];
    if (item.image_urls) {
        if (Array.isArray(item.image_urls)) {
            allImages = item.image_urls.filter(url => typeof url === 'string' && url.startsWith('http'));
        } else if (typeof item.image_urls === 'string') {
            try {
                const parsedImages = JSON.parse(item.image_urls);
                if (Array.isArray(parsedImages)) {
                    allImages = parsedImages.filter(url => typeof url === 'string' && url.startsWith('http'));
                } else if (item.image_urls.startsWith('http')) {
                    allImages = [item.image_urls];
                }
            } catch (e) {
                if (item.image_urls.startsWith('http')) {
                    allImages = [item.image_urls];
                }
            }
        }
    }
    if (!Array.isArray(allImages) || allImages.length === 0) {
        allImages = ['image/placeholder.png'];
    }

    const thumbnailsHtml = allImages.length > 1 ? allImages
        .map((image, index) => `
            <img
                src="${image}"
                alt="Thumbnail ${index + 1}"
                class="thumbnail ${index === mainImageIndex ? 'selected' : ''}"
                style="flex-shrink: 0; width: 80px; height: 80px; object-fit: contain; border: 2px solid ${index === mainImageIndex ? 'darkorange' : 'orange'}; border-radius: 5px; cursor: pointer; transition: transform 0.3s ease, border-color 0.3s ease;"
                onclick="updateMainImage('${image}', ${index})"
            >
        `).join('') : '';

    modalContent.innerHTML = `
        <div style="position: relative; margin-bottom: 1rem;">
            <div class="main-image-container" style="position: relative; text-align: center;">
                 <img id="mainImage" src="${allImages[mainImageIndex]}" alt="${item.name || 'Mod Image'}" style="max-width: 100%; height: auto; object-fit: contain; background-color: black; border-radius: 10px; border: 3px solid orange;">
                 <button class="modal-image-close-btn" onclick="closeModal()" title="Close">&times;</button>
                 <!-- أيقونة الاستفهام -->
                 <button id="helpIcon" class="help-icon" onclick="showNewInstallInstructions()" title="كيفية التحميل والتركيب">
                     <i class="fa-solid fa-question"></i>
                 </button>
            </div>
            ${allImages.length > 1 ? `
            <div class="thumbnail-container" style="display: flex; justify-content: center; gap: 10px; margin-top: 10px; overflow-x: auto; padding: 10px 0;">
                ${thumbnailsHtml}
            </div>` : ''}
        </div>
        <h2 style="font-size: 1.75rem; margin-bottom: 0.5rem; text-align: center; color: #f1f5f9;">${item.name || t('mod_name')}</h2>
        <center><div id="text-container" style="margin-bottom: 1rem; padding: 0 15px; color: #cbd5e1;">
            <span class="initial-textD">${getLocalizedDescription(item)}</span>
        </div></center>
        <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 1rem; margin-bottom: 20px; text-align: center;">
            <div class="tag">${t('size')}: ${item.size || 'N/A'}</div>
            <div class="tag">${t('version')}: ${item.version || 'N/A'}</div>
            <div class="tag">${t('category')}: ${item.category || 'N/A'}</div>
        </div>

        <!-- قسم معلومات صانع المود -->
        <div id="creatorInfoSection" style="margin-bottom: 120px; padding: 0 15px;">
            <div style="
                background: rgba(0, 0, 0, 0.3);
                border-radius: 12px;
                padding: 15px;
                border: 2px solid #ffcc00;
                margin-bottom: 15px;
            ">
                <h3 style="color: #ffcc00; margin-bottom: 10px; font-size: 1.1rem; text-align: center;">
                    ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'معلومات صانع المود' : 'Mod Creator Info'}
                </h3>
                <div id="creatorInfoContent" style="color: #ffffff; text-align: center;">
                    <div style="color: #ccc; font-size: 0.9rem;">
                        ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'جاري تحميل معلومات الصانع...' : 'Loading creator info...'}
                    </div>
                </div>
            </div>
        </div>
        <div style="position: fixed; bottom: 0; left: 0; right: 0; background: linear-gradient(to right, #FFCC00, #FFA500); padding: 8px 8px 23px 8px; display: flex; justify-content: center; align-items: center; gap: 6px; box-shadow: 0 -2px 5px rgba(0,0,0,0.2); height: 85px; z-index: 100;">
            <!-- Download Button / Progress Area -->
            <button
                class="download-btn ${isDownloaded ? 'downloaded' : ''}"
                data-mod-id="${item.id}"
                onclick="handleDownload('${item.id}', '${item.name || 'Unnamed Mod'}', '${item.download_url}')"
                title="${isDownloaded ? 'Open Mod' : 'Download Mod'}"
                style="background-color: #ffc107; color: #fff; border: none; padding: 0 12px; border-radius: 8px; font-weight: bold; font-size: 16px; display: flex; align-items: center; justify-content: center; gap: 5px; cursor: pointer; height: 57px; flex-grow: 1; position: relative; overflow: hidden; box-shadow: 0 2px 3px rgba(0,0,0,0.2);"
            >
                <i class="fa-solid ${isDownloaded ? 'fa-folder-open' : 'fa-download'}" style="color: #fff; margin-right: 5px; font-size: 18px;"></i>
                <span class="download-btn-text">${isDownloaded ? 'Open' : 'Download'}</span>
                <!-- Progress Indicator (Initially Hidden) -->
                <div class="download-progress-container" data-mod-id="${item.id}" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); align-items: center; justify-content: center; border-radius: 8px;">
                    <div class="download-progress-bar" data-mod-id="${item.id}" style="position: absolute; top: 0; left: 0; height: 100%; width: 0%; border-radius: 8px; transition: width 0.2s ease-out;"></div>
                    <span class="download-progress-text" data-mod-id="${item.id}" style="position: relative; z-index: 1; color: white; font-size: 14px; font-weight: bold;">0%</span>
                </div>
            </button>
            <!-- End Download Button / Progress Area -->
            <button title="Total Downloads" style="background-color: #ffd700; color: #f1f5f9; border: none; padding: 5px; border-radius: 8px; font-weight: bold; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 3px; cursor: default; width: 55px; height: 57px; box-shadow: 0 2px 3px rgba(0,0,0,0.15);">
                <i class="fa-solid fa-arrow-down" style="font-size: 18px; color: #f1f5f9;"></i>
                <span class="download-count" data-mod-id="${item.id}" style="font-size: 11px; color: #f1f5f9;">${formatCount(item.downloads || 0)}</span>
            </button>
            <button class="like-button" onclick="toggleLike('${item.id}', '${item.name || 'Unnamed Mod'}', this)" title="Like Mod" style="background-color: #ffd700; color: #f1f5f9; border: none; padding: 5px; border-radius: 8px; font-weight: bold; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 3px; cursor: pointer; width: 55px; height: 57px; box-shadow: 0 2px 3px rgba(0,0,0,0.15);">
                <i class="fa-solid fa-heart heart-icon" style="font-size: 18px; color: #f1f5f9;"></i>
                <span class="like-count" data-mod-id="${item.id}" style="font-size: 11px; color: #f1f5f9;">${formatCount(item.likes || 0)}</span>
            </button>
            <button onclick="showModCreatorInfo('${item.id}')" title="Mod Creator Info" style="background-color: #ffd700; color: #f1f5f9; border: none; padding: 5px; border-radius: 8px; font-weight: bold; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 3px; cursor: pointer; width: 55px; height: 57px; box-shadow: 0 2px 3px rgba(0,0,0,0.15);">
                <i class="fa-solid fa-copyright" style="font-size: 18px; color: #f1f5f9;"></i>
                <span style="font-size: 11px; color: #f1f5f9;">Creator</span>
            </button>
        </div>
    `;

    modal.style.display = "block";
    document.documentElement.classList.add('modal-open'); // Add class to HTML
    document.body.classList.add('modal-open');

    // Hide bottom bar when modal opens
    const bottomBar = document.querySelector('.bottom-fixed-bar');
    if (bottomBar) bottomBar.classList.add('hidden');

    // --- ADDED: Increment clicks when modal is shown ---
    incrementModClicks(item.id);
    // --- END ADDED ---

    // --- إضافة تأثيرات أيقونة الاستفهام ---
    setTimeout(() => {
        initializeHelpIcon();
    }, 100); // تأخير قصير للتأكد من تحميل العناصر

    // تحميل معلومات صانع المود في القسم الجديد
    setTimeout(() => {
        loadCreatorInfoInSection(item.id);
    }, 200);
}

// متغير لمنع النقر المتعدد
let isCreatorInfoLoading = false;

// دالة تحميل معلومات صانع المود في القسم
async function loadCreatorInfoInSection(modId) {
    try {
        const contentElement = document.getElementById('creatorInfoContent');
        if (!contentElement) return;

        // جلب بيانات المود من قاعدة البيانات
        const { data: mod, error } = await supabaseClient
            .from('mods')
            .select('creator_name, creator_contact_info, creator_social_channels, custom_social_site_name, custom_social_site_url')
            .eq('id', modId)
            .single();

        if (error) {
            console.error('خطأ في جلب بيانات صانع المود:', error);
            contentElement.innerHTML = `
                <div style="color: #ff6b6b; font-size: 0.9rem;">
                    ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'خطأ في تحميل معلومات الصانع' : 'Error loading creator info'}
                </div>
            `;
            return;
        }

        const isArabic = (localStorage.getItem('selectedLanguage') || 'en') === 'ar';

        // إنشاء محتوى معلومات الصانع
        let creatorContent = '';

        // اسم الصانع
        if (mod.creator_name) {
            creatorContent += `
                <div style="margin-bottom: 10px;">
                    <span style="color: #ffcc00; font-weight: bold;">
                        ${isArabic ? 'الصانع:' : 'Creator:'}
                    </span>
                    <span style="color: #ffffff; margin-left: 8px;">
                        ${mod.creator_name}
                    </span>
                </div>
            `;
        }

        // وسائل التواصل الاجتماعي
        let socialChannelsHtml = '';
        if (mod.creator_social_channels) {
            try {
                const socialChannels = typeof mod.creator_social_channels === 'string'
                    ? JSON.parse(mod.creator_social_channels)
                    : mod.creator_social_channels;

                const socialIcons = {
                    youtube: { icon: 'fab fa-youtube', color: '#FF0000' },
                    twitter: { icon: 'fab fa-twitter', color: '#1DA1F2' },
                    instagram: { icon: 'fab fa-instagram', color: '#E4405F' },
                    facebook: { icon: 'fab fa-facebook', color: '#1877F2' },
                    discord: { icon: 'fab fa-discord', color: '#5865F2' },
                    tiktok: { icon: 'fab fa-tiktok', color: '#000000' },
                    telegram: { icon: 'fab fa-telegram', color: '#0088CC' },
                    github: { icon: 'fab fa-github', color: '#333333' }
                };

                socialChannelsHtml = Object.entries(socialChannels)
                    .filter(([platform, url]) => url && url.trim())
                    .map(([platform, url]) => {
                        const iconData = socialIcons[platform.toLowerCase()] || { icon: 'fas fa-link', color: '#ffcc00' };
                        return `
                            <a href="${url}" target="_blank" style="
                                display: inline-flex;
                                align-items: center;
                                justify-content: center;
                                width: 30px;
                                height: 30px;
                                background: ${iconData.color};
                                border-radius: 50%;
                                margin: 3px;
                                text-decoration: none;
                                transition: transform 0.2s ease;
                                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                            " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="${iconData.icon}" style="color: white; font-size: 14px;"></i>
                            </a>
                        `;
                    }).join('');
            } catch (e) {
                console.error('خطأ في تحليل قنوات التواصل الاجتماعي:', e);
            }
        }

        // إضافة الموقع المخصص إذا كان موجود
        if (mod.custom_social_site_url && mod.custom_social_site_name) {
            socialChannelsHtml += `
                <a href="${mod.custom_social_site_url}" target="_blank" style="
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 30px;
                    height: 30px;
                    background: #ffcc00;
                    border-radius: 50%;
                    margin: 3px;
                    text-decoration: none;
                    transition: transform 0.2s ease;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'" title="${mod.custom_social_site_name}">
                    <i class="fas fa-globe" style="color: #000; font-size: 14px;"></i>
                </a>
            `;
        }

        if (socialChannelsHtml) {
            creatorContent += `
                <div style="margin-bottom: 10px;">
                    <div style="color: #ffcc00; font-weight: bold; margin-bottom: 8px;">
                        ${isArabic ? 'وسائل التواصل:' : 'Social Media:'}
                    </div>
                    <div style="text-align: center;">
                        ${socialChannelsHtml}
                    </div>
                </div>
            `;
        }

        // إذا لم توجد معلومات
        if (!creatorContent) {
            creatorContent = `
                <div style="color: #ccc; font-size: 0.9rem;">
                    ${isArabic ? 'لا توجد معلومات متاحة عن الصانع' : 'No creator information available'}
                </div>
            `;
        }

        contentElement.innerHTML = creatorContent;

    } catch (error) {
        console.error('خطأ في تحميل معلومات صانع المود:', error);
        const contentElement = document.getElementById('creatorInfoContent');
        if (contentElement) {
            contentElement.innerHTML = `
                <div style="color: #ff6b6b; font-size: 0.9rem;">
                    ${(localStorage.getItem('selectedLanguage') || 'en') === 'ar' ? 'خطأ في تحميل معلومات الصانع' : 'Error loading creator info'}
                </div>
            `;
        }
    }
}

// دالة عرض معلومات صانع المود
async function showModCreatorInfo(modId) {
    // منع النقر المتعدد
    if (isCreatorInfoLoading) {
        console.log('⚠️ Creator info is already loading, ignoring click');
        return;
    }

    // التحقق من وجود مربع مفتوح مسبقاً
    const existingOverlay = document.getElementById('creator-info-overlay');
    if (existingOverlay) {
        console.log('⚠️ Creator info dialog already open, ignoring click');
        return;
    }

    // تعيين حالة التحميل
    isCreatorInfoLoading = true;
    console.log('🔄 Loading creator info for mod:', modId);

    try {
        // عرض مؤشر تحميل سريع
        showQuickLoadingIndicator();

        // جلب بيانات المود من قاعدة البيانات
        const { data: mod, error } = await supabaseClient
            .from('mods')
            .select('creator_name, creator_contact_info, creator_social_channels, custom_social_site_name, custom_social_site_url, name')
            .eq('id', modId)
            .single();

        if (error) {
            console.error('خطأ في جلب بيانات صانع المود:', error);
            return;
        }

        // التحقق من وجود حقوق طبع مخصصة لهذا المود
        const { data: customCopyright, error: copyrightError } = await supabaseClient
            .from('custom_copyright_mods')
            .select('is_active')
            .eq('mod_id', modId)
            .eq('is_active', true)
            .single();

        const hasCustomCopyright = !copyrightError && customCopyright;

        // الحصول على لغة المستخدم
        const userLanguage = localStorage.getItem('selectedLanguage') || 'en';
        const isArabic = userLanguage === 'ar';

        // إنشاء المربع
        const overlay = document.createElement('div');
        overlay.id = 'creator-info-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100000;
            animation: fadeIn 0.3s ease;
        `;

        const modal = document.createElement('div');
        modal.style.cssText = `
            background: #000000;
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            border: 3px solid #ffcc00;
            box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
            animation: slideInUp 0.4s ease;
            position: relative;
            direction: ${isArabic ? 'rtl' : 'ltr'};
        `;

        // إنشاء المحتوى
        let socialChannelsHtml = '';
        if (mod.creator_social_channels) {
            try {
                const socialChannels = typeof mod.creator_social_channels === 'string'
                    ? JSON.parse(mod.creator_social_channels)
                    : mod.creator_social_channels;

                const socialIcons = {
                    youtube: { icon: 'fab fa-youtube', color: '#FF0000' },
                    twitter: { icon: 'fab fa-twitter', color: '#1DA1F2' },
                    instagram: { icon: 'fab fa-instagram', color: '#E4405F' },
                    facebook: { icon: 'fab fa-facebook', color: '#1877F2' },
                    discord: { icon: 'fab fa-discord', color: '#5865F2' },
                    tiktok: { icon: 'fab fa-tiktok', color: '#000000' },
                    telegram: { icon: 'fab fa-telegram', color: '#0088CC' },
                    github: { icon: 'fab fa-github', color: '#333333' }
                };

                socialChannelsHtml = Object.entries(socialChannels)
                    .filter(([platform, url]) => url && url.trim())
                    .map(([platform, url]) => {
                        const iconData = socialIcons[platform.toLowerCase()] || { icon: 'fas fa-link', color: '#ffcc00' };
                        return `
                            <a href="${url}" target="_blank" style="
                                display: inline-flex;
                                align-items: center;
                                justify-content: center;
                                width: 45px;
                                height: 45px;
                                background: ${iconData.color};
                                border-radius: 50%;
                                margin: 5px;
                                text-decoration: none;
                                transition: transform 0.2s ease;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                            " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="${iconData.icon}" style="color: white; font-size: 20px;"></i>
                            </a>
                        `;
                    }).join('');
            } catch (e) {
                console.error('خطأ في تحليل قنوات التواصل الاجتماعي:', e);
            }
        }

        // إضافة الموقع المخصص إذا كان موجود
        if (mod.custom_social_site_url && mod.custom_social_site_name) {
            socialChannelsHtml += `
                <a href="${mod.custom_social_site_url}" target="_blank" style="
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 45px;
                    height: 45px;
                    background: #ffcc00;
                    border-radius: 50%;
                    margin: 5px;
                    text-decoration: none;
                    transition: transform 0.2s ease;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'" title="${mod.custom_social_site_name}">
                    <i class="fas fa-globe" style="color: #000; font-size: 20px;"></i>
                </a>
            `;
        }

        const copyrightText = {
            title: t('creator_info_title'),
            creatorLabel: t('creator_label'),
            socialLabel: t('social_label'),
            copyrightTitle: t('copyright_title'),
            copyrightDesc: hasCustomCopyright ? t('custom_copyright_desc') : t('copyright_desc'),
            contactTitle: t('contact_title'),
            contactInfo: t('contact_info'),
            noCreator: t('no_creator'),
            noSocial: t('no_social'),
            closeBtn: t('close_btn')
        };

        modal.innerHTML = `
            <!-- زر الإغلاق -->
            <button onclick="closeCreatorInfo()" style="
                position: absolute;
                top: 15px;
                ${isArabic ? 'left' : 'right'}: 15px;
                background: transparent;
                border: none;
                color: #ffcc00;
                font-size: 1.5rem;
                cursor: pointer;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s;
            " onmouseover="this.style.backgroundColor='rgba(255,204,0,0.2)'" onmouseout="this.style.backgroundColor='transparent'">&times;</button>

            <!-- العنوان -->
            <h2 style="
                color: #ffcc00;
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 20px;
                text-align: center;
                font-family: 'Press Start 2P', 'Minecraft-Fallback', 'Courier New', monospace;
                text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
            ">
                <i class="fa-solid fa-copyright" style="margin-${isArabic ? 'left' : 'right'}: 10px;"></i>
                ${copyrightText.title}
            </h2>

            <!-- اسم صانع المود -->
            <div style="margin-bottom: 20px;">
                <h3 style="color: #ffcc00; margin-bottom: 10px; font-size: 1.1rem;">
                    ${copyrightText.creatorLabel}
                </h3>
                <p style="
                    color: white;
                    background: rgba(255, 204, 0, 0.1);
                    padding: 10px;
                    border-radius: 8px;
                    border: 1px solid #ffcc00;
                    font-size: 1rem;
                    font-weight: bold;
                ">
                    ${mod.creator_name || copyrightText.noCreator}
                </p>
            </div>

            <!-- وسائل التواصل الاجتماعي -->
            <div style="margin-bottom: 25px;">
                <h3 style="color: #ffcc00; margin-bottom: 15px; font-size: 1.1rem;">
                    ${copyrightText.socialLabel}
                </h3>
                <div style="
                    text-align: center;
                    background: rgba(255, 204, 0, 0.1);
                    padding: 15px;
                    border-radius: 8px;
                    border: 1px solid #ffcc00;
                    min-height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-wrap: wrap;
                ">
                    ${socialChannelsHtml || `<p style="color: #888; margin: 0;">${copyrightText.noSocial}</p>`}
                </div>
            </div>

            <!-- معلومات حقوق الطبع والنشر -->
            <div style="
                background: ${hasCustomCopyright ? 'rgba(255, 107, 107, 0.1)' : 'rgba(255, 204, 0, 0.1)'};
                border: 2px solid ${hasCustomCopyright ? '#ff6b6b' : '#ffcc00'};
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            ">
                <h3 style="
                    color: ${hasCustomCopyright ? '#ff6b6b' : '#ffcc00'};
                    margin-bottom: 15px;
                    font-size: 1.1rem;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                ">
                    <i class="${hasCustomCopyright ? 'fa-solid fa-exclamation-triangle' : 'fa-solid fa-shield-alt'}"></i>
                    ${copyrightText.copyrightTitle}
                    ${hasCustomCopyright ? '<span style="background: #ff6b6b; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.7rem; margin-left: 8px;">خاص</span>' : ''}
                </h3>
                <p style="
                    color: white;
                    line-height: 1.6;
                    margin-bottom: 15px;
                    font-size: 0.95rem;
                ">
                    ${copyrightText.copyrightDesc}
                </p>

                <div style="
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 8px;
                    padding: 15px;
                    border-left: 4px solid #ffcc00;
                ">
                    <h4 style="color: #ffcc00; margin-bottom: 10px; font-size: 1rem;">
                        ${copyrightText.contactTitle}
                    </h4>
                    <p style="color: #ffffff; margin: 0 0 10px 0; font-size: 0.9rem;">
                        ${copyrightText.contactInfo}
                    </p>
                    <div style="
                        background: rgba(255, 204, 0, 0.1);
                        border: 1px solid #ffcc00;
                        border-radius: 6px;
                        padding: 10px;
                        margin-top: 10px;
                    ">
                        <p style="color: #ffcc00; margin: 0; font-size: 0.9rem; font-weight: bold;">
                            📧 للتواصل / Contact:
                            <a href="mailto:<EMAIL>" style="color: #ffffff; text-decoration: none;">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- زر الإغلاق -->
            <div style="text-align: center;">
                <button onclick="closeCreatorInfo()" style="
                    background: linear-gradient(45deg, #ffcc00, #ff9800);
                    color: #ffffff;
                    border: none;
                    padding: 12px 30px;
                    border-radius: 8px;
                    font-size: 1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s;
                " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(255,204,0,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    ${copyrightText.closeBtn}
                </button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // إخفاء مؤشر التحميل بعد نجاح العملية
        hideQuickLoadingIndicator();

        // منع التمرير في الخلفية
        document.body.style.overflow = 'hidden';

        console.log('✅ Creator info dialog displayed successfully');

        // إغلاق عند النقر خارج المربع
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                closeCreatorInfo();
            }
        });

    } catch (error) {
        console.error('❌ خطأ في عرض معلومات صانع المود:', error);

        // إزالة مؤشر التحميل في حالة الخطأ
        hideQuickLoadingIndicator();

        // عرض رسالة خطأ للمستخدم
        showErrorMessage('حدث خطأ في تحميل معلومات صانع المود');

    } finally {
        // إعادة تعيين حالة التحميل
        isCreatorInfoLoading = false;
        console.log('✅ Creator info loading completed');
    }
}

// دالة إغلاق مربع معلومات صانع المود
function closeCreatorInfo() {
    const overlay = document.getElementById('creator-info-overlay');
    if (overlay) {
        overlay.remove();
        document.body.style.overflow = '';
    }

    // إزالة مؤشر التحميل إذا كان موجود
    hideQuickLoadingIndicator();

    // إعادة تعيين حالة التحميل
    isCreatorInfoLoading = false;
    console.log('🔒 Creator info dialog closed');
}

// دالة عرض مؤشر تحميل سريع
function showQuickLoadingIndicator() {
    // إزالة أي مؤشر تحميل موجود
    hideQuickLoadingIndicator();

    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'quick-loading-indicator';
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 99999;
        animation: fadeIn 0.2s ease;
    `;

    const spinner = document.createElement('div');
    spinner.style.cssText = `
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 204, 0, 0.3);
        border-top: 4px solid #ffcc00;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    `;

    loadingOverlay.appendChild(spinner);
    document.body.appendChild(loadingOverlay);

    console.log('⏳ Quick loading indicator shown');
}

// دالة إخفاء مؤشر التحميل السريع
function hideQuickLoadingIndicator() {
    const loadingOverlay = document.getElementById('quick-loading-indicator');
    if (loadingOverlay) {
        loadingOverlay.remove();
        console.log('✅ Quick loading indicator hidden');
    }
}

// دالة عرض رسالة خطأ
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #ff4444;
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        z-index: 100001;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        animation: slideInUp 0.3s ease;
    `;
    errorDiv.textContent = message;

    document.body.appendChild(errorDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 3000);
}

// دالة تهيئة أيقونة الاستفهام مع التأثيرات
function initializeHelpIcon() {
    const helpIcon = document.getElementById('helpIcon');
    if (!helpIcon) return;

    // إظهار الأيقونة واضحة في البداية
    helpIcon.style.opacity = '1';
    helpIcon.style.transform = 'scale(1)';

    // بعد 3 ثوان، جعل الأيقونة شفافة
    setTimeout(() => {
        helpIcon.style.opacity = '0.3';
    }, 3000);

    // إضافة مستمع النقر
    helpIcon.addEventListener('click', function() {
        // جعل الأيقونة واضحة مؤقتاً عند النقر
        this.style.opacity = '1';
        this.style.transform = 'scale(1.1)';

        // إضافة تأثير نبضة
        this.classList.add('help-icon-pulse');

        // إزالة التأثير بعد فترة قصيرة
        setTimeout(() => {
            this.style.transform = 'scale(1)';
            this.classList.remove('help-icon-pulse');

            // العودة للشفافية بعد ثانية واحدة
            setTimeout(() => {
                this.style.opacity = '0.3';
            }, 1000);
        }, 200);
    });

    // إضافة تأثير hover
    helpIcon.addEventListener('mouseenter', function() {
        this.style.opacity = '1';
        this.style.transform = 'scale(1.05)';
    });

    helpIcon.addEventListener('mouseleave', function() {
        this.style.opacity = '0.3';
        this.style.transform = 'scale(1)';
    });
}

window.updateMainImage = (src, index) => {
    const mainImage = document.getElementById('mainImage');
    if (mainImage) {
        mainImage.src = src;
    }
    const thumbnails = document.querySelectorAll('#modalContent .thumbnail');
    thumbnails.forEach((img, i) => {
        img.classList.toggle('selected', i === index);
        img.style.borderColor = i === index ? 'darkorange' : 'orange';
    });
};

// --- Infinite Scroll / Load More (Currently not used, keep if needed later) ---
function addLoadingIndicator(container, sectionModsContext) { /* ... */ }
function loadMoreMods(container, loadingIndicator, sectionModsContext) { /* ... */ }


// --- Filtering Logic (Updated: Switch Display Function) ---
function filterItems(category) {
    // console.log(`*** filterItems ENTRY POINT for category: ${category} ***`); // Removed debug log
    console.log(`>>> filterItems called for category: ${category}`); // Existing log
    console.log(`Filtering by category: ${category}`);
    currentCategory = category; // Update global state

    // Set default sort criteria based on category
    if (['Addons', 'Texture', 'Shaders', 'Maps', 'Seeds', 'Skin Pack'].includes(category)) {
        currentSortBy = 'likes';
        currentSortAscending = false; // Most liked first
    } else if (category === 'News') {
        currentSortBy = 'created_at';
        currentSortAscending = false; // Newest first for News
    } else if (category === 'Suggested') {
        currentSortBy = 'created_at';
        currentSortAscending = false; // Newest first for Suggested
    } else {
        // Default for 'All' (though displayModsBySection handles its own fetching)
        // or any other future categories.
        currentSortBy = 'created_at';
        currentSortAscending = false; // Newest first
    }
    updateActiveSortButton(); // Update UI for sort buttons

    // Decide which display function to call
    if (category === 'All') {
        displayModsBySection();
    } else {
        // For single category view (Addons, Texture, Shaders, etc.)
        // displaySingleCategory will use the currentSortBy and currentSortAscending set above
        displaySingleCategory(category, currentSortBy, currentSortAscending);
    }

    // Update active category button in the top bar
    document.querySelectorAll('#categories .category-btn').forEach(btn => {
        const btnCategory = btn.getAttribute('data-category');
        btn.classList.toggle('active-category', btnCategory === category);
    });
}

// --- Sorting Logic (Re-added) ---
function sortMods(criteria) {
     console.log(`>>> sortMods called for criteria: ${criteria}`); // Added entry log
     console.log(`Sorting by: ${criteria}`);
     let sortBy = 'created_at';
     let ascending = false;

     switch(criteria) {
        case 'likes':
            sortBy = 'likes';
            ascending = false; // Most first
            break;
        case 'downloads':
            sortBy = 'downloads';
            ascending = false; // Most first
            break;
        case 'newest':
            sortBy = 'created_at';
            ascending = false; // Newest first (descending)
            break;
        case 'oldest':
            sortBy = 'created_at';
            ascending = true; // Oldest first (ascending)
            break;
        default:
            sortBy = 'created_at';
            ascending = false;
     }

     // Update global sort state
     currentSortBy = sortBy;
     currentSortAscending = ascending;

     // Re-display the current single category with the new sort order
     // Ensure currentCategory is not 'All' before calling displaySingleCategory
     if (currentCategory !== 'All') {
         displaySingleCategory(currentCategory, currentSortBy, currentSortAscending);
     } else {
         console.warn("Sorting called while 'All' category is active. This shouldn't happen.");
     }

     // Highlight active sort button
     updateActiveSortButton(criteria);
}

// Helper to update active sort button visuals
function updateActiveSortButton(activeCriteria = null) {
    document.querySelectorAll('.sort-btn').forEach(btn => {
        const onclickAttr = btn.getAttribute('onclick');
        const match = onclickAttr ? onclickAttr.match(/sortMods\('([^']+)'\)/) : null;
        const btnCriteria = match ? match[1] : null;

        // Determine the criteria represented by the current global state
        let currentCriteria = null;
        if (currentSortBy === 'created_at') {
            currentCriteria = currentSortAscending ? 'oldest' : 'newest';
        } else if (currentSortBy === 'likes') {
            currentCriteria = 'likes';
        } else if (currentSortBy === 'downloads') {
            currentCriteria = 'downloads';
        }

        // Activate the button corresponding to the current sort state
        btn.classList.toggle('active-sort', btnCriteria === currentCriteria);
    });
}


// --- Create Banner Ads Table if it doesn't exist ---
async function createBannerAdsTableIfNeeded() {
    try {
        // Check if the table exists by trying to select from it
        const { error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('id')
            .limit(1);

        // If there's a permission error or the table doesn't exist
        if (error && (error.message.includes('permission denied') || error.message.includes('does not exist'))) {
            console.log('Banner ads table might not exist.');
            console.warn('Please create the banner_ads table manually in Supabase SQL Editor.');
            console.info('Execute the SQL from database/missing_tables.sql to create all required tables.');

            // Try to create the table using RPC if available
            try {
                const { error: createError } = await supabaseClient.rpc('execute_sql', {
                    sql_query: `
                        CREATE TABLE IF NOT EXISTS ${BANNER_ADS_TABLE} (
                            id SERIAL PRIMARY KEY,
                            title TEXT,
                            description TEXT,
                            image_url TEXT NOT NULL,
                            target_url TEXT,
                            display_order INTEGER DEFAULT 0,
                            is_active BOOLEAN DEFAULT true,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                        );
                    `
                });

                if (createError) {
                    console.warn('Could not create banner ads table via RPC:', createError.message);
                    console.warn('Please execute database/missing_tables.sql in Supabase SQL Editor');
                } else {
                    console.log('Banner ads table created successfully');
                }
            } catch (rpcError) {
                console.warn('RPC function execute_sql not available. Please create tables manually.');
                console.info('Execute database/missing_tables.sql in Supabase SQL Editor');
            }

            // Also check the featured_addons table
            try {
                const { error: featuredError } = await supabaseClient
                    .from(FEATURED_ADDONS_TABLE)
                    .select('id')
                    .limit(1);

                if (featuredError && (featuredError.message.includes('permission denied') || featuredError.message.includes('does not exist'))) {
                    console.log('Featured addons table might not exist.');

                    try {
                        const { error: createFeaturedError } = await supabaseClient.rpc('execute_sql', {
                            sql_query: `
                                CREATE TABLE IF NOT EXISTS ${FEATURED_ADDONS_TABLE} (
                                    id SERIAL PRIMARY KEY,
                                    mod_id UUID NOT NULL REFERENCES mods(id),
                                    is_active BOOLEAN DEFAULT true,
                                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                                );
                            `
                        });

                        if (createFeaturedError) {
                            console.warn('Could not create featured addons table via RPC:', createFeaturedError.message);
                        } else {
                            console.log('Featured addons table created successfully');
                        }
                    } catch (rpcError) {
                        console.warn('RPC function execute_sql not available for featured addons table.');
                    }
                }
            } catch (err) {
                console.warn('Error checking featured addons table:', err.message);
            }
        } else {
            console.log('Banner ads table already exists');
        }
    } catch (error) {
        console.error('Unexpected error checking/creating banner ads table:', error);
    }
}

// --- Initial Load & Event Listeners ---
document.addEventListener("DOMContentLoaded", function () {
    console.log(">>> DOMContentLoaded event fired."); // Added log

    // Initialize Supabase if not already done
    if (!supabaseClient) {
        initializeSupabase();
    }
    const categoriesContainer = document.getElementById("categories");
    const sortButtons = document.querySelectorAll("#sortButtons .sort-btn");
    const mainContentContainers = [
        document.getElementById('addons-section'),
        document.getElementById('texture-pack-section'),
        document.getElementById('shaders-section'),
        document.getElementById('singleCategoryContainer')
    ].filter(Boolean); // Filter out nulls if some containers don't exist

    // --- Event Delegation for Category Buttons (Moved to separate function) ---
    // Removed listener attachment from here
    // --- End Event Delegation ---


    // REMOVED Event Delegation for Mod Item Clicks


    // Add click listeners to SORT buttons (keep individual listeners attached on DOMContentLoaded)
    sortButtons.forEach(button => {
        button.addEventListener("click", function(event) {
            const onclickAttr = this.getAttribute('onclick');
            const match = onclickAttr ? onclickAttr.match(/sortMods\('([^']+)'\)/) : null;
            if (match && match[1]) {
                sortMods(match[1]); // Call sortMods with the criteria
            }
        });
    });


    const searchButton = document.querySelector(".search-btn");
    if (searchButton) {
        searchButton.addEventListener("click", () => {
            if (window.location.pathname.endsWith('search.html')) {
                 console.log("Already on search page.");
                 return;
            }
            window.location.href = 'search.html';
        });
    } else {
        console.warn("Search button not found.");
    }

    // --- Initial Page Load ---
    // Apply the animation class to the main content wrapper
    const mainContentWrapper = document.getElementById('main-content-wrapper');
    if (mainContentWrapper) {
        mainContentWrapper.classList.add('animate-visible');
    }

    // Set 'All' active initially using data-category
    const allButton = document.querySelector("#categories button[data-category='All']");
    if (allButton) {
        allButton.classList.add('active-category');
    } else {
        console.warn("'All' category button not found for initial activation.");
    }

    // Initial display is "All" category (sectioned view)
    // Defer the initial heavy load slightly to allow UI/listeners to become responsive first
    setTimeout(async () => {
        console.log(">>> Starting initial data load (displayModsBySection) (after 50ms delay)..."); // Added log

        try {
            // Check network connectivity before starting
            const isConnected = await checkNetworkConnectivity();
            if (!isConnected) {
                console.error('No network connectivity detected during initial load');
                showNetworkError();
                return;
            }

            // تهيئة نظام الشعبية الديناميكي
            await initializePopularitySystem();

            // --- Re-enabled initial load ---
            displayModsBySection();
            // --- End Re-enabled ---
            updateActiveSortButton(); // Set initial sort button state (though hidden)
            console.log(">>> Initial data load function called."); // Re-enabled log

            // REMOVED: Call to simulateInitialClick
            // setTimeout(simulateInitialClick, 200);
        } catch (error) {
            console.error('Error during initial data load:', error);
            if (error.message && (error.message.includes('Failed to fetch') ||
                                  error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                                  error.message.includes('TypeError: Failed to fetch'))) {
                showNetworkError();
            }
        }

    }, 50); // Keep slight delay for initial data load


    // --- Button Press Animation (Listeners removed to potentially fix click delay issue) ---
    // document.querySelectorAll(".download-btn, .like-button, .category-btn, .sort-btn, .close-btn, .back-btn").forEach(button => {
    //     const removePressedClass = () => button.classList.remove('button-pressed');
    //     // Touch listeners removed
    // });

    // Initialize lazy loading for initially loaded images if any (though display functions handle it now)
    // initializeLazyLoading('.lazy-load');

    // Fetch and display update notification
    fetchAndDisplayUpdateNotification();
    fetchAndDisplayAppAnnouncements(); // أضف هذا الاستدعاء
    loadDrawerLinks(); // أضف هذا الاستدعاء
    createBannerAdsTableIfNeeded(); // Create banner ads table and featured addons table if they don't exist

    // Update language link text after drawer links are loaded
    setTimeout(() => {
        updateLanguageLinkText();
    }, 100);

    // Reverted: Removed simulated touch/click events as they didn't solve the issue.
});

async function loadDrawerLinks() {
    const drawerContainer = document.querySelector(".drawer");

    if (!drawerContainer) {
        console.error("Drawer container element (.drawer) not found.");
        return;
    }

    // Add admin link at the top of the drawer
    const adminLinkContainer = document.createElement('div');
    adminLinkContainer.id = 'admin-link-container';

    const adminLink = document.createElement('a');
    adminLink.href = 'admin/admin.html';
    adminLink.innerHTML = '<i class="fas fa-cog"></i> لوحة الإدارة';
    adminLink.style.cssText = `
        display: block; padding: 12px 15px; text-decoration: none;
        color: #ffd700; /* Gold color for admin link */
        border-bottom: 1px solid #444;
        transition: background-color 0.2s;
        margin-bottom: 15px;
        font-weight: bold;
    `;
    adminLink.onmouseover = () => adminLink.style.backgroundColor = '#444';
    adminLink.onmouseout = () => adminLink.style.backgroundColor = 'transparent';

    adminLinkContainer.appendChild(adminLink);

    // Add language settings link
    const languageLinkContainer = document.createElement('div');
    languageLinkContainer.id = 'language-link-container';

    const languageLink = document.createElement('a');
    languageLink.href = '#';
    languageLink.onclick = (e) => {
        e.preventDefault();
        showLanguageSelectionModal();
        // Close drawer after clicking
        if (drawer) drawer.classList.remove("active");
        if (overlay) overlay.classList.remove("active");
    };

    // Set text based on current language
    const currentLang = localStorage.getItem('selectedLanguage') || 'en';
    const languageText = currentLang === 'ar' ? 'تعديل اللغة' : 'Change Language';
    const languageIcon = currentLang === 'ar' ? '🌍' : '🌍';

    languageLink.innerHTML = `${languageIcon} ${languageText}`;
    languageLink.id = 'language-link'; // Add ID for easy updating
    languageLink.style.cssText = `
        display: block; padding: 12px 15px; text-decoration: none;
        color: #4ade80; /* Green color for language link */
        border-bottom: 1px solid #444;
        transition: background-color 0.2s;
        margin-bottom: 15px;
        font-weight: bold;
    `;
    languageLink.onmouseover = () => languageLink.style.backgroundColor = '#444';
    languageLink.onmouseout = () => languageLink.style.backgroundColor = 'transparent';

    languageLinkContainer.appendChild(languageLink);

    // Add the admin link at the top
    if (drawerContainer.firstChild) {
        drawerContainer.insertBefore(adminLinkContainer, drawerContainer.firstChild);
        drawerContainer.insertBefore(languageLinkContainer, adminLinkContainer.nextSibling);
    } else {
        drawerContainer.appendChild(adminLinkContainer);
        drawerContainer.appendChild(languageLinkContainer);
    }

    // Find or create a specific container for dynamic links
    let dynamicLinksContainer = drawerContainer.querySelector("#dynamic-drawer-links");
    if (!dynamicLinksContainer) {
        console.log("Creating #dynamic-drawer-links container inside .drawer");
        dynamicLinksContainer = document.createElement('div');
        dynamicLinksContainer.id = 'dynamic-drawer-links';
        // Prepend or append based on where you want the dynamic links relative to static ones
        drawerContainer.appendChild(dynamicLinksContainer); // Append at the end
    }

    console.log("Fetching dynamic drawer links...");
    // Show loading message inside the specific container
    dynamicLinksContainer.innerHTML = '<p style="padding: 15px; color: #aaa;">Loading links...</p>';

    try {
        const { data: links, error } = await supabaseClient
            .from(DRAWER_LINKS_TABLE)
            .select('text, url, icon_class')
            .eq('is_active', true)
            .order('order', { ascending: true });

        if (error) {
             // Check for RLS errors specifically
             if (error.message.includes("permission denied")) {
                 console.error("Permission denied fetching drawer links. Check RLS policies for anon key on 'drawer_links' table.");
                 throw new Error("Permission denied fetching drawer links.");
             } else {
                throw error; // Re-throw other errors
             }
        }

        console.log("Fetched drawer links data:", links); // Log fetched data

        // Clear only the dynamic links container
        dynamicLinksContainer.innerHTML = '';

        if (links && links.length > 0) {
            links.forEach(link => {
                const linkElement = document.createElement('a');
                linkElement.href = link.url || '#';
                if (link.url && (link.url.startsWith('http') || link.url.startsWith('https'))) {
                    linkElement.target = '_blank'; // فتح الروابط الخارجية في نافذة جديدة
                    linkElement.rel = 'noopener noreferrer'; // للأمان
                }

                let iconHtml = '';
                if (link.icon_class) {
                    // تحقق إذا كنت تستخدم FontAwesome أو صورًا للأيقونات
                    // إذا FontAwesome:
                    // iconHtml = `<i class="${link.icon_class}" style="margin-right: 8px;"></i> `;
                    // إذا صور (عدّل حسب حاجتك):
                    // iconHtml = `<img src="${link.icon_class}" alt="" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;"> `;
                    // الكود من النسخة الحالية يستخدم FontAwesome:
                    if (link.icon_class.startsWith('fa-') || link.icon_class.startsWith('fas ') || link.icon_class.startsWith('fab ') || link.icon_class.startsWith('far ')) {
                         iconHtml = `<i class="${link.icon_class}" style="margin-right: 8px; width: 20px; text-align: center;"></i> `;
                    } else {
                        // افترض أنه مسار صورة إذا لم يكن كلاس FontAwesome
                        iconHtml = `<img src="${link.icon_class}" alt="" style="width: 20px; height: 20px; margin-right: 8px; vertical-align: middle;"> `;
                    }
                }

                linkElement.innerHTML = `${iconHtml}${link.text || 'Link'}`;
                // أضف كلاسات للرابط إذا لزم الأمر للتنسيق
                // linkElement.className = 'drawer-link-item';
                linkElement.style.cssText = `
                    display: block; padding: 12px 15px; text-decoration: none;
                    color: #ddd; /* أو لون مناسب */ border-bottom: 1px solid #444;
                    transition: background-color 0.2s;
                `; // أنماط أساسية
                linkElement.onmouseover = () => linkElement.style.backgroundColor = '#444';
                linkElement.onmouseout = () => linkElement.style.backgroundColor = 'transparent';

                dynamicLinksContainer.appendChild(linkElement); // Use the correct container variable
            });
            console.log(`Successfully loaded ${links.length} drawer links.`);
        } else {
             // Display message inside the specific container
            dynamicLinksContainer.innerHTML = '<p style="padding: 15px; color: #aaa;">No links available.</p>';
        }

    } catch (error) {
        console.error("Error fetching or displaying drawer links:", error);
        // Check if it's a network-related error
        if (error.message && (error.message.includes('Failed to fetch') ||
                              error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                              error.message.includes('TypeError: Failed to fetch'))) {
            showNetworkError();
        }
         // Display error inside the specific container
        dynamicLinksContainer.innerHTML = `<p style="padding: 15px; color: #f87171;">Error loading links: ${error.message}</p>`;
    }
}

async function fetchAndDisplayAppAnnouncements() {
    console.log("Fetching active app announcements...");
    try {
        const { data, error } = await supabaseClient
            .from(APP_ANNOUNCEMENTS_TABLE)
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching app announcements:', error);
            // Check if it's a network-related error
            if (error.message && (error.message.includes('Failed to fetch') ||
                                  error.message.includes('ERR_PROXY_CONNECTION_FAILED') ||
                                  error.message.includes('TypeError: Failed to fetch'))) {
                showNetworkError();
            }
            return;
        }

        if (data && data.length > 0) {
            const userId = generateUserId();
            for (const announcement of data) {
                const announcementKey = `announcement_shown_${userId}_${announcement.id}`;
                if (announcement.display_frequency === 'once') {
                    if (localStorage.getItem(announcementKey)) {
                        console.log(`Announcement ${announcement.id} (once) already shown. Skipping.`);
                        continue;
                    }
                }
                createAnnouncementModal(announcement, userId);
                break; // عرض إعلان واحد فقط لكل تحميل صفحة مبدئيًا
            }
        } else {
            console.log('No active app announcements found.');
        }
    } catch (err) {
        console.error('Unexpected error in fetchAndDisplayAppAnnouncements:', err);
    }
}

function createAnnouncementModal(announcement, userId) {
    const modalId = `app-announcement-modal-${announcement.id}`;
    if (document.getElementById(modalId)) return; // تجنب التكرار

    const modalOverlay = document.createElement('div');
    modalOverlay.id = modalId;
    // ... (انسخ بقية محتوى دالة createAnnouncementModal من النسخة الحالية - الأطول)
    // مرة أخرى، انتبه لمسارات أيقونة الإغلاق وأنماط CSS.
    // الأنماط الرئيسية:
    modalOverlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background-color: rgba(0, 0, 0, 0.75); display: flex;
        justify-content: center; align-items: center; z-index: 100006; /* أعلى من إشعار التحديث */
        padding: 15px; box-sizing: border-box;
    `;

    const card = document.createElement('div');
    card.className = 'announcement-card'; // ستحتاج لتعريف هذا الكلاس في CSS
    card.style.cssText = `
        background: #1e1e1e; color: #ffffff; padding: 20px; border-radius: 12px;
        border-image: linear-gradient(to right, #FFD700, #FFC300) 1; border-width: 2px; border-style: solid;
        box-shadow: 0 8px 25px rgba(0,0,0,0.6); width: 100%; max-width: 400px;
        display: flex; flex-direction: column; align-items: center; text-align: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* أو الخط الذي تستخدمه */
        position: relative; overflow: hidden;
        /* animation: fadeInScaleUp 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); */ /* تجاهل الرسوم المتحركة */
    `;

    const closeButton = document.createElement('button');
    const closeIconImg2 = document.createElement('img');
    closeIconImg2.src = 'image/close_icon.png'; // تأكد من صحة المسار
    closeIconImg2.alt = 'Close';
    closeIconImg2.style.width = '20px'; closeIconImg2.style.height = '20px';
    closeButton.appendChild(closeIconImg2);
    closeButton.setAttribute('aria-label', 'إغلاق الإعلان');
    closeButton.style.cssText = `
        position: absolute; top: 10px; right: 10px; background: transparent;
        border: none; cursor: pointer; line-height: 1; padding: 0 5px; z-index: 10;
    `;
    closeButton.onclick = (e) => {
        e.stopPropagation(); modalOverlay.remove();
        if (announcement.display_frequency === 'once') {
            localStorage.setItem(`announcement_shown_${userId}_${announcement.id}`, 'true');
        }
        document.body.style.overflow = '';
    };
    card.appendChild(closeButton);

    const isImageOnlyAnnouncement = announcement.image_url && (!announcement.description || announcement.description.trim() === '');

    if (isImageOnlyAnnouncement) {
        const img = document.createElement('img');
        img.src = announcement.image_url;
        img.alt = announcement.title || 'Announcement Image';
        img.style.cssText = `
            width: 100%; max-height: 70vh; object-fit: contain;
            border-radius: 8px; margin-bottom: 20px;
            border: 3px solid; border-image-slice: 1;
            border-image-source: linear-gradient(to right, #FFD700, #FFA500); display: block;
        `;
        card.appendChild(img);

        if (announcement.title && announcement.title.trim() !== '') {
            const titleElement = document.createElement('h3');
            titleElement.textContent = announcement.title;
            titleElement.style.cssText = `color: #ffcc00; font-size: 1.5em; margin-bottom: 20px; font-weight: 600;`;
            card.appendChild(titleElement);
        }

        const continueButton = document.createElement('button');
        continueButton.textContent = 'Continue';
        continueButton.style.cssText = `
            background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
            padding: 12px 25px; border-radius: 8px; cursor: pointer; font-size: 1.1em;
            font-weight: bold; text-transform: uppercase; width: auto; min-width: 150px;
            font-family: 'Courier New', Courier, monospace; margin-top: 10px;
            /* box-shadow: 0 2px 5px rgba(0,0,0,0.3); */ /* تجاهل الظل */
        `;
        continueButton.onclick = (e) => {
            e.stopPropagation(); modalOverlay.remove();
            if (announcement.display_frequency === 'once') {
                localStorage.setItem(`announcement_shown_${userId}_${announcement.id}`, 'true');
            }
            document.body.style.overflow = '';
        };
        card.appendChild(continueButton);

    } else { // إعلان عادي
        if (announcement.image_url) {
            const img = document.createElement('img');
            img.src = announcement.image_url;
            img.alt = announcement.title || 'Announcement Image';
            img.style.cssText = `
                width: 100%; max-height: 200px; object-fit: cover;
                border-radius: 8px; margin-bottom: 15px; border: 1px solid #444;
            `;
            card.appendChild(img);
        }
        if (announcement.title && announcement.title.trim() !== '') {
            const titleElement = document.createElement('h3');
            titleElement.textContent = announcement.title;
            titleElement.style.cssText = `color: #ffcc00; font-size: 1.5em; margin-bottom: 10px; font-weight: 600;`;
            card.appendChild(titleElement);
        }
        if (announcement.description && announcement.description.trim() !== '') {
            const descriptionElement = document.createElement('p');
            descriptionElement.textContent = announcement.description;
            descriptionElement.style.cssText = `
                font-size: 0.95em; line-height: 1.5; margin-bottom: 20px; color: #e0e0e0;
                max-height: 100px; overflow-y: auto;
            `;
            card.appendChild(descriptionElement);
        }
        if (announcement.button_text) {
            const actionButton = document.createElement('button');
            actionButton.textContent = announcement.button_text;
            actionButton.style.cssText = `
                background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
                padding: 12px 25px; border-radius: 8px; cursor: pointer; font-size: 1.1em;
                font-weight: bold; text-transform: uppercase; width: auto; min-width: 150px;
                font-family: 'Courier New', Courier, monospace;
                /* box-shadow: 0 2px 5px rgba(0,0,0,0.3); */ /* تجاهل الظل */
            `;
            actionButton.onclick = (e) => {
                e.stopPropagation(); modalOverlay.remove();
                if (announcement.display_frequency === 'once') {
                    localStorage.setItem(`announcement_shown_${userId}_${announcement.id}`, 'true');
                }
                document.body.style.overflow = '';
            };
            card.appendChild(actionButton);
        }
    }
    modalOverlay.appendChild(card);
    document.body.appendChild(modalOverlay);
    document.body.style.overflow = 'hidden';
}

async function fetchAndDisplayUpdateNotification() {
    console.log("Fetching active update notifications (new logic)...");
    try {
        // 1. Fetch all active notifications, ordered by priority (desc) then created_at (desc)
        const { data: notifications, error } = await supabaseClient
            .from(UPDATE_NOTIFICATIONS_TABLE)
            .select('*')
            .eq('is_active', true)
            .order('priority', { ascending: false }) // Higher priority first
            .order('created_at', { ascending: false }); // Then newest first for same priority

        if (error) {
            console.error('Error fetching update notifications:', error);
            return;
        }

        if (!notifications || notifications.length === 0) {
            console.log('[UpdateCheckV2] No active update notifications found.');
            return;
        }

        // 2. Get current app version
        let currentAppVersion = null;
        if (typeof AndroidInterface !== 'undefined' && typeof AndroidInterface.getAppVersionName === 'function') {
            try {
                currentAppVersion = AndroidInterface.getAppVersionName();
            } catch (e) {
                console.error("Error calling AndroidInterface.getAppVersionName():", e);
            }
        } else {
            console.warn('AndroidInterface.getAppVersionName() is not defined.');
        }

        if (!currentAppVersion || typeof currentAppVersion !== 'string' || currentAppVersion.trim() === "") {
            console.warn('[UpdateCheckV2] Current app version is invalid or not available. Cannot process version-specific notifications accurately.');
            // Fallback: Display the highest priority non-version-specific notification if any, or the first one.
            // For simplicity, we might just display the first one if app version is unknown.
            // Or, only display notifications with version_targeting_type === 'ALL_VERSIONS'.
            // Let's try to display the first applicable one, prioritizing ALL_VERSIONS.
            const allVersionsNotification = notifications.find(n => n.version_targeting_type === 'ALL_VERSIONS');
            if (allVersionsNotification) {
                 if (!sessionStorage.getItem(`notification_closed_${allVersionsNotification.id}`)) {
                    console.log(`[UpdateCheckV2] App version unknown, showing 'ALL_VERSIONS' notification ID ${allVersionsNotification.id}`);
                    createNotificationBanner(allVersionsNotification);
                 } else {
                    console.log(`[UpdateCheckV2] App version unknown, 'ALL_VERSIONS' notification ID ${allVersionsNotification.id} already closed.`);
                 }
            } else if (notifications.length > 0 && !sessionStorage.getItem(`notification_closed_${notifications[0].id}`)) {
                // Fallback to the very first notification if no ALL_VERSIONS and app version is unknown
                console.warn(`[UpdateCheckV2] App version unknown, no 'ALL_VERSIONS' notification. Showing highest priority notification ID ${notifications[0].id} as fallback.`);
                createNotificationBanner(notifications[0]);
            }
            return;
        }
        console.log(`[UpdateCheckV2] Current App Version: '${currentAppVersion}'`);

        // 3. Iterate through notifications by priority
        for (const notification of notifications) {
            if (sessionStorage.getItem(`notification_closed_${notification.id}`)) {
                console.log(`[UpdateCheckV2] Notification ${notification.id} was already closed in this session. Skipping.`);
                continue;
            }

            const targetVersion = notification.version_name; // e.g., "1.1.0"
            const targetingType = notification.version_targeting_type || 'OLDER_THAN_TARGET'; // Default if null
            const isExclusive = notification.is_exclusive || false;

            console.log(`[UpdateCheckV2] Processing Notif ID ${notification.id}: TargetVer='${targetVersion}', Type='${targetingType}', Exclusive=${isExclusive}, Priority=${notification.priority}`);

            let matches = false;

            switch (targetingType) {
                case 'OLDER_THAN_TARGET':
                    // Show if currentAppVersion < targetVersion
                    // Requires a valid targetVersion
                    if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) < 0;
                    } else {
                        console.warn(`[UpdateCheckV2] Notif ID ${notification.id}: OLDER_THAN_TARGET requires a valid version_name. Skipping.`);
                        matches = false; // Cannot apply if targetVersion is invalid
                    }
                    break;
                case 'EXACTLY_TARGET':
                    // Show if currentAppVersion == targetVersion
                    // Requires a valid targetVersion
                     if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) === 0;
                    } else {
                        console.warn(`[UpdateCheckV2] Notif ID ${notification.id}: EXACTLY_TARGET requires a valid version_name. Skipping.`);
                        matches = false;
                    }
                    break;
                case 'ALL_BUT_TARGET':
                    // Show if currentAppVersion != targetVersion
                    // Requires a valid targetVersion
                    if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) !== 0;
                    } else {
                        // If no target version, it means "all but nothing", so effectively "all versions"
                        console.warn(`[UpdateCheckV2] Notif ID ${notification.id}: ALL_BUT_TARGET with no valid version_name. Treating as ALL_VERSIONS.`);
                        matches = true;
                    }
                    break;
                case 'ALL_VERSIONS':
                    // Always show
                    matches = true;
                    break;
                default:
                    console.warn(`[UpdateCheckV2] Unknown version_targeting_type: '${targetingType}' for Notif ID ${notification.id}. Defaulting to not match.`);
                    matches = false;
                    break;
            }

            console.log(`[UpdateCheckV2] Notif ID ${notification.id}: Match result for type '${targetingType}' with current '${currentAppVersion}' and target '${targetVersion}': ${matches}`);

            if (matches) {
                console.log(`[UpdateCheckV2] SHOWING notification ID ${notification.id}.`);
                createNotificationBanner(notification);
                if (isExclusive) {
                    console.log(`[UpdateCheckV2] Notification ID ${notification.id} is exclusive. Stopping further processing.`);
                    return; // Stop processing if exclusive
                }
                // If not exclusive, we might still want to show only one banner at a time.
                // For now, the logic will display the first matching, and if it's exclusive, it stops.
                // If not exclusive, it would continue, but createNotificationBanner might replace previous.
                // To show only one banner (the highest priority matching one), we should return after the first createNotificationBanner.
                return; // Display only the highest-priority matching notification
            }
        }
        console.log('[UpdateCheckV2] No suitable update notification found after checking all active ones.');

    } catch (err) {
        console.error('[UpdateCheckV2] Unexpected error in fetchAndDisplayUpdateNotification:', err);
    }
}

// Helper function to compare semantic versions (e.g., "1.2.3", "1.10.0")
// Returns:
//   -1 if v1 < v2
//    0 if v1 == v2
//    1 if v1 > v2
function compareVersions(v1, v2) {
    // Ensure inputs are strings before splitting
    const sV1 = String(v1);
    const sV2 = String(v2);

    const parts1 = sV1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    const len = Math.max(parts1.length, parts2.length);

    for (let i = 0; i < len; i++) {
        const p1 = parts1[i] || 0;
        const p2 = parts2[i] || 0;
        if (p1 < p2) return -1;
        if (p1 > p2) return 1;
    }
    return 0;
}


function createNotificationBanner(notification) {
    const modalId = `update-notification-modal-${notification.id}`;
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.id = modalId;
    // ... (انسخ بقية محتوى دالة createNotificationBanner من النسخة الحالية - الأطول)
    // تأكد من تعديل مسارات الصور للأيقونات (مثل أيقونة الإغلاق) لتناسب النسخة السابقة
    // مثال لتعديل زر الإغلاق إذا كنت تستخدم صورة في النسخة السابقة:
    // const closeButton = document.createElement('button');
    // const closeIconImg = document.createElement('img');
    // closeIconImg.src = 'image/close_icon.png'; // أو المسار الصحيح في النسخة السابقة
    // closeIconImg.alt = 'Close';
    // closeIconImg.style.width = '20px'; closeIconImg.style.height = '20px';
    // closeButton.appendChild(closeIconImg);
    // ... (باقي خصائص closeButton)

    // انسخ الـ CSS اللازم من النسخة الحالية أو أنشئه لـ .notification-card وغيرها.
    // أهم الأنماط التي يجب نسخها:
    modalOverlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background-color: rgba(0, 0, 0, 0.6); display: flex;
        justify-content: center; align-items: center; z-index: 100005; /* تأكد أن z-index مناسب */
        padding: 20px; box-sizing: border-box;
    `;

    const card = document.createElement('div');
    card.className = 'notification-card'; // ستحتاج لتعريف هذا الكلاس في CSS
    card.style.cssText = `
        background: linear-gradient(145deg, #2c2c2c, #1a1a1a); color: #ffffff;
        padding: 25px; border-radius: 15px;
        border-image: linear-gradient(to right, #FFD700, #FFC300) 1; border-width: 3px; border-style: solid;
        box-shadow: 0 10px 30px rgba(0,0,0,0.5); width: 100%; max-width: 380px;
        min-height: 300px; display: flex; flex-direction: column;
        justify-content: space-between; text-align: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* أو الخط الذي تستخدمه */
        position: relative; overflow: hidden;
        /* animation: fadeInScaleUp 0.3s ease-out; */ /* تجاهل الرسوم المتحركة مبدئيًا */
    `;

    const closeButton = document.createElement('button');
    const closeIconImg1 = document.createElement('img');
    closeIconImg1.src = 'image/close_icon.png'; // تأكد من صحة المسار
    closeIconImg1.alt = 'Close';
    closeIconImg1.style.width = '20px'; closeIconImg1.style.height = '20px';
    closeButton.appendChild(closeIconImg1);
    closeButton.setAttribute('aria-label', 'إغلاق الإشعار');
    closeButton.style.cssText = `
        position: absolute; top: 10px; right: 10px; background: transparent;
        border: none; cursor: pointer; line-height: 1; padding: 0 5px;
    `;
    closeButton.onclick = (e) => {
        e.stopPropagation(); modalOverlay.remove();
        sessionStorage.setItem(`notification_closed_${notification.id}`, 'true');
        document.body.style.overflow = '';
    };

    let imageElementHtml = '';
    if (notification.image_url) {
        const img = document.createElement('img');
        img.src = notification.image_url;
        img.alt = notification.title || 'Notification Image';
        img.style.cssText = `
            width: 100%; max-height: 180px; object-fit: cover;
            border-radius: 10px; margin-bottom: 15px;
            border-image: linear-gradient(to right, orange, gold) 1; border-width: 2px; border-style: solid;
        `;
        card.appendChild(img);
    }

    const titleElement = document.createElement('h3');
    titleElement.id = `notification-title-${notification.id}`;
    titleElement.textContent = notification.title || 'تحديث جديد متوفر!';
    titleElement.style.cssText = `
        color: #ffcc00; font-size: 1.6em;
        margin-top: ${notification.image_url ? '0' : '10px'}; margin-bottom: 15px; font-weight: bold;
    `;

    const descriptionElement = document.createElement('p');
    descriptionElement.textContent = notification.description || 'يتوفر إصدار جديد من التطبيق. قم بالتحديث الآن!';
    descriptionElement.style.cssText = `font-size: 1em; line-height: 1.6; margin-bottom: 25px; color: #e0e0e0;`;

    const actionsContainer = document.createElement('div');
    actionsContainer.style.cssText = `display: flex; flex-direction: column; gap: 10px; width: 100%;`;

    if (notification.update_url) {
        const updateButton = document.createElement('button');
        updateButton.textContent = 'Update Now';
        // انسخ أنماط الزر من النسخة الحالية أو أنشئها. مثال بسيط:
        updateButton.style.cssText = `
            background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
            padding: 15px 20px; border-radius: 5px; cursor: pointer; font-size: 1.2em;
            font-weight: bold; text-transform: uppercase; width: 100%;
            font-family: 'Courier New', Courier, monospace; /* أو خط مناسب */
            /* box-shadow: 2px 2px 0px #a0522d; */ /* تجاهل الظل مبدئيًا */
        `;
        updateButton.onclick = (e) => {
            e.stopPropagation();
            if (typeof AndroidInterface !== 'undefined' && AndroidInterface.openExternalUrl) {
                AndroidInterface.openExternalUrl(notification.update_url);
            } else {
                window.open(notification.update_url, '_blank');
            }
        };
        actionsContainer.appendChild(updateButton);
    }

    if (notification.show_cancel_button) {
        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Skip';
        // انسخ أنماط الزر من النسخة الحالية أو أنشئها. مثال بسيط:
        cancelButton.style.cssText = `
            background: linear-gradient(to right, #FFD700, #FFC300); color: #FFFFFF; border: none;
            padding: 10px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;
            font-weight: bold; width: 100%;
            font-family: 'Courier New', Courier, monospace; /* أو خط مناسب */
        `;
        cancelButton.onclick = (e) => {
            e.stopPropagation(); modalOverlay.remove();
            sessionStorage.setItem(`notification_closed_${notification.id}`, 'true');
            document.body.style.overflow = '';
        };
        actionsContainer.appendChild(cancelButton);
    }

    card.appendChild(closeButton);
    card.appendChild(titleElement);
    card.appendChild(descriptionElement);
    card.appendChild(actionsContainer);
    modalOverlay.appendChild(card);
    document.body.appendChild(modalOverlay);
    document.body.style.overflow = 'hidden'; // لمنع التمرير في الخلفية
}

// --- Lazy Loading Initialization ---
let lazyLoadObserver;

function initializeLazyLoading(selector = '.lazy-load') {
    const imagesToLoad = document.querySelectorAll(selector + ':not(.loaded)'); // Select only images not yet loaded

    if (!lazyLoadObserver) {
        lazyLoadObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const src = img.getAttribute('data-src');
                    if (src) {
                        img.src = src;
                        img.classList.add('loaded'); // Mark as loaded
                        img.classList.remove('lazy-load'); // Optional: remove class if no longer needed
                        console.log(`Lazy loading image: ${src.substring(0, 50)}...`);
                    }
                    observer.unobserve(img); // Stop observing once loaded
                }
            });
        }, {
            rootMargin: '0px 0px 100px 0px', // Load images 100px before they enter viewport
            threshold: 0.01 // Trigger even if only 1% visible
        });
    }

    imagesToLoad.forEach(img => {
        lazyLoadObserver.observe(img);
    });
}


// --- New Installation Instructions Modal ---
function showNewInstallInstructions() {
    console.log(">>> showNewInstallInstructions called");

    // Check if modal already exists
    if (document.getElementById('install-instructions-modal')) {
        console.log("Install instructions modal already exists.");
        return;
    }

    // Create modal container
    const modal = document.createElement('div');
    modal.id = 'install-instructions-modal';
    // Add classes or styles via CSS later

    // Create header div
    const header = document.createElement('div');
    header.className = 'modal-install-header';

    // Create close button inside header
    const closeButton = document.createElement('button');
    closeButton.className = 'close-button'; // Use class for styling
    closeButton.innerHTML = '&times;'; // '×' symbol
    closeButton.title = 'Close';
    closeButton.onclick = closeNewInstallInstructions; // Assign close function
    header.appendChild(closeButton);

    // (Optional: Add title to header if needed)
    // const title = document.createElement('h2');
    // title.textContent = 'How to Install';
    // header.appendChild(title);

    // Create image container div
    const imageContainer = document.createElement('div');
    imageContainer.id = 'install-image-container';

    // Add touch listeners to disable/enable pull-to-refresh
    imageContainer.addEventListener('touchstart', () => {
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.disablePullToRefresh) {
            console.log(">>> Disabling pull-to-refresh for instruction scroll");
            AndroidInterface.disablePullToRefresh();
        }
    }, { passive: true }); // Use passive listener if just observing

    imageContainer.addEventListener('touchend', () => {
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.enablePullToRefresh) {
            console.log(">>> Re-enabling pull-to-refresh after instruction scroll");
            AndroidInterface.enablePullToRefresh();
        }
    });
     imageContainer.addEventListener('touchcancel', () => { // Also re-enable on cancel
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.enablePullToRefresh) {
            console.log(">>> Re-enabling pull-to-refresh after touch cancel");
            AndroidInterface.enablePullToRefresh();
        }
    });


    // Loop to create and append images 1 to 6
    for (let i = 1; i <= 6; i++) {
        const img = document.createElement('img');
        img.src = `image/${i}.jpg`; // Use relative path
        img.alt = `Installation Instructions Step ${i}`;
        // Add click listener for zoom
        img.addEventListener('click', () => showImageZoomModal(img.src));
        // Add styling via CSS
        imageContainer.appendChild(img);
    }

    // Append header and image container to modal
    modal.appendChild(header);
    modal.appendChild(imageContainer);

    // Append modal to body
    document.body.appendChild(modal);

    // Add class to body to prevent scrolling (optional, if needed)
    // document.body.classList.add('modal-open'); // Keep body scrollable
}

function closeNewInstallInstructions() {
    console.log(">>> closeNewInstallInstructions called");
    // Also close zoom modal if open when closing instructions
    closeImageZoomModal();
    const modal = document.getElementById('install-instructions-modal');
    if (modal) {
        modal.remove(); // Remove the install instructions modal from the DOM
    }
    // Ensure pull-to-refresh is re-enabled when closing the modal
    if (typeof AndroidInterface !== 'undefined' && AndroidInterface.enablePullToRefresh) {
        console.log(">>> Ensuring pull-to-refresh is enabled after closing instructions");
        AndroidInterface.enablePullToRefresh();
    }
}

// --- Image Zoom Modal Functions ---
function showImageZoomModal(src) {
    console.log(">>> showImageZoomModal called with src:", src);
    // Close any existing zoom modal first
    closeImageZoomModal();

    // Create modal container
    const zoomModal = document.createElement('div');
    zoomModal.id = 'image-zoom-modal';
    zoomModal.onclick = closeImageZoomModal; // Close when background is clicked

    // Create image element
    const zoomImage = document.createElement('img');
    zoomImage.src = src;
    zoomImage.alt = 'Zoomed Installation Instruction';
    zoomImage.onclick = (event) => {
        event.stopPropagation(); // Prevent closing modal when clicking the image itself
    };

    // Append image to modal, and modal to body
    zoomModal.appendChild(zoomImage);
    document.body.appendChild(zoomModal);
    // Optional: Prevent body scroll while zoom modal is open
    // document.body.classList.add('modal-open');
}

function closeImageZoomModal() {
    const zoomModal = document.getElementById('image-zoom-modal');
    if (zoomModal) {
        console.log(">>> Closing image zoom modal");
        zoomModal.remove();
        // Optional: Restore body scroll if it was prevented
        // document.body.classList.remove('modal-open');
    }
}
// --- End Image Zoom Modal Functions ---

// --- Update Notification Functions ---
function createNotificationBanner(notification) {
    const modalId = `update-notification-modal-${notification.id}`;
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.id = modalId;
    modalOverlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background-color: rgba(0, 0, 0, 0.7); display: flex;
        justify-content: center; align-items: center; z-index: 10000;
        padding: 20px; box-sizing: border-box;
    `;

    const card = document.createElement('div');
    card.style.cssText = `
        background-color: #333; color: white; padding: 20px; border-radius: 10px;
        width: 100%; max-width: 350px; position: relative; text-align: center;
        border: 2px solid #FFD700;
    `;

    const closeButton = document.createElement('button');
    const closeIconImg1 = document.createElement('img');
    closeIconImg1.src = 'image/close_icon.png';
    closeIconImg1.alt = 'Close';
    closeIconImg1.style.width = '20px';
    closeIconImg1.style.height = '20px';
    closeButton.appendChild(closeIconImg1);
    closeButton.setAttribute('aria-label', 'إغلاق الإشعار');
    closeButton.style.cssText = `
        position: absolute; top: 10px; right: 10px; background: transparent;
        border: none; cursor: pointer; line-height: 1; padding: 0 5px;
    `;
    closeButton.onclick = (e) => {
        e.stopPropagation(); modalOverlay.remove();
        sessionStorage.setItem(`notification_closed_${notification.id}`, 'true');
        document.body.style.overflow = '';
    };

    let imageElementHtml = '';
    if (notification.image_url) {
        const img = document.createElement('img');
        img.src = notification.image_url;
        img.alt = notification.title || 'Notification Image';
        img.style.cssText = `
            width: 100%; max-height: 180px; object-fit: cover;
            border-radius: 10px; margin-bottom: 15px;
            border-image: linear-gradient(to right, orange, gold) 1; border-width: 2px; border-style: solid;
        `;
        card.appendChild(img);
    }

    const titleElement = document.createElement('h3');
    titleElement.id = `notification-title-${notification.id}`;
    titleElement.textContent = notification.title || 'تحديث جديد متوفر!';
    titleElement.style.cssText = `
        color: #ffcc00; font-size: 1.6em;
        margin-top: ${notification.image_url ? '0' : '10px'}; margin-bottom: 15px; font-weight: bold;
    `;

    // Use the appropriate language message based on device language
    const descriptionElement = document.createElement('p');
    const isArabic = document.documentElement.lang === 'ar';
    descriptionElement.textContent = isArabic ?
        (notification.message_ar || 'يتوفر إصدار جديد من التطبيق. قم بالتحديث الآن!') :
        (notification.message_en || 'A new version of the app is available. Update now!');
    descriptionElement.style.cssText = `font-size: 1em; line-height: 1.6; margin-bottom: 25px; color: #e0e0e0;`;

    const actionsContainer = document.createElement('div');
    actionsContainer.style.cssText = `display: flex; flex-direction: column; gap: 10px; width: 100%;`;

    if (notification.download_url) {
        const updateButton = document.createElement('button');
        updateButton.textContent = isArabic ? 'تحديث الآن' : 'Update Now';
        updateButton.style.cssText = `
            background: linear-gradient(to right, #FFD700, #FFC300); color: #333; border: none;
            padding: 15px 20px; border-radius: 5px; cursor: pointer; font-size: 1.2em;
            font-weight: bold; text-transform: uppercase; width: 100%;
        `;
        updateButton.onclick = (e) => {
            e.stopPropagation();
            if (typeof AndroidInterface !== 'undefined' && AndroidInterface.openExternalUrl) {
                AndroidInterface.openExternalUrl(notification.download_url);
            } else {
                window.open(notification.download_url, '_blank');
            }
        };
        actionsContainer.appendChild(updateButton);
    }

    const cancelButton = document.createElement('button');
    cancelButton.textContent = isArabic ? 'تخطي' : 'Skip';
    cancelButton.style.cssText = `
        background: transparent; color: #ccc; border: 1px solid #ccc;
        padding: 10px 15px; border-radius: 5px; cursor: pointer; font-size: 0.9em;
        font-weight: bold; width: 100%; margin-top: 5px;
    `;
    cancelButton.onclick = (e) => {
        e.stopPropagation(); modalOverlay.remove();
        sessionStorage.setItem(`notification_closed_${notification.id}`, 'true');
        document.body.style.overflow = '';
    };
    actionsContainer.appendChild(cancelButton);

    card.appendChild(closeButton);
    card.appendChild(titleElement);
    card.appendChild(descriptionElement);
    card.appendChild(actionsContainer);
    modalOverlay.appendChild(card);
    document.body.appendChild(modalOverlay);
    document.body.style.overflow = 'hidden'; // Prevent scrolling in the background
}

async function fetchAndDisplayUpdateNotification() {
    console.log("Fetching active update notifications...");
    try {
        // 1. Fetch all active notifications, ordered by priority (desc) then created_at (desc)
        const { data: notifications, error } = await supabaseClient
            .from(UPDATE_NOTIFICATIONS_TABLE)
            .select('*')
            .eq('is_active', true)
            .order('priority', { ascending: false }) // Higher priority first
            .order('created_at', { ascending: false }); // Then newest first for same priority

        if (error) {
            console.error('Error fetching update notifications:', error);
            return;
        }

        if (!notifications || notifications.length === 0) {
            console.log('[UpdateCheck] No active update notifications found.');
            return;
        }

        // 2. Get current app version
        let currentAppVersion = null;
        if (typeof AndroidInterface !== 'undefined' && typeof AndroidInterface.getAppVersionName === 'function') {
            try {
                currentAppVersion = AndroidInterface.getAppVersionName();
            } catch (e) {
                console.error("Error calling AndroidInterface.getAppVersionName():", e);
            }
        } else {
            console.warn('AndroidInterface.getAppVersionName() is not defined.');
        }

        if (!currentAppVersion || typeof currentAppVersion !== 'string' || currentAppVersion.trim() === "") {
            console.warn('[UpdateCheck] Current app version is invalid or not available. Cannot process version-specific notifications accurately.');
            // Fallback: Display the highest priority non-version-specific notification if any, or the first one.
            const allVersionsNotification = notifications.find(n => n.version_targeting_type === 'ALL_VERSIONS');
            if (allVersionsNotification) {
                 if (!sessionStorage.getItem(`notification_closed_${allVersionsNotification.id}`)) {
                    console.log(`[UpdateCheck] App version unknown, showing 'ALL_VERSIONS' notification ID ${allVersionsNotification.id}`);
                    createNotificationBanner(allVersionsNotification);
                 } else {
                    console.log(`[UpdateCheck] App version unknown, 'ALL_VERSIONS' notification ID ${allVersionsNotification.id} already closed.`);
                 }
            } else if (notifications.length > 0 && !sessionStorage.getItem(`notification_closed_${notifications[0].id}`)) {
                // Fallback to the very first notification if no ALL_VERSIONS and app version is unknown
                console.warn(`[UpdateCheck] App version unknown, no 'ALL_VERSIONS' notification. Showing highest priority notification ID ${notifications[0].id} as fallback.`);
                createNotificationBanner(notifications[0]);
            }
            return;
        }
        console.log(`[UpdateCheck] Current App Version: '${currentAppVersion}'`);

        // 3. Iterate through notifications by priority
        for (const notification of notifications) {
            if (sessionStorage.getItem(`notification_closed_${notification.id}`)) {
                console.log(`[UpdateCheck] Notification ${notification.id} was already closed in this session. Skipping.`);
                continue;
            }

            const targetVersion = notification.version_name; // e.g., "1.1.0"
            const targetingType = notification.version_targeting_type || 'OLDER_THAN_TARGET'; // Default if null
            const isExclusive = notification.is_exclusive || false;

            console.log(`[UpdateCheck] Processing Notif ID ${notification.id}: TargetVer='${targetVersion}', Type='${targetingType}', Exclusive=${isExclusive}, Priority=${notification.priority}`);

            let matches = false;

            switch (targetingType) {
                case 'OLDER_THAN_TARGET':
                    // Show if currentAppVersion < targetVersion
                    // Requires a valid targetVersion
                    if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) < 0;
                    } else {
                        console.warn(`[UpdateCheck] Notif ID ${notification.id}: OLDER_THAN_TARGET requires a valid version_name. Skipping.`);
                        matches = false; // Cannot apply if targetVersion is invalid
                    }
                    break;
                case 'EXACTLY_TARGET':
                    // Show if currentAppVersion == targetVersion
                    // Requires a valid targetVersion
                     if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) === 0;
                    } else {
                        console.warn(`[UpdateCheck] Notif ID ${notification.id}: EXACTLY_TARGET requires a valid version_name. Skipping.`);
                        matches = false;
                    }
                    break;
                case 'ALL_BUT_TARGET':
                    // Show if currentAppVersion != targetVersion
                    // Requires a valid targetVersion
                    if (targetVersion && typeof targetVersion === 'string' && targetVersion.trim() !== "") {
                        matches = compareVersions(currentAppVersion, targetVersion) !== 0;
                    } else {
                        // If no target version, it means "all but nothing", so effectively "all versions"
                        console.warn(`[UpdateCheck] Notif ID ${notification.id}: ALL_BUT_TARGET with no valid version_name. Treating as ALL_VERSIONS.`);
                        matches = true;
                    }
                    break;
                case 'ALL_VERSIONS':
                    // Always show
                    matches = true;
                    break;
                default:
                    console.warn(`[UpdateCheck] Unknown version_targeting_type: '${targetingType}' for Notif ID ${notification.id}. Defaulting to not match.`);
                    matches = false;
                    break;
            }

            console.log(`[UpdateCheck] Notif ID ${notification.id}: Match result for type '${targetingType}' with current '${currentAppVersion}' and target '${targetVersion}': ${matches}`);

            if (matches) {
                console.log(`[UpdateCheck] SHOWING notification ID ${notification.id}.`);
                createNotificationBanner(notification);
                if (isExclusive) {
                    console.log(`[UpdateCheck] Notification ID ${notification.id} is exclusive. Stopping further processing.`);
                    return; // Stop processing if exclusive
                }
                // If not exclusive, we might still want to show only one banner at a time.
                return; // Display only the highest-priority matching notification
            }
        }

    } catch (error) {
        console.error('Unexpected error in fetchAndDisplayUpdateNotification:', error);
    }
}

// --- Utility: Apply Fade-in Animation ---
function applyFadeInAnimation(selector) {
     const elements = document.querySelectorAll(selector);
     if (elements.length > 0) {
         setTimeout(() => {
            elements.forEach((el, index) => {
                 el.style.animationDelay = `${index * 0.05}s`;
                 el.classList.add('fade-in');
            });
        }, 50);
      }
 }

// --- Version Comparison Utility ---
// Returns: -1 if v1 < v2, 0 if v1 == v2, 1 if v1 > v2
function compareVersions(v1, v2) {
    if (!v1 || !v2) return 0; // Handle null/undefined

    // Split version strings by dots and convert to numbers
    const v1Parts = v1.split('.').map(part => parseInt(part, 10) || 0);
    const v2Parts = v2.split('.').map(part => parseInt(part, 10) || 0);

    // Ensure both arrays have the same length by padding with zeros
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    while (v1Parts.length < maxLength) v1Parts.push(0);
    while (v2Parts.length < maxLength) v2Parts.push(0);

    // Compare each part
    for (let i = 0; i < maxLength; i++) {
        if (v1Parts[i] > v2Parts[i]) return 1;
        if (v1Parts[i] < v2Parts[i]) return -1;
    }

    return 0; // Versions are equal
}

// --- Pull-to-Refresh Handler ---
async function handlePullToRefresh() {
    console.log(">>> handlePullToRefresh called from native.");

    // 1. Clear relevant cache
    // We need to clear the cache for the *current* view (All or specific category/sort)
    // For simplicity, let's clear all 'mods_' cache entries. A more targeted approach
    // could be implemented if performance becomes an issue.
    try {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('mods_')) {
                localStorage.removeItem(key);
                console.log(`Pull-to-refresh: Removed cache key: ${key}`);
            }
        }
    } catch (e) {
        console.error("Error clearing cache during pull-to-refresh:", e);
    }

    // 2. Re-fetch and re-display data based on the current view
    try {
        if (currentCategory === 'All') {
            // Re-display the "All" view (sections)
            await displayModsBySection();
        } else {
            // Re-display the current single category view with current sort order
            await displaySingleCategory(currentCategory, currentSortBy, currentSortAscending);
        }
    } catch (error) {
        console.error("Error re-fetching data during pull-to-refresh:", error);
        // Optionally display an error message in the UI
    } finally {
        // 3. Hide the native refresh indicator via the Android Interface
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.hideRefreshIndicator) {
            AndroidInterface.hideRefreshIndicator();
            console.log("Pull-to-refresh: Called AndroidInterface.hideRefreshIndicator()");
        } else {
            console.warn("Pull-to-refresh: AndroidInterface.hideRefreshIndicator not available.");
            // Fallback for testing in browser: hide a potential web-based indicator if you add one
        }
    }
}
// --- End Pull-to-Refresh Handler ---

// --- REMOVED: Simulate initial click ---
// function simulateInitialClick() { ... }
// --- END REMOVED ---

console.log("Mod Etaris script loaded and Supabase initialized.");

// --- Function definition moved higher up ---

// --- Pull-to-Refresh Handler ---
async function handlePullToRefresh() {
    console.log(">>> handlePullToRefresh called from native.");

    // 1. مسح الكاش المتعلق بالمودات
    try {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('mods_')) { // امسح جميع مفاتيح الكاش التي تبدأ بـ 'mods_'
                localStorage.removeItem(key);
                console.log(`Pull-to-refresh: Removed cache key: ${key}`);
            }
        }
        // يمكنك أيضًا مسح كاشات أخرى إذا لزم الأمر
        // localStorage.removeItem('app_announcements_cache'); // مثال
    } catch (e) {
        console.error("Error clearing cache during pull-to-refresh:", e);
    }

    // 2. إعادة جلب وعرض البيانات بناءً على العرض الحالي
    try {
        if (currentCategory === 'All') {
            // إذا كنت تعرض الصفحة الرئيسية المقسمة (مع الأخبار والإضافات وغيرها)
            await displayModsBySection();
        } else {
            // إذا كنت تعرض فئة واحدة
            await displaySingleCategory(currentCategory, currentSortBy, currentSortAscending);
        }

        // يمكنك إعادة جلب بيانات أخرى مثل إعلانات التطبيق إذا أردت
        // await fetchAndDisplayAppAnnouncements();
    } catch (error) {
        console.error("Error re-fetching data during pull-to-refresh:", error);
    } finally {
        // 3. إخفاء مؤشر التحديث الأصلي عبر واجهة Android
        if (typeof AndroidInterface !== 'undefined' && AndroidInterface.hideRefreshIndicator) {
            AndroidInterface.hideRefreshIndicator();
            console.log("Pull-to-refresh: Called AndroidInterface.hideRefreshIndicator()");
        } else {
            console.warn("Pull-to-refresh: AndroidInterface.hideRefreshIndicator not available.");
        }
    }
}
