#!/usr/bin/env python3
"""
خادم محلي بسيط لتشغيل لوحة الإدارة
يحل مشكلة CORS وتحميل الملفات المحلية
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from urllib.parse import urlparse

class AdminHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """معالج طلبات HTTP مخصص للوحة الإدارة"""
    
    def end_headers(self):
        # إضافة headers لحل مشكلة CORS
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_OPTIONS(self):
        # معالجة طلبات OPTIONS للـ CORS
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # تخصيص رسائل السجل
        print(f"[{self.address_string()}] {format % args}")

def find_free_port(start_port=8000, max_attempts=100):
    """البحث عن منفذ متاح"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"لم يتم العثور على منفذ متاح في النطاق {start_port}-{start_port + max_attempts}")

def main():
    """تشغيل الخادم المحلي"""
    
    # التأكد من أننا في المجلد الصحيح
    admin_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(admin_dir)
    
    print("🚀 بدء تشغيل خادم لوحة الإدارة...")
    print(f"📁 مجلد العمل: {admin_dir}")
    
    # البحث عن منفذ متاح
    try:
        port = find_free_port()
    except RuntimeError as e:
        print(f"❌ خطأ: {e}")
        sys.exit(1)
    
    # إعداد الخادم
    handler = AdminHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("localhost", port), handler) as httpd:
            server_url = f"http://localhost:{port}"
            
            print(f"✅ تم تشغيل الخادم بنجاح!")
            print(f"🌐 رابط لوحة الإدارة: {server_url}")
            print(f"🔗 رابط الاشتراك المجاني: {server_url}/subscription_admin.html")
            print(f"🔗 رابط إدارة البانر: {server_url}/banner_admin.html")
            print("📝 اضغط Ctrl+C لإيقاف الخادم")
            print("-" * 60)
            
            # فتح المتصفح تلقائياً
            try:
                webbrowser.open(server_url)
                print("🌍 تم فتح المتصفح تلقائياً")
            except Exception as e:
                print(f"⚠️  لم يتم فتح المتصفح تلقائياً: {e}")
                print(f"يرجى فتح الرابط يدوياً: {server_url}")
            
            print("-" * 60)
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
