body {
    font-family: sans-serif;
    margin: 0;
    background-color: #f4f4f4;
    color: #333;
    line-height: 1.6;
}

header {
    background-color: #333;
    color: #fff;
    padding: 1rem 0;
    text-align: center;
    position: relative; /* Added for positioning */
    top: -5px; /* Move header up slightly */
}

header h1 {
    margin: 0;
}

main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 1rem;
    position: relative; /* Added for positioning */
    top: 10px; /* Move main content down slightly */
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

section {
    margin-bottom: 2rem;
}

h2 {
    color: #333;
    border-bottom: 2px solid #eee;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

h3 {
    color: #555;
    margin-bottom: 0.8rem;
}

hr {
    border: 0;
    height: 1px;
    background-color: #ddd;
    margin: 2rem 0;
}

/* Statistics Section */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.stat-card {
    background-color: #f9f9f9;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 5px;
}

.stat-card ol {
    padding-left: 20px;
    margin: 0;
}

.stat-card li {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Manage Mods Section */
.action-button,
.cancel-button,
.delete-button,
.edit-button {
    background-color: #007bff;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-right: 10px;
    transition: background-color 0.3s ease;
}

.action-button:hover {
    background-color: #0056b3;
}

.cancel-button {
    background-color: #6c757d;
}
.cancel-button:hover {
    background-color: #5a6268;
}

.delete-button {
    background-color: #dc3545;
}
.delete-button:hover {
    background-color: #c82333;
}

/* Style for the "Delete All" button specifically */
.danger-button {
    background-color: #a01c2a; /* Darker red for extra warning */
    border: 1px solid #7f1721;
}
.danger-button:hover {
    background-color: #7f1721; /* Even darker on hover */
}


.edit-button {
    background-color: #ffc107;
    color: #333;
}
.edit-button:hover {
    background-color: #e0a800;
}

/* زر مطلوب */
.toggle-required-button {
    background-color: #6c757d;
    color: white;
    padding: 6px 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.toggle-required-button:hover {
    background-color: #5a6268;
}

.toggle-required-button.required-active {
    background-color: #28a745;
}

.toggle-required-button.required-active:hover {
    background-color: #218838;
}


/* Forms */
.form-container {
    background-color: #f9f9f9;
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-top: 1rem;
    margin-bottom: 2rem;
}

.form-container label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-container input[type="text"],
.form-container input[type="url"],
.form-container input[type="file"],
.form-container textarea,
.form-container select {
    width: 100%;
    padding: 10px;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.form-container textarea {
    min-height: 100px;
    resize: vertical;
}

.form-checkbox-group {
    margin-bottom: 1.5rem;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #e9ecef;
}

.form-checkbox-group input[type="checkbox"] {
    margin-right: 8px;
    vertical-align: middle;
}

.form-checkbox-group label {
    display: inline-block;
    font-weight: bold;
    margin-right: 10px;
    margin-bottom: 0;
}

.form-checkbox-group small {
    display: block;
    margin-top: 5px;
    color: #6c757d;
}

.image-previews {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.image-previews img {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.image-previews .img-container {
    position: relative;
}

.image-previews .remove-img-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    cursor: pointer;
    font-weight: bold;
}

.image-previews .img-container {
    position: relative; /* Needed for absolute positioning of the button */
    display: inline-block; /* Keep images side-by-side */
}

.image-previews .remove-img-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: rgba(220, 53, 69, 0.8); /* Semi-transparent red */
    color: white;
    border: 1px solid rgba(178, 34, 34, 0.9); /* Darker red border */
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 14px;
    line-height: 18px; /* Adjust for vertical centering */
    text-align: center;
    cursor: pointer;
    font-weight: bold;
    padding: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: background-color 0.2s ease;
}

.image-previews .remove-img-btn:hover {
    background-color: rgba(178, 34, 34, 1); /* Solid darker red on hover */
}


/* Mod List */
.mod-list-controls {
    margin-bottom: 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;
}

.mod-list-controls input[type="text"],
.mod-list-controls select {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

#mods-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

#mods-table th,
#mods-table td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: left;
}

#mods-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

#mods-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

#mods-table tbody tr:hover {
    background-color: #f1f1f1;
}

#mods-table .action-cell button {
    margin-right: 5px;
    padding: 5px 8px;
    font-size: 0.9rem;
}


/* Usage Limits Section */
.usage-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Adjust minmax for better fit */
    gap: 1.5rem; /* Increased gap */
    margin-bottom: 1rem;
}

.usage-item {
    background-color: #f9f9f9;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 5px;
}

.usage-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #555;
}

.usage-bar-container {
    width: 100%;
    background-color: #e9ecef;
    border-radius: 4px;
    height: 15px; /* Slightly thicker bar */
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.usage-bar {
    height: 100%;
    background-color: #007bff; /* Default blue */
    width: 0%; /* Initial width */
    border-radius: 4px 0 0 4px; /* Rounded left edge */
    transition: width 0.5s ease-in-out;
    text-align: center; /* Center text if needed inside bar */
    color: white;
    font-size: 0.7rem;
    line-height: 15px;
}

/* Optional: Different colors based on usage percentage */
.usage-bar.high-usage {
    background-color: #ffc107; /* Yellow for high usage */
}
.usage-bar.critical-usage {
    background-color: #dc3545; /* Red for critical usage */
}


.usage-item span {
    font-size: 0.9rem;
    color: #333;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 2rem;
    padding: 1rem 0;
    background-color: #333;
    color: #fff;
}

footer p {
    margin: 0;
}

/* Modal for Category-Mods Management */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-modal-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.close-modal-button:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 70px); /* Subtract header height */
}

.search-container {
    margin-bottom: 20px;
}

.search-container input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.mods-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.available-mods-section,
.category-mods-section {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 15px;
}

.mods-list {
    max-height: 400px;
    overflow-y: auto;
}

.mod-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.mod-item:last-child {
    border-bottom: none;
}

.add-to-category-button,
.remove-from-category-button {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.add-to-category-button {
    background-color: #28a745;
    color: white;
}

.add-to-category-button:hover {
    background-color: #218838;
}

.remove-from-category-button {
    background-color: #dc3545;
    color: white;
}

.remove-from-category-button:hover {
    background-color: #c82333;
}

.error-message {
    color: #dc3545;
    font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }

    .mod-list-controls {
        flex-direction: column;
        align-items: stretch;
    }

    #mods-table th,
    #mods-table td {
        font-size: 0.9rem;
        padding: 8px;
    }

    #mods-table .action-cell button {
        padding: 4px 6px;
        font-size: 0.8rem;
    }

    .mods-container {
        grid-template-columns: 1fr; /* Stack on mobile */
    }

    .modal-content {
        width: 95%;
        max-height: 95vh;
    }
}


/* Recent Activity Section */
.activity-container {
    display: grid;
    /* Adjust grid columns as needed if more activity cards are added */
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.activity-card {
    background-color: #f9f9f9;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 5px;
}

#recent-likes-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.5rem; /* Reduced margin */
}

#recent-likes-table th,
#recent-likes-table td {
    border: 1px solid #ddd;
    padding: 8px; /* Slightly smaller padding */
    text-align: right; /* Align text to right for Arabic */
    font-size: 0.9rem; /* Slightly smaller font */
}

#recent-likes-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

#recent-likes-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

#recent-likes-table tbody tr:hover {
    background-color: #f1f1f1;
}

/* Adjust alignment for specific columns if needed */
#recent-likes-table td:nth-child(1), /* User ID */
#recent-likes-table td:nth-child(3) { /* Timestamp */
    /* Example: Keep left alignment if preferred for IDs/timestamps */
    /* text-align: left; */
    direction: ltr; /* Ensure LTR direction for IDs/timestamps */
    unicode-bidi: embed; /* Helps with mixed directionality */
}

/* Delete Files Section */
#delete-files-section {
    margin-top: 2rem;
}

.files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); /* Responsive grid */
    gap: 1rem;
    margin-top: 1rem;
}

.file-item {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Push button to bottom */
    word-wrap: break-word; /* Wrap long file names */
}

.file-item img {
    max-width: 100%;
    height: 100px; /* Fixed height for consistency */
    object-fit: cover; /* Scale image nicely */
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.file-item span {
    display: block;
    font-size: 0.85rem;
    margin-bottom: 10px;
    flex-grow: 1; /* Allow span to take available space */
}

.file-item button {
    background-color: #dc3545; /* Red delete button */
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
    width: 100%; /* Make button full width */
    margin-top: auto; /* Push to bottom */
}

.file-item button:hover {
    background-color: #c82333;
}

/* Ad Management Section */
.ads-settings-container {
    display: flex;
    flex-direction: column;
    gap: 2rem; /* Space between different ad type settings */
}

.ad-type-settings {
    background-color: #f9f9f9;
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: 5px;
}

.ad-type-settings h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.ad-type-settings form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.ad-type-settings form input[type="text"],
.ad-type-settings form input[type="checkbox"],
.ad-type-settings form select {
    width: 100%;
    padding: 10px;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.ad-type-settings form input[type="checkbox"] {
    width: auto; /* Checkboxes shouldn't be full width */
    margin-right: 5px;
    vertical-align: middle;
}

.ad-type-settings form button {
    margin-top: 0.5rem; /* Add some space above the button */
}

.ad-type-separator {
    border: 0;
    height: 1px;
    background-color: #e0e0e0; /* Lighter separator for within the ad section */
    margin: 1rem 0; /* Less margin than main section hr */
}
