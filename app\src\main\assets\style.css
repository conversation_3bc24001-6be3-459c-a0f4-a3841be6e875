/* إضافة خط البيكسل مع fallback */
@import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');
/* إضافة خط ماين كرافت للأقسام */
@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

/* Fallback font face for offline mode */
@font-face {
    font-family: 'VT323-Fallback';
    src: local('Courier New'), local('monospace');
    font-display: swap;
}

/* Fallback font face for Minecraft style font */
@font-face {
    font-family: 'Minecraft-Fallback';
    src: local('Courier New'), local('monospace');
    font-display: swap;
    font-weight: bold;
}

/* Language Selection Styles */
.language-selection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100010;
    animation: fadeIn 0.5s ease;
}

.language-selection-container {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    border: 3px solid #ffcc00;
    box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
    max-width: 400px;
    width: 90%;
    animation: slideInUp 0.6s ease;
}

.language-selection-title {
    font-size: 2rem;
    color: #ffcc00;
    margin-bottom: 10px;
    font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.language-selection-subtitle {
    font-size: 1.2rem;
    color: #ffffff;
    margin-bottom: 30px;
    opacity: 0.9;
}

.language-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
}

.language-option {
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    color: #000;
    border: none;
    padding: 15px 30px;
    border-radius: 15px;
    font-size: 1.3rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
    font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace;
}

.language-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 204, 0, 0.5);
    background: linear-gradient(45deg, #ffd700, #ffb300);
}

.language-option:active {
    transform: translateY(-1px);
}

.language-flag {
    font-size: 2rem;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additional animations for language modal */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInScaleUp {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --secondary-color: #ffffff;
    --background-color: #21221f;
    --card-background: #000000;
    --text-color: #ffffff;
    --border-radius: 12px;
    --transition: all 0.3s ease;
    --primary-color: transparent;
    --accent-color: #ffcc00;
}

html {
    background-color: #21221f; /* Match body background */
    width: 100%; /* Ensure full width */
    overflow-x: hidden; /* Also prevent overflow here */
    border: none; /* Explicitly remove border */
    box-shadow: none; /* Explicitly remove shadow */
    outline: none; /* Explicitly remove outline */
}

/* --- New Top Fixed Bar --- */
.top-fixed-bar {
    position: fixed;
    top: 0; /* Reverted to original position */
    left: 0; /* Adjust to right: 0; if needed for RTL, but left: 0 usually works */
    width: 100%;
    height: 45px; /* Increased height */
    background: linear-gradient(to right, #ffcc00, #ff9800); /* Match header gradient */
    color: white; /* Change text color to white for better contrast */
    display: flex;
    align-items: center;
    justify-content: space-between; /* Space out items: button, span, button */
    padding: 0 10px; /* Adjust padding slightly */
    z-index: 100004; /* Increased z-index to be above install modal (100003) */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* .top-fixed-bar span { */ /* Removed the span style as the span element is removed */
    /* font-size: 0.9rem; */
    /* font-weight: bold; */
/* } */
/* --- End New Top Fixed Bar --- */

body {
    padding-top: 55px; /* Adjusted padding for 45px fixed bar */
    background-color: #21221f !important; /* Explicitly set dark background color and force override */
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden; /* Keep this */
    width: 100%; /* Ensure body takes full width of html */
    border: none; /* Explicitly remove border */
    box-shadow: none; /* Explicitly remove shadow */
    outline: none; /* Explicitly remove outline */
    /* منع تحديد النص */
    user-select: none; /* Standard syntax */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    padding-bottom: 60px; /* Reduced bottom padding to avoid excessive space */
}

/* Hide scrollbar for Chrome, Safari and Opera (including WebView) */
body::-webkit-scrollbar {
    display: none;
}

/* Prevent body and html scrolling when modal is open */
html.modal-open {
    overflow: hidden !important;
}
.modal-open {
    overflow: hidden !important; /* Use !important to ensure override */
}

/* --- Original Header Styles (Restored for index.html) --- */
.header {
    position: relative; /* Set to relative for absolute positioning of children */
    width: 100%;
    height: 100px; /* Further increased header height again */
    display: flex;
    flex-direction: column; /* Original direction */
    align-items: center;
    justify-content: center;
    background: linear-gradient(to right, #ffcc00, #ff9800); /* Restore original gradient */
    padding: 10px 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* ظل خفيف */
    margin-bottom: 30px; /* يمكنك تغيير الرقم حسب المسافة التي تريدها */
    margin-top: -10px; /* Moved header down again */
}

.header-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1; /* الصورة في الخلفية */
}

/* Removed .back-btn rule - will be added specifically for search page */

.header h1 {
    font-size: 2rem; /* Original size */
    font-weight: bold;
    color: white;
    margin-bottom: 10px; /* Original margin */
    /* Removed flex properties */
}

.app-logo-header { /* Added this rule back just in case, although it might be overridden */
    height: 60px; /* Increased height */
    margin-bottom: 10px; /* Same margin as the H1 had */
    object-fit: contain;
    /* Apply the fadeInUp animation */
    /* Animation properties moved to .fade-in class */
}

/* Removed fadeInLogo keyframes as we are using fadeInUp now */
/* @keyframes fadeInLogo {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
} */


#categories {
    margin-top: -10px;
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
    border-radius: 5px; /* زوايا دائرية للشريط */

}

.category-btn {
    padding: 1px 13px;
    background-color: transparent; /* خلفية شفافة */
    color: white; /* لون النص أبيض */
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* خط طبيعي بدون بيكسل */
    font-size: 1rem; /* زيادة حجم النص قليلاً */
    font-weight: bold; /* جعل النص عريضًا */
    text-transform: capitalize; /* جعل أول حرف كبيرًا */
    letter-spacing: 0.5px; /* تقليل تباعد الأحرف */
    border: none; /* إزالة الحدود */
    border-radius: 20px; /* زوايا دائرية */
    cursor: pointer;
    transition: all 0.3s ease; /* حركة سلسة عند التفاعل */
    background-color: transparent !important; /* إزالة الخلفية */
    transition: transform 0.2s ease;
}

.category-btn:hover {
    color: #ffcc00; /* لون النص عند التمرير (أصفر) */
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3); /* إضافة ظل خفيف للنص */
}

/* Style for the active category button */
.category-btn.active-category {
    background-color: #ffcc00 !important; /* Orange background */
    color: black !important; /* Black text */
}

/* Ensure the active category button retains its style even when pressed/focused */
.category-btn.active-category:active,
.category-btn.active-category:focus,
.category-btn.active-category:focus-visible {
    background-color: #ffcc00 !important; /* Keep orange background */
    color: black !important; /* Keep black text */
    outline: none !important; /* Remove any focus outline */
    box-shadow: none !important; /* Remove any focus shadow */
}

/* --- Original Drawer Button Styles (Restored for index.html) --- */
.drawer-btn {
    width: 50px; /* Restored width */
    height: 40px;
    font-size: 1.2rem;
    position: absolute; /* Changed to absolute */
    left: 15px; /* Position from left edge of header */
    top: 35%; /* Increased from 11% to move down */
    transform: translateY(-50%); /* Adjust vertical centering */
    background-color: var(--primary-color);
    color: white;
    padding: 5px; /* Adjusted padding */
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    z-index: 10; /* Lower z-index */
    background-color: transparent !important; /* إزالة الخلفية */
    transition: transform 0.2s ease;
}

.drawer {
    position: fixed;
    top: 45px; /* Adjusted top for 45px fixed bar */
    left: -250px; /* البداية خارج الشاشة */
    width: 250px;
    height: calc(100% - 45px); /* Adjust height for 45px fixed bar */
    background: #000000; /* تغيير الخلفية إلى الأسود */
    color: white;
    display: flex; /* Reverted to flex */
    flex-direction: column; /* Reverted to flex */
    padding: 20px; /* Restored original padding */
    z-index: 10001; /* Increased z-index to be above buttons and top bar */
    justify-content: flex-start; /* Added back for flex */
    align-items: stretch; /* Let items stretch, alignment handled by margin */
    transition: left 0.3s ease, background 0.5s ease; /* إضافة انتقال للخلفية */
    box-shadow: 5px 0 15px rgba(0, 0, 0, 0.2); /* إضافة ظل */
}

.drawer a {
    display: flex; /* Keep flex for icon/text alignment within link */
    align-items: center; /* Keep flex for icon/text alignment within link */
    gap: 10px; /* Space between icon and text */
    color: #ffffff; /* تغيير لون النص الافتراضي إلى الأبيض */
    text-decoration: none;
    padding: 15px 10px; /* Restored original padding */
    font-size: 1.1rem; /* Slightly smaller font size */
    margin-left: 0 !important; /* Force to left edge */
    margin-right: auto !important; /* Push from right */
    border-radius: 5px; /* Adjust border radius */
    transition: color 0.3s ease, background-color 0.3s ease, transform 0.2s ease;
}

.drawer a i { /* Style for the icons */
    width: 20px; /* Fixed width for icons */
    text-align: center;
    color: #ffcc00; /* Accent color for icons */
}

.drawer a:hover {
    color: #000000; /* Text color on hover */
    background-color: #ffcc00; /* Background color on hover */
    transform: translateX(5px); /* Slight move effect */
}

.drawer a:hover i {
    color: #000000; /* Icon color on hover */
}

.drawer.active {
    left: 0; /* تظهر القائمة */
}

/* --- Banner Ad Styles --- */
.banner-ad-container {
    width: 90%; /* Reduced width to 90% */
    margin: 20px auto; /* Added margin top/bottom and centered horizontally */
    position: relative;
    overflow: hidden;
    border-radius: 12px; /* Slightly increased border radius */
    height: 130px; /* Reduced height */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Added subtle shadow */
}

.banner-ad-slide {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    justify-content: center;
    align-items: center;
}

.banner-ad-slide.active {
    opacity: 1;
}

.banner-ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px; /* Match container border radius */
    cursor: pointer;
}

/* Gradient overlay at the bottom of the banner */
.banner-ad-slide::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px; /* Height of the gradient */
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    pointer-events: none; /* Make sure it doesn't interfere with clicks */
}

/* --- Banner Ad Modal Styles --- */
.banner-ad-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100005;
    animation: fadeIn 0.3s ease;
}

.banner-ad-modal-content {
    background-color: var(--card-background);
    width: 90%;
    max-width: 500px;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    position: relative;
}

.banner-ad-modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.banner-ad-modal-image {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    margin-bottom: 20px;
    border-radius: 10px;
}

.banner-ad-modal-title {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 10px;
}

.banner-ad-modal-description {
    color: #ccc;
    margin-bottom: 20px;
}

.banner-ad-modal-button {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: black;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.banner-ad-modal-button:hover {
    transform: scale(1.05);
}

/* --- New Category Section Styles --- */
.category-section {
    margin-bottom: 10px; /* Space between sections - Reduced from 25px */
    /* padding: 0 10px; */ /* Remove horizontal padding again */
    direction: ltr; /* Ensure section itself is LTR */
    text-align: left; /* Align text within section to left */
    /* background-color: var(--card-background); */ /* Reverted background color change */
}

/* ترتيب الأقسام بالترتيب الجديد المطلوب */
#news-section {
    order: 1; /* News - أول قسم */
}

#addons-section {
    order: 2; /* Addons - القسم الثاني (يتضمن Free Addons) */
}

#suggested-mods-section {
    order: 3; /* Suggested - القسم الثالث */
}

#shaders-section {
    order: 4; /* Shaders - القسم الرابع */
}

#texture-pack-section {
    order: 5; /* Texture Pack - القسم الخامس */
}

#seeds-section {
    order: 6; /* Seeds - القسم السادس */
}

#maps-section {
    order: 7; /* Maps - القسم السابع */
}

/* إضافة flex للحاوي الرئيسي لكي تعمل خاصية order */
#main-content-wrapper {
    display: flex;
    flex-direction: column;
}

/* أيقونة Free Addon */
.free-addon-icon {
    position: absolute;
    bottom: 5px;
    left: 5px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    padding: 2px 6px; /* Increased padding */
    border-radius: 3px;
    font-size: 0.7rem; /* Increased font size */
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.1px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    z-index: 10;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
    /* animation: growShrink 1.5s infinite alternate; */ /* Removed animation */
}

/* Free Addon icon glow animation removed for consistency with NEW badge */

/* أيقونة Popular للمودات الشعبية */
.popular-icon {
    position: absolute;
    bottom: 6px;
    right: 6px;
    background: linear-gradient(45deg, #FFD700, #FFA500); /* Golden yellow and orange */
    color: #FFF;
    padding: 3px 6px;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 4px 10px rgba(255, 215, 0, 0.8); /* Increased shadow */
    border: 1px solid #FFD700; /* Keep golden yellow border */
    z-index: 10;
    animation: popularGlow 2s ease-in-out infinite alternate;
}

@keyframes popularGlow {
    0% {
        box-shadow: 0 2px 6px rgba(255, 107, 53, 0.4);
        transform: scale(1);
    }
    100% {
        box-shadow: 0 4px 10px rgba(255, 107, 53, 0.6); /* Increased shadow slightly */
        transform: scale(1.03); /* Increased scale slightly */
    }
}

@keyframes freeAddonGlow {
    0% {
        box-shadow: 0 2px 6px rgba(255, 165, 0, 0.4);
        transform: scale(1);
    }
    100% {
        box-shadow: 0 3px 10px rgba(255, 215, 0, 0.5); /* Increased shadow slightly */
        transform: scale(1.02); /* Increased scale slightly */
    }
}

/* تأثيرات خاصة بمودات Free Addons */
.free-addon-mod {
    position: relative;
    border: 2px solid transparent;
    background: var(--card-background);
    overflow: hidden;
    border-radius: var(--border-radius);
    box-shadow: 0 0 25px rgba(255, 165, 0, 0.5), 0 0 50px rgba(255, 215, 0, 0.4); /* Increased shadow */
    animation: freeAddonCardGlow 2s ease-in-out infinite alternate;
}

/* تأثير لمعان أبيض لمودات Free Addons - مطابق للمودات الشعبية */
.free-addon-mod::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.35),
        rgba(255, 255, 255, 0.2),
        transparent);
    animation: shimmerLeftToRight 2.5s ease-in-out 2s infinite; /* Duration 2.5s, delay 2s, infinite */
    pointer-events: none;
    z-index: 10;
}

@keyframes freeAddonCardGlow {
    0% {
        box-shadow: 0 0 15px rgba(255, 165, 0, 0.3), 0 0 30px rgba(255, 215, 0, 0.2); /* Increased shadow slightly */
        border-color: rgba(255, 165, 0, 0.4); /* Increased border glow slightly */
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 165, 0, 0.5), 0 0 40px rgba(255, 215, 0, 0.35); /* Increased shadow slightly */
        border-color: rgba(255, 215, 0, 0.6); /* Increased border glow slightly */
    }
}

/* تأثير اللمعان الأبيض لمودات Free Addons */
@keyframes freeAddonShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* تأثير توهج عام لجميع الكاردات في تصنيف All */
.all-category-glow {
    position: relative;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.4), 0 0 40px rgba(255, 165, 0, 0.3); /* Increased shadow */
    animation: allCategoryGlow 3s ease-in-out infinite alternate;
}

@keyframes allCategoryGlow {
    0% {
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.2), 0 0 20px rgba(255, 165, 0, 0.15); /* Increased shadow slightly */
    }
    100% {
        box-shadow: 0 0 18px rgba(255, 215, 0, 0.35), 0 0 35px rgba(255, 165, 0, 0.25); /* Increased shadow slightly */
    }
}

/* مربعات البيكسل المتوهجة لـ Free Addons */
.free-addon-pixel-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 1px;
    pointer-events: none;
    z-index: 5;
    animation: freeAddonPixelFloat 3s ease-out forwards;
    box-shadow: 0 0 5px rgba(255, 165, 0, 0.6); /* Increased shadow slightly */
}

@keyframes freeAddonPixelFloat {
    0% {
        opacity: 1; /* Reverted to original opacity */
        transform: translateY(0) scale(1); /* Reverted to original scale */
    }
    50% {
        opacity: 0.8; /* Reverted to original opacity */
        transform: translateY(-30px) scale(1.2); /* Reverted to original scale */
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.5); /* Reverted to original scale */
    }
}




/* مربعات البيكسل المتوهجة لـ Free Addons */
.free-addon-pixel-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 1px;
    pointer-events: none;
    z-index: 5;
    animation: freeAddonPixelFloat 3s ease-out forwards;
    box-shadow: 0 0 5px rgba(255, 165, 0, 0.6); /* Increased shadow slightly */
}

@keyframes freeAddonPixelFloat {
    0% {
        opacity: 1; /* Reverted to original opacity */
        transform: translateY(0) scale(1); /* Reverted to original scale */
    }
    50% {
        opacity: 0.8; /* Reverted to original opacity */
        transform: translateY(-30px) scale(1.2); /* Reverted to original scale */
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.5); /* Reverted to original scale */
    }
}

/* تأثيرات المودات الأكثر إعجاباً (أكثر من 1000 تحميل) */
.popular-mod {
    position: relative;
    border: 2px solid transparent; /* Keep border for structure, but it will be transparent */
    background: var(--card-background); /* Remove the border-box gradient */
    animation: none; /* Remove popularModGlow animation */
    overflow: hidden;
}

/* إزالة تأثير التحديد السيء */
.popular-mod:focus,
.popular-mod:active,
.popular-mod:hover {
    outline: none;
    transform: none;
    box-shadow: inherit; /* منع تغيير الظل عند التحديد */
}

/* إزالة تأثيرات التحديد من جميع كاردات المودات */
.mod-card:hover,
.item:hover,
.mod-card:focus,
.item:focus,
.mod-card:active,
.item:active {
    transform: none;
    box-shadow: inherit;
    outline: none;
}

.popular-mod::before {
    display: none; /* Remove the rotating yellow border effect */
}

/* Removed popularModGlow and borderRotate keyframes as they are no longer used */

/* مربعات البيكسل المتوهجة */
.pixel-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #FFD700, #FF8C00);
    border-radius: 1px;
    pointer-events: none;
    z-index: 5;
    animation: pixelFloat 3s ease-out forwards;
    box-shadow: 0 0 10px rgba(255, 215, 0, 1); /* Increased shadow */
}

@keyframes pixelFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-30px) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.5);
    }
}

/* تأثير لمعان إضافي للمودات الشعبية - محدث */
/* تأثير لمعان إضافي للمودات الشعبية - محدث */
.popular-mod::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.35),
        rgba(255, 255, 255, 0.2),
        transparent);
    animation: shimmerLeftToRight 1.5s ease-in-out 2s infinite; /* Duration 1.5s, delay 2s, infinite */
    pointer-events: none;
    z-index: 10;
}

@keyframes shimmerLeftToRight {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Align category headers */
.category-header {
    font-size: 1.3rem; /* تقليل حجم الخط قليلاً لخط ماين كرافت */
    font-weight: normal; /* خط ماين كرافت لا يحتاج bold */
    font-family: 'Press Start 2P', 'Minecraft-Fallback', 'Courier New', monospace; /* خط ماين كرافت مع fallback */
    color: var(--text-color);
    margin-bottom: 20px; /* Increased space below header */
    padding-left: 5px; /* Indent text slightly */
    padding-bottom: 5px; /* Space between text and underline */
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align content to the start (left) */
    width: 100%; /* Ensure it takes full width */
    gap: 8px; /* زيادة المسافة بين النص والأيقونة لخط ماين كرافت */
    direction: ltr; /* Explicitly set direction */
    text-align: left; /* Explicitly set text-align */
    letter-spacing: 1px; /* تباعد الأحرف للوضوح */
    text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8); /* إضافة ظل لتأثير ماين كرافت */
}

/* Reduce gap specifically for News header */
#news-section .category-header {
    gap: 5px; /* تقليل المسافة لقسم الأخبار */
}

.category-icon { /* Added this rule back */
    width: 45px; /* Reverted to original size */
    height: 45px; /* Reverted to original size */
    vertical-align: middle; /* Align icon with text */
    /* margin-left: -2px; Removed negative margin */
    position: relative; /* Added for precise positioning if needed */
    top: 5px; /* Adjust vertical alignment if needed */
}

/* Specific rule for News icon */
#news-section .category-header .category-icon {
    width: 40px !important; /* Increased size, override inline style */
    height: 40px !important; /* Increased size, override inline style */
    top: 1px !important; /* Moved further up, override inline style */
}

.category-header i { /* Style the dropdown icon */
    font-size: 1rem; /* Adjust icon size */
    color: var(--accent-color); /* Match accent color */
}

/* Style for the "see all" button */
.see-all-btn {
    margin-left: auto; /* Push the button to the right */
    font-size: 0.9rem; /* تقليل حجم الخط لخط ماين كرافت */
    font-family: 'Press Start 2P', 'Minecraft-Fallback', 'Courier New', monospace; /* خط ماين كرافت مع fallback */
    color: var(--text-color); /* Changed to white */
    cursor: pointer;
    font-weight: normal; /* Make it less prominent than the header */
    padding: 3px 6px; /* Further reduced padding */
    border-radius: 5px; /* Optional: slightly rounded corners */
    transition: color 0.2s ease, background-color 0.2s ease;
    margin-top: 5px; /* Added to move button down slightly */
    letter-spacing: 0.5px; /* تباعد الأحرف للوضوح */
    text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.8); /* إضافة ظل لتأثير ماين كرافت */
}

.see-all-btn img { /* Added this rule back */
    width: 16px; /* Adjust size */
    height: 16px; /* Adjust size */
    margin-left: 4px; /* Space between text and icon */
    vertical-align: middle; /* Align with text */
}


.see-all-btn:hover {
    color: var(--accent-color); /* Change hover color to yellow */
    background-color: rgba(255, 255, 255, 0.1); /* Subtle background on hover */
}

/* --- REMOVED: .mods-grid rule --- */
/* .mods-grid { ... } */

/* --- Re-added Horizontal Scroll Styles for "All" View --- */
.items-container {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    /* gap: 1.5rem; */ /* Removed gap */
    padding: 1rem 10px 1rem 0; /* Reduced vertical padding, Added right padding */
    max-width: 100%;
    margin: 0 auto;
    /* Hide scrollbar visually */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    direction: ltr; /* Force LTR for horizontal scroll */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.items-container::-webkit-scrollbar {
    display: none;
}

.item { /* Style for horizontal items */
    flex: 0 0 280px; /* Fixed width for horizontal items */
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    border: 2px solid var(--accent-color); /* Add yellow border */
    padding: 1rem;
    /* cursor: pointer; */ /* Removed to potentially improve scroll behavior on touch */
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    direction: ltr; /* Ensure content inside is LTR */
    touch-action: pan-x pan-y; /* Allow horizontal and vertical panning */
    margin-right: 5px; /* Further reduced right margin */
    margin-left: 10px; /* Add left margin */
    position: relative; /* Added for shimmer effect */
    overflow: hidden; /* Added for shimmer effect */
}

/* تأثير لمعان أبيض للعناصر الأفقية Free Addons */
.item.free-addon-mod::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.35),
        rgba(255, 255, 255, 0.2),
        transparent);
    animation: shimmerLeftToRight 1.5s ease-in-out 2s infinite; /* Duration 1.5s, delay 2s, infinite */
    pointer-events: none;
    z-index: 10;
}

/* Remove margin from the last item in the container */
.items-container .item:last-child {
    margin-right: 0;
}

.item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.item .mod-image { /* Style image within horizontal item */
    width: 100%;
    height: 150px; /* Adjust height for horizontal items */
    object-fit: cover; /* Changed from center */
    border-radius: calc(var(--border-radius) - 4px);
    margin-bottom: 1rem;
}

.item .mod-name { /* Style name within horizontal item */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: block;
    text-align: left;
    min-width: 0;
    font-size: 1.1rem; /* Slightly smaller font */
}

.item .mod-actions { /* Adjust actions for horizontal item */
    margin-top: auto; /* Push actions to bottom */
    padding-top: 0.5rem;
    border-top: none;
    justify-content: space-evenly;
}

/* Increased specificity to ensure these icons are 20px */
.item .action-item .action-icon {
    width: 20px;
    height: 20px;
}

.item .action-count {
    font-size: 0.9rem;
}
/* --- End Horizontal Scroll Styles --- */

.drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 9998;
}

.drawer-overlay.active {
    visibility: visible;
    opacity: 1;
}

.drawer-btn img {
    width: 33px;
    height: 33px;
    object-fit: contain;
}

/* النافذة تغطي الشاشة بالكامل (مع الأخذ في الاعتبار الشريط العلوي الثابت) */
.modal {
    display: none;
    position: fixed;
    top: 45px; /* Adjusted top for 45px fixed bar */
    left: 0;
    width: 100%;
    height: calc(100% - 45px); /* Adjust modal height for 45px fixed bar */
    background-color: rgba(0, 0, 0, 0.8); /* خلفية داكنة لتمييز النافذة */
    z-index: 99999; /* Keep high z-index, but positioning handles overlap */
    animation: fadeIn 0.3s ease;
    overflow-y: auto; /* تمكين التمرير عند الحاجة */
}

/* محتوى النافذة */
.modal-content {
    background-color: var(--card-background);
    width: 100%;
    max-width: 800px; /* حجم عرض مناسب للشاشة */
    margin: 0 auto;
    height: auto;
    min-height: 100%; /* تغطية جزء كبير من الشاشة */
    padding: 0.5rem;
    border-radius: 10px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    animation: slideIn 0.3s ease; /* تأثير جميل عند الظهور */
    padding-bottom: 150px; /* Increased bottom padding for creator info section */
    overflow-y: auto; /* Ensure content can scroll */
}

/* الصورة الرئيسية */
.main-image {
    width: 100%;
    max-height: 300px;
    object-fit: cover;
    border-radius: 10px;
    transition: transform 0.3s ease;
    margin-bottom: 1rem;
}

/* الصور المصغرة أسفل الصورة الرئيسية */
.thumbnail-container {
    display: flex;
    flex-wrap: nowrap; /* Ensure images stay in a single row */
    justify-content: center;
    gap: 15px;
    margin-bottom: 1.5rem;
    overflow-x: auto; /* تمرير أفقي عند وجود الكثير من الصور */
    padding: 10px 0;
    /* Hide scrollbar visually */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
}
/* Hide scrollbar for Chrome, Safari and Opera */
.thumbnail-container::-webkit-scrollbar {
    display: none;
}


.thumbnail-container img {
    flex-shrink: 0; /* Prevent images from shrinking */
    width: 70px;
    height: 70px;
    object-fit: cover;
    border: 2px solid transparent; /* إطار شفاف بدلاً من الأصفر */
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s ease, border-color 0.3s ease, padding 0.3s ease; /* Add padding to transition */
}

.thumbnail-container img.selected {
    border-color: orange; /* Change border to orange when selected */
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3); /* ظل برتقالي عند التحديد */
}

/* عدد التحميلات والإعجابات */
.stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    color: #4caf50;
}

/* الوصف */
.description-container {
    /* text-align: center; */ /* Changed to left */
    text-align: left;
    margin-bottom: 1.5rem;
}

.description {
    /* max-height: 60px; */ /* Removed height limit */
    /* overflow: hidden; */ /* Removed overflow hidden */
    /* text-overflow: ellipsis; */ /* Removed ellipsis */
    white-space: normal; /* Allow text wrapping */
    color: var(--text-color); /* Use theme text color */
    line-height: 1.5;
    text-align: left; /* Align text to the left for readability */
    padding: 0 1rem; /* Add some horizontal padding */
}

/* .read-more-btn { ... } */ /* Removed - button likely not needed anymore */

/* الحجم والإصدار */
.details {
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    font-weight: bold;
    color: #555;
}

/* زر التحميل كنافذة عائمة */
.download-bar {
    position: fixed;
    bottom: 0; /* Default position */
    left: 0;
    width: 100%;
    background-color: #4caf50; /* Corrected color value */
    padding: 10px 15px; /* Reverted padding */
    padding-bottom: calc(10px + env(safe-area-inset-bottom, 0px)); /* Adjusted bottom padding + safe area */
    min-height: 100px; /* Add minimum height */
    display: flex; /* Use flexbox for layout */
    justify-content: center; /* Center download button by default */
    align-items: center; /* Center items vertically */
    gap: 10px; /* Add gap between potential buttons */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    z-index: 100001; /* Ensure it's above modal content */
}

.download-btn {
    background: linear-gradient(45deg, #ffd700, #ffcc00); /* خلفية ذهبية */
    color: white; /* لون النص */
    font-size: 1.2rem; /* حجم النص */
    font-weight: bold; /* خط عريض */
    border: none; /* إزالة الحدود */
    border-radius: 10px; /* زوايا مدورة */
    padding: 12px 25px; /* حجم الزر */
    cursor: pointer; /* مؤشر اليد */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); /* ظل خفيف */
    transition: transform 0.15s ease-out, box-shadow 0.15s ease-out;
    text-align: center;
}

/* تأثير الضغط */
.download-btn:active {
    transform: scale(0.95); /* تصغير الزر عند الضغط */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* تقليل الظل لجعل التأثير طبيعيًا */
}

/* Ensure download button can contain the absolute positioned progress */
.download-btn {
    position: relative; /* Needed for absolute positioning of children */
    overflow: hidden;   /* Hide parts of the progress bar that might overflow */
}

/* Style for the button when the mod is downloaded */
.download-btn.downloaded {
    background-color: #5cb85c; /* Green background for downloaded state */
    /* Optional: Add other styles like border */
}
.download-btn.downloaded:hover {
    background-color: #4cae4c; /* Darker green on hover */
}

/* Progress Indicator Styles */
.download-progress-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* background-color: rgba(0, 0, 0, 0.6); /* Dark overlay */
    background-color: rgba(255, 215, 0, 0.9); /* Yellow background */
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); /* Subtle inner shadow */
    display: flex; /* Use flex to center text */
    align-items: center;
    justify-content: center;
    border-radius: 10px; /* Match button's border-radius */
    z-index: 2; /* Ensure it's above the button's text/icon */
    /* display: none; /* Initially hidden - handled by JS */
}

.download-progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%; /* Initial width */
    /* background-color: #4CAF50; /* Green progress bar - Removed */
    background: linear-gradient(to right, #FFA500, #FFD700); /* Orange to Golden Yellow gradient */
    border-radius: 10px; /* Match button's border-radius */
    transition: width 0.1s linear; /* Smooth width transition */
    z-index: 1; /* Below the text */
}

.download-progress-text {
    position: relative; /* Ensure text is above the progress bar */
    z-index: 2;
    color: white;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* Add shadow for readability */
}
/* End Progress Indicator Styles */


button:hover {
    background-color: #f5f5f5;
}

#details-modal {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} /* End of @keyframes fadeIn */

/* زر الإغلاق (Keep existing styles, ensure it doesn't conflict) */
.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
    font-size: 1.5rem;
    color: #64748b;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%; /* الزر يبقى دائريًا */
    background-color: transparent; /* إزالة الخلفية البيضاء */
    box-shadow: none; /* إزالة الظل إذا كان يسبب المشكلة */
}

.close-btn:hover {
    background-color: #e2e8f0;
    color: #333;
}

/* New Close Button on Modal Image */
.modal-image-close-btn {
    position: absolute;
    top: 10px; /* Adjust as needed */
    right: 10px; /* Adjust as needed */
    background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent background */
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;  /* Increased size */
    height: 35px; /* Increased size */
    font-size: 1.8rem; /* Increased font size */
    line-height: 1; /* Center the '×' */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10; /* Ensure it's above the image */
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.modal-image-close-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.modal-image-close-btn:active {
    transform: scale(0.95);
}

.modal-image-close-btn img {
    width: 36px; /* Increased size */
    height: 36px; /* Increased size */
    display: block; /* Ensure proper rendering */
}

/* زر الإعجاب */
.like-button {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: none;
}

.like-button .heart-icon {
    width: 24px;  /* Changed from 30px */
    height: 24px; /* Changed from 30px */
    transition: color 0.3s ease;
}

.thumbnail-container img:active {
    transform: scale(1.2);
}

/* تنسيق عام للأزرار */
#button-1 {
    padding: 10px 20px;
    border: none;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.1s ease-in-out;
}

        @keyframes fadeIn {
            from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* --- Animation for page load (Fade-in from Bottom) --- */
.initial-hidden-animate {
    opacity: 0;
    transform: translateY(20px); /* Start slightly lower */
    /* filter: blur(5px); */ /* Removed blur */
    transition: opacity 0.4s ease-out, transform 0.4s ease-out; /* Removed filter transition */
}

.initial-hidden-animate.animate-visible {
    opacity: 1;
    transform: translateY(0);
    /* filter: blur(0); */ /* Removed blur */
}
/* --- End Animation for page load --- */

    #modalContent p {
        overflow-wrap: break-word; /* كسر الكلمات الطويلة */
        word-wrap: break-word; /* دعم المتصفحات القديمة */
        word-break: break-word; /* كسر النص عند الحدود */
        white-space: normal; /* السماح بتعدد الأسطر */
    }

/* --- Original Search Button Styles (Restored for index.html) --- */
.search-btn {
    position: absolute; /* Changed to absolute */
    right: 15px; /* Position from right edge of header */
    top: 12px; /* Use fixed pixel value for top */
    /* transform: translateY(-50%); */ /* Removed transform for simplicity */
    background-color: var(--primary-color);
    padding: 5px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    z-index: 100; /* Increased z-index */
    transition: background-color 0.3s ease;
    background-color: transparent !important; /* إزالة الخلفية */
    transition: transform 0.2s ease;
    /* Removed margin-top */
    /* display: flex; Removed */
}

.search-btn:hover {
    background-color: var(--secondary-color); /* Restored */
}

.search-btn img { /* Restored */
    width: 34px; /* Restored */
    height: 34px; /* Restored */
    object-fit: contain; /* Restored */
}


@keyframes likeBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.4); }
    100% { transform: scale(1); }
}

/* تأثير عند الضغط على زر الإعجاب */
button.like-button:active .heart-icon {
    animation: likeBounce 0.3s ease-out;
}

/* عند الإعجاب، يصبح القلب أحمر */
button.like-button.liked .heart-icon {
    color: red;
    transition: color 0.3s ease;
}

.downloads-icon {
    color: gold;
}

.mod-card { /* Styles for vertical list items AND grid items */
    color: white;
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    border: 2px solid var(--accent-color); /* Add yellow border */
    padding: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    touch-action: pan-y; /* Explicitly allow vertical panning */
    /* margin: 10px; */ /* Use gap from container instead */
    margin-left: 10px; /* Add left margin */
    margin-bottom: 10px; /* Add bottom margin for spacing in vertical lists */
    position: relative; /* Added for positioning the new badge */
}

/* تأثير لمعان أبيض للكروت العمودية Free Addons */
.mod-card.free-addon-mod::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.35),
        rgba(255, 255, 255, 0.2),
        transparent);
    animation: shimmerLeftToRight 1.5s ease-in-out 2s infinite; /* Duration 1.5s, delay 2s, infinite */
    pointer-events: none;
    z-index: 10;
}

/* New badge for recent mods */
.mod-image-container {
    position: relative;
    width: 100%;
    height: 150px; /* Or whatever height your images are */
    overflow: hidden; /* Ensure badge doesn't overflow */
    border-radius: calc(var(--border-radius) - 4px); /* Match image border-radius */
    margin-bottom: 1rem; /* Match original image margin */
}

.mod-image-container .mod-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: calc(var(--border-radius) - 4px);
}

.new-badge {
    position: absolute;
    bottom: 5px; /* Position from bottom */
    right: 5px; /* Position from right */
    background: linear-gradient(135deg, #FFD700, #FFA500); /* Same gradient as FREE ADDON */
    color: white;
    padding: 2px 5px; /* Increased padding */
    border-radius: 3px;
    font-size: 0.65rem; /* Increased font size */
    font-weight: bold;
    z-index: 10;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.1px;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
    /* animation: growShrink 1.5s infinite alternate; */ /* Removed animation */
}

/* New specific rule for centering cards in single category view */
#singleCategoryContainer .mod-card {
    margin-left: auto;   /* Override general margin-left from .mod-card */
    margin-right: auto;  /* Add margin-right to auto for centering */
    width: 98%;          /* Make cards 98% of container width, allowing auto margins to center them. */
                         /* The general .mod-card rule provides margin-bottom: 10px for vertical spacing. */
}

/* Remove bottom margin for the last card in a single category view to avoid extra space at the end */
#singleCategoryContainer .mod-card:last-child {
    margin-bottom: 0;
}

.mod-name { /* General styles */
    font-size: 1.25rem;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 10px;
    text-align: left; /* Keep left alignment for vertical cards */
    width: 100%;
    min-width: 0;
}

.mod-info { /* Added style for the info container */
    width: 100%; /* Ensure it takes full width */
    display: flex; /* Use flex for its children */
    flex-direction: column; /* Stack name and actions vertically */
    align-items: stretch;
    margin-top: 10px; /* Add some space below the image */
}

.mod-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
}

/* صورة المود */
.mod-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: calc(var(--border-radius) - 4px);
}

/* قسم الإعجابات والتحميلات */
.mod-actions {
    display: flex;
    justify-content: space-evenly; /* Changed to space-evenly for equal spacing */
    align-items: center;
    width: 100%;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.action-icon {
    width: 24px;
    height: 24px;
}

.action-count {
    font-size: 1rem;
    color: var(--text-color);
}

/* أيقونة منصة ماين كرافت */
.minecraft-icon {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ستايل مؤشر جاري التحميل */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #ffcc00;
    font-weight: bold;
    padding: 15px;
    opacity: 0.9;
    animation: fadeBlink 1s infinite alternate;
}

/* دائرة التحميل المتحركة */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 204, 0, 0.3);
    border-top-color: #ffcc00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

/* تأثير الوميض */
@keyframes fadeBlink {
    from {
        opacity: 0.5;
    }
    to {
        opacity: 1;
    }
}

/* دوران دائرة التحميل */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeBlink {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* الرسوم المتحركة لتعبئة القلب */
@keyframes fillHeart {
    0% {
        background-color: transparent;
        color: black;
        transform: scale(1);
    }
    50% {
        background-color: rgba(255, 0, 0, 0.5);
        transform: scale(1.2);
    }
    100% {
        background-color: red;
        color: white;
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Initial state REMOVED */
/* .header .drawer-btn, ... */
/* { ... } */

/* .fade-in class REMOVED */
/* .fade-in { ... } */


/* Animation properties for mod cards and items removed for performance */

/* --- Adjusted Animation Delay Selectors --- */
/* تأخير ظهور كل عنصر ليحدث التأثير بالتدريج */
/* nth-child delays removed, JS will handle dynamic delays */
/* --- End Adjusted Animation Delay Selectors --- */

/* أنيميشن للأزرار */
/* General button animation properties moved to .fade-in class for specific buttons */
/* button { */
    /* opacity: 0; */
    /* transform: scale(0.8); */
    /* animation: fadeInUp 0.4s ease-in-out forwards; */
    /* animation-delay: 0.5s; */
/* } */ /* Commented out to avoid conflict with new load animation */

/* تطبيق الرسوم المتحركة */
button.like-button.animate-heart {
    position: relative;
    overflow: hidden;
    border-radius: 50%; /* لتجنب overflow */
    animation: fillHeart 0.6s ease forwards;
}

/* التأثير على أيقونة القلب */
button.like-button.animate-heart .heart-icon {
    color: white;
    transition: color 0.6s ease;
}


/* تحسين تمرير الصور */
div[style*="overflow-x: auto"] {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    /* Hide scrollbar visually */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
}
/* Hide scrollbar for Chrome, Safari and Opera */
div[style*="overflow-x: auto"]::-webkit-scrollbar {
    display: none;
}


div[style*="overflow-x: auto"] img {
    scroll-snap-align: start;
    border-radius: 10px;
}


        .more-text { display: none; }
        .toggle-btn {
            color: yellow;
            cursor: pointer;
            text-decoration: underline;
        }

        .close-btn {
    position: absolute;
    top: 10px; /* رفع الزر للأعلى */
    right: 15px; /* تعديل المسافة عن اليمين */
    width: 48px; /* زيادة عرض الزر قليلا */
    height: 48px; /* زيادة ارتفاع الزر قليلا */
    background-image: url('image/close2.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain; /* Or adjust to a percentage like 80% if needed */
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: transform 0.2s ease, opacity 0.2s ease;
    /* Hide text if any is present in the HTML element */
    font-size: 0;
    text-indent: -9999px;
    overflow: hidden;
}

.close-btn:hover {
    transform: scale(1.2); /* تكبير عند التحويم */
    opacity: 0.7;
}

.help-icon {
    width: 35px; /* تكبير الأيقونة */
    height: 35px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.help-icon:hover {
    transform: scale(1.2); /* تكبير عند التحويم */
}

/* --- Sorting Buttons Styles (Re-added/Ensured) --- */
.sort-buttons {
    display: none; /* Hide by default */
    justify-content: center; /* Center buttons horizontally */
    gap: 10px; /* Add space between buttons */
    /* flex-wrap: wrap; */ /* Removed to keep buttons on one line */
    margin: 15px 0; /* Add margin above and below */
    padding: 0 10px; /* Add horizontal padding */
}

.sort-btn {
    background-color: transparent;
    color: #ff9800;
    border: 2px solid #ff9800;
    padding: 6px 11px;
    border-radius: 20px;
    font-size: 1rem; /* زيادة حجم الخط قليلاً */
    font-family: 'VT323', 'VT323-Fallback', 'Courier New', monospace; /* خط البيكسل مع fallback */
    cursor: pointer;
    font-weight: bold;
    white-space: nowrap; /* منع النص من الانتقال إلى سطر جديد */
    transition: background 0.3s ease, color 0.3s ease, transform 0.2s ease;
    text-align: center; /* Center the text inside the button */
    letter-spacing: 0.5px; /* تباعد الأحرف للوضوح */
}

.sort-btn:hover {
    background-color: #ff9800;
    color: white;
    transform: scale(1.05);
}

.sort-btn.active-sort { /* Style for the active sort button */
    background-color: #ff9800;
    color: white;
    border-color: #ff9800; /* Ensure border color matches */
}
/* --- End Sorting Buttons Styles --- */

/* --- Network Status Indicator Styles --- */
.network-status-indicator {
    position: fixed;
    top: 50px; /* Below the top fixed bar */
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    z-index: 100002;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    display: none;
    animation: slideDown 0.3s ease;
}

.network-status-indicator.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.network-status-indicator.warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.network-status-indicator.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Offline Mode Styles */
.offline-mode {
    filter: grayscale(0.3);
    opacity: 0.9;
}

.offline-banner {
    position: fixed;
    top: 45px;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, #607d8b, #455a64);
    color: white;
    text-align: center;
    padding: 8px;
    font-size: 0.9rem;
    z-index: 100001;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Retry Button Styles */
.retry-btn {
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: bold;
    cursor: pointer;
    margin-left: 10px;
    transition: transform 0.2s ease;
}

.retry-btn:hover {
    transform: scale(1.05);
}

.retry-btn:active {
    transform: scale(0.95);
}
/* --- End Network Status Indicator Styles --- */


/* Search Input Styles */
.search-container {
    padding: 15px 20px; /* Add some padding around the search bar */
    margin-bottom: 10px; /* Space below the search bar */
}

.search-input {
    width: 100%;
    padding: 12px 15px;
    font-size: 1rem;
    border: 2px solid var(--accent-color); /* Yellow border */
    border-radius: var(--border-radius);
    background-color: var(--card-background); /* Match card background */
    color: var(--text-color); /* White text */
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.search-input::placeholder {
    color: #aaa; /* Lighter placeholder text */
}

.search-input:focus {
    outline: none; /* Remove default focus outline */
    box-shadow: 0 0 5px var(--accent-color); /* Add a subtle glow on focus */
}

/* --- REMOVED: #moreCategoriesContainer Styles --- */
/* #moreCategoriesContainer { ... } */
/* #moreCategoriesContainer .category-btn { ... } */


/* --- إزالة تأثيرات النقر والتركيز الافتراضية --- */

/* القاعدة العامة لإزالة لون التحديد عند اللمس (للموبايل) */
* {
    -webkit-tap-highlight-color: transparent;
}

/* إزالة الإطار (outline) عند التركيز (focus) لجميع العناصر التفاعلية */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus {
    outline: none !important;
    box-shadow: none !important; /* إزالة أي ظل قد يظهر عند التركيز */
}

/* Adjust :active/:focus-visible styles to prevent overriding .active-category */
/* These should generally not set background color to avoid conflicts */
a:active, a:focus-visible,
button:not(.active-category):not(.active-sort):active,
button:not(.active-category):not(.active-sort):focus-visible,
input:active, input:focus-visible,
select:active, select:focus-visible,
textarea:active, textarea:focus-visible,
[tabindex]:active, [tabindex]:focus-visible {
    color: inherit; /* Prevent unexpected text color changes */
    /* background-color: transparent; <-- Keep removed */
}

/* Removed .category-btn:active rule, will use JS class for press effect */

/* Class for button press effect via JS */
.button-pressed {
    transform: scale(0.98) !important; /* Apply press effect */
}

/* --- أنيميشن الأزرار --- */

/* أنيميشن عام للأزرار عند النقر */
button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

button:active::after {
    width: 200px;
    height: 200px;
}

/* أنيميشن خاص لأزرار التصنيفات */
.category-btn {
    position: relative;
    overflow: hidden;
}

.category-btn::before {
    display: none; /* تم تعطيل الخط الأصفر */
}

/* أنيميشن لزر التحميل */
.download-btn {
    position: relative;
    overflow: hidden;
}

.download-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 25%,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 50%,
        transparent 75%,
        rgba(255, 255, 255, 0.1) 75%
    );
    transform: rotate(45deg);
    animation: downloadGlow 2s infinite linear;
}

@keyframes downloadGlow {
    0% { transform: rotate(45deg) translateX(-50%); }
    100% { transform: rotate(45deg) translateX(50%); }
}

/* --- Search Page Specific Header Styles --- */
.search-page .header {
    height: 60px; /* Adjusted height for search page */
    flex-direction: row; /* Override to row */
    justify-content: space-between; /* Space out items */
    padding: 0 15px; /* Horizontal padding */
    margin-bottom: 15px; /* Reduced margin for search page */
}

/* Hide drawer button on search page */
.search-page .drawer-btn {
    display: none;
}

/* Style back button specifically for search page */
.search-page .back-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.8rem; /* Larger icon */
    cursor: pointer;
    padding: 10px; /* Touch area */
    display: flex; /* Center icon inside button */
    align-items: center;
    justify-content: center;
    line-height: 1; /* Ensure icon is centered vertically */
    order: -1; /* Ensure it comes first in flex row */
}

/* Adjust title for search page header */
.search-page .header h1 {
    font-size: 1.5rem; /* Slightly smaller title */
    margin-bottom: 0; /* Remove bottom margin */
    flex-grow: 1; /* Allow title to take space */
    text-align: center; /* Center title text */
    margin: 0 10px; /* Add some margin around title */
}

/* Ensure search button (though hidden) doesn't interfere with layout */
.search-page .search-btn {
    position: static; /* Remove absolute positioning */
    transform: none; /* Remove transform */
    padding: 10px; /* Match back button padding */
    /* visibility: hidden; is handled inline */
}

/* Hide categories on search page header */
.search-page #categories {
    display: none;
}
/* --- End Search Page Specific Styles --- */

/* --- New Install Instructions Modal Styles --- */
#install-instructions-modal {
    position: fixed;
    top: 45px !important; /* Start below the top bar (Forced) */
    left: 0;
    width: 100%;
    height: calc(100% - 45px) !important; /* Adjust height to fill remaining space (Forced) */
    background-color: #333333; /* Dark Gray background */
    display: flex; /* Use flex for layout */
    flex-direction: column; /* Stack header and content vertically */
    z-index: 100003; /* Ensure it's above everything */
    animation: fadeIn 0.3s ease; /* Reuse existing fadeIn */
    box-sizing: border-box;
}

.modal-install-header {
    width: 100%;
    height: 60px; /* Adjust height as needed */
    background: linear-gradient(to right, #ffcc00, #ff9800); /* Theme gradient */
    display: flex;
    align-items: center;
    padding: 0 15px; /* Padding */
    flex-shrink: 0; /* Prevent header from shrinking */
    box-sizing: border-box;
}

#install-instructions-modal .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 2.5rem; /* Larger '×' */
    font-weight: bold;
    line-height: 1;
    padding: 5px 10px; /* Touch area */
    cursor: pointer;
    margin-right: auto; /* Push to the left */
    transition: transform 0.2s ease;
}
#install-instructions-modal .close-button:hover {
    transform: scale(1.1);
}
#install-instructions-modal .close-button:active {
    transform: scale(0.95);
}

#install-image-container {
    width: 100%;
    flex-grow: 1; /* Take remaining vertical space */
    overflow-y: auto; /* Enable vertical scrolling */
    padding: 20px; /* Padding around images */
    box-sizing: border-box;
    display: flex;
    flex-direction: column; /* Stack images vertically */
    align-items: center; /* Center images horizontally */
    gap: 15px; /* Space between images */
    touch-action: pan-y; /* Explicitly allow vertical panning/scrolling */
}

#install-image-container img {
    display: block;
    max-width: 90%; /* Constrain image width to make it smaller */
    height: auto; /* Maintain aspect ratio */
    border: 3px solid #ffcc00; /* Yellow border */
    border-radius: 5px; /* Slightly rounded corners */
    cursor: pointer; /* Indicate images are clickable */
    transition: transform 0.2s ease; /* Add transition for hover effect */
}

#install-image-container img:hover {
    transform: scale(1.03); /* Slight zoom on hover */
}
/* --- End New Install Instructions Modal Styles --- */

/* --- Image Zoom Modal Styles --- */
#image-zoom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
}

/* Reduce margin below the header for the "All" category section (or similar horizontal sections) */
/* Please adjust #all-mods-section if the ID for your "All" category is different */
#all-mods-section .category-header {
    margin-bottom: 10px; /* Default is 20px */
}

/* --- Featured Addons Styles --- */
.featured-addon {
    position: relative;
    border: 2px solid #ffcc00 !important;
    box-shadow: 0 0 15px 5px rgba(255, 204, 0, 0.5) !important;
    animation: featuredPulse 2s infinite alternate;
    overflow: visible !important;
}

.featured-addon::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: var(--border-radius);
    background: transparent;
    z-index: -1;
    box-shadow: 0 0 15px 5px rgba(255, 204, 0, 0.3);
    animation: glowPulse 2s infinite alternate;
}

/* Yellow orbs animation */
.featured-addon .orb {
    position: absolute;
    width: 10px;
    height: 10px;
    background: rgba(255, 204, 0, 0.8);
    border-radius: 50%;
    z-index: 10;
    pointer-events: none;
    box-shadow: 0 0 10px 2px rgba(255, 204, 0, 0.5);
    opacity: 0;
}

@keyframes featuredPulse {
    0% {
        box-shadow: 0 0 15px 5px rgba(255, 204, 0, 0.3);
    }
    100% {
        box-shadow: 0 0 20px 8px rgba(255, 204, 0, 0.6);
    }
}

@keyframes glowPulse {
    0% {
        box-shadow: 0 0 15px 5px rgba(255, 204, 0, 0.2);
    }
    100% {
        box-shadow: 0 0 25px 10px rgba(255, 204, 0, 0.4);
    }
}

@keyframes orbFloat {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.8);
    }
    50% {
        opacity: 1;
        transform: translateY(-15px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px) scale(0.8);
    }
}

/* Glowing Square Effect */
.glowing-square-effect {
    position: absolute;
    width: 12px; /* Slightly larger for squares */
    height: 12px;
    background: linear-gradient(45deg, #ffd700, #ff9800); /* Golden-orange gradient */
    border-radius: 2px; /* Small border-radius for slightly rounded squares */
    pointer-events: none;
    z-index: 5;
    box-shadow: 0 0 25px 10px rgba(255, 165, 0, 1); /* Increased shadow */
    opacity: 0; /* Start hidden */
    animation: glowingSquareFloat 3s ease-out forwards; /* New animation */
}

@keyframes glowingSquareFloat {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.5) rotate(0deg);
    }
    20% {
        opacity: 1;
        transform: translateY(-10px) scale(1) rotate(45deg);
    }
    80% {
        opacity: 1;
        transform: translateY(-40px) scale(1.2) rotate(90deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-60px) scale(0.8) rotate(135deg);
    }
}

/* Sparkle Effect - Re-purposed for additional glow */
.glowing-sparkle-effect {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #ffcc00; /* Golden color */
    border-radius: 50%; /* Keep as circles for a different sparkle look */
    pointer-events: none;
    animation: glowingSparkleAnimation 2s ease-in-out infinite;
    z-index: 6;
    box-shadow: 0 0 20px 8px rgba(255, 204, 0, 1); /* Increased shadow */
}

@keyframes glowingSparkleAnimation {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
        box-shadow: 0 0 10px 3px rgba(255, 204, 0, 0.9);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
        box-shadow: 0 0 15px 5px rgba(255, 204, 0, 1);
    }
}

/* Remove Free Addons specific styling as it will be replaced by general glowing squares */
/* .free-addon-item, .free-addon-item::before, .free-addon-item:hover,
   @keyframes freeAddonGlow, @keyframes borderGlow,
   .free-badge, @keyframes freeBadgePulse,
   .free-icon, .free-icon::before, @keyframes freeIconPulse {
    display: none;
} */
/* The above commented out block is too broad and might remove other necessary styles.
   Instead, I will remove the specific rules that were previously defined for these.
   The user wants the effect on *all* cards, so the "free-addon-item" class and its
   associated effects are no longer needed. */

/* Remove Free Addons Special Effects */
.free-addon-item {
    /* No special styling for free-addon-item anymore */
    position: relative;
    overflow: hidden;
    background-color: var(--card-background) !important;
    border-radius: var(--border-radius) !important;
    transition: var(--transition);
}

.free-addon-item:hover {
    transform: translateY(-5px);
}

/* Remove Free Badge and Free Icon styles */
.free-badge, .free-icon {
    display: none; /* Hide these elements */
}

/* Remove Free Addons Section Header Enhancement - already done in previous step */
/* #free-addons-section .category-header {
    position: relative;
    overflow: hidden;
} */

@keyframes growShrink {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Custom Dialog Animations */
@keyframes customDialogFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(5px);
    }
}

@keyframes customDialogSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Custom Dialog Styles */
.custom-dialog-overlay {
    animation: customDialogFadeIn 0.3s ease;
}

.custom-dialog-content {
    animation: customDialogSlideIn 0.4s ease;
}

/* Help Icon Styles */
.help-icon {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #ffcc00, #ff9800);
    border: none;
    border-radius: 50%;
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(255, 204, 0, 0.4);
    opacity: 1;
    transform: scale(1);
}

.help-icon:hover {
    background: linear-gradient(135deg, #ffd700, #ffb347);
    box-shadow: 0 6px 20px rgba(255, 204, 0, 0.6);
    transform: scale(1.05);
}

.help-icon:active {
    transform: scale(0.95);
}

/* Help Icon Pulse Animation */
@keyframes helpIconPulse {
    0% {
        box-shadow: 0 4px 15px rgba(255, 204, 0, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 6px 25px rgba(255, 204, 0, 0.8);
        transform: scale(1.1);
    }
    100% {
        box-shadow: 0 4px 15px rgba(255, 204, 0, 0.4);
        transform: scale(1);
    }
}

.help-icon-pulse {
    animation: helpIconPulse 0.6s ease-in-out;
}

/* Help Icon Glow Effect */
.help-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #ffcc00, #ff9800);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.help-icon:hover::before {
    opacity: 0.3;
}

/* Responsive Help Icon */
@media (max-width: 768px) {
    .help-icon {
        width: 30px;
        height: 30px;
        font-size: 14px;
        top: 10px;
        left: 10px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .category-btn {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .item {
        width: calc(50% - 10px);
    }

    .modal-content {
        width: 95%;
        padding: 15px;
    }
}

/* Creator Info Loading Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Quick Loading Indicator Styles */
#quick-loading-indicator {
    backdrop-filter: blur(2px);
}

#quick-loading-indicator div {
    background: transparent;
}

/* Error Message Animation */
@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}
