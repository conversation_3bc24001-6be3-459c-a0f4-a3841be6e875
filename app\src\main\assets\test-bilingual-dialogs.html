<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المربعات ثنائية اللغة - Bilingual Dialogs Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .test-section h2 {
            color: #34495e;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .status-display {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        
        .language-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1000;
        }
        
        .instructions {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #0c5460;
            margin-top: 0;
        }
        
        .instructions ol {
            color: #0c5460;
            margin: 10px 0;
        }
        
        .console-output {
            background: #1a1a2e;
            color: #e5e7eb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #444;
        }
    </style>
</head>
<body>
    <div class="language-indicator" id="languageIndicator">
        🌍 Loading...
    </div>

    <div class="container">
        <h1>🧪 اختبار المربعات ثنائية اللغة</h1>
        <p style="text-align: center; color: #666;">
            Bilingual Custom Dialogs Test Page
        </p>

        <!-- تعليمات الاختبار -->
        <div class="instructions">
            <h3>📋 تعليمات الاختبار / Test Instructions</h3>
            <ol>
                <li>تأكد من تنفيذ SQL في قاعدة البيانات أولاً / Execute SQL in database first</li>
                <li>اختبر تغيير اللغة ومراقبة المربعات / Test language switching and observe dialogs</li>
                <li>راقب Console للرسائل التفصيلية / Monitor Console for detailed messages</li>
            </ol>
        </div>

        <!-- حالة النظام -->
        <div class="test-section">
            <h2>📊 حالة النظام / System Status</h2>
            <div class="status-display" id="systemStatus">جاري التحميل... / Loading...</div>
            <button class="btn" onclick="updateSystemStatus()">🔄 تحديث الحالة / Refresh Status</button>
        </div>

        <!-- اختبار اللغة -->
        <div class="test-section">
            <h2>🌍 اختبار اللغة / Language Testing</h2>
            <button class="btn btn-success" onclick="switchToArabic()">🇸🇦 تبديل للعربية</button>
            <button class="btn btn-success" onclick="switchToEnglish()">🇺🇸 Switch to English</button>
            <button class="btn btn-warning" onclick="testCurrentLanguage()">🧪 اختبار اللغة الحالية</button>
            <div id="languageTestResult" class="status-display" style="display: none;"></div>
        </div>

        <!-- اختبار المربعات -->
        <div class="test-section">
            <h2>📱 اختبار المربعات / Dialog Testing</h2>
            <button class="btn" onclick="testArabicDialog()">🇸🇦 مربع عربي</button>
            <button class="btn" onclick="testEnglishDialog()">🇺🇸 English Dialog</button>
            <button class="btn btn-warning" onclick="testAutoLanguageDialog()">🤖 مربع تلقائي</button>
            <button class="btn btn-danger" onclick="resetDialogs()">🔄 إعادة تعيين المربعات</button>
        </div>

        <!-- سجل وحدة التحكم -->
        <div class="test-section">
            <h2>📝 سجل وحدة التحكم / Console Log</h2>
            <div class="console-output" id="consoleOutput">
                انتظار الرسائل... / Waiting for messages...
            </div>
            <button class="btn" onclick="clearConsoleOutput()">🗑️ مسح السجل / Clear Log</button>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-manager.js"></script>
    <script src="translations.js"></script>
    <script src="bilingual-dialogs.js"></script>

    <script>
        // متغيرات عامة
        let consoleMessages = [];

        // تحديث مؤشر اللغة
        function updateLanguageIndicator() {
            const indicator = document.getElementById('languageIndicator');
            const currentLang = getCurrentDialogLanguage();
            const langText = currentLang === 'ar' ? '🇸🇦 العربية' : '🇺🇸 English';
            indicator.textContent = `🌍 ${langText}`;
        }

        // تحديث حالة النظام
        function updateSystemStatus() {
            const status = document.getElementById('systemStatus');
            const currentLang = getCurrentDialogLanguage();
            const translationManager = window.translationManager;
            const bilingualManager = window.bilingualDialogManager;
            
            const statusText = `
🌍 Current Language: ${currentLang}
📱 Translation Manager: ${translationManager ? '✅ Loaded' : '❌ Not Found'}
🔧 Bilingual Manager: ${bilingualManager ? '✅ Loaded' : '❌ Not Found'}
💾 localStorage Language: ${localStorage.getItem('selectedLanguage') || 'Not Set'}
🆔 User ID: ${bilingualManager?.getUserId() || 'Unknown'}
📊 Shown Dialogs: ${bilingualManager?.shownDialogs.size || 0}
            `.trim();
            
            status.textContent = statusText;
            updateLanguageIndicator();
        }

        // تبديل للعربية
        function switchToArabic() {
            if (window.translationManager) {
                window.translationManager.setLanguage('ar');
            } else {
                localStorage.setItem('selectedLanguage', 'ar');
                setDialogLanguage('ar');
            }
            
            document.documentElement.dir = 'rtl';
            document.documentElement.lang = 'ar';
            
            logMessage('🇸🇦 Switched to Arabic');
            updateSystemStatus();
        }

        // تبديل للإنجليزية
        function switchToEnglish() {
            if (window.translationManager) {
                window.translationManager.setLanguage('en');
            } else {
                localStorage.setItem('selectedLanguage', 'en');
                setDialogLanguage('en');
            }
            
            document.documentElement.dir = 'ltr';
            document.documentElement.lang = 'en';
            
            logMessage('🇺🇸 Switched to English');
            updateSystemStatus();
        }

        // اختبار اللغة الحالية
        function testCurrentLanguage() {
            const currentLang = getCurrentDialogLanguage();
            const result = document.getElementById('languageTestResult');
            
            result.textContent = `Current Language: ${currentLang}`;
            result.style.display = 'block';
            
            logMessage(`🧪 Current language test: ${currentLang}`);
        }

        // اختبار مربع عربي
        async function testArabicDialog() {
            logMessage('🇸🇦 Testing Arabic dialog...');
            try {
                const result = await testBilingualDialog('ar');
                logMessage(`✅ Arabic dialog result: ${JSON.stringify(result)}`);
            } catch (error) {
                logMessage(`❌ Arabic dialog error: ${error.message}`);
            }
        }

        // اختبار مربع إنجليزي
        async function testEnglishDialog() {
            logMessage('🇺🇸 Testing English dialog...');
            try {
                const result = await testBilingualDialog('en');
                logMessage(`✅ English dialog result: ${JSON.stringify(result)}`);
            } catch (error) {
                logMessage(`❌ English dialog error: ${error.message}`);
            }
        }

        // اختبار مربع تلقائي
        async function testAutoLanguageDialog() {
            logMessage('🤖 Testing auto-language dialog...');
            try {
                const result = await testBilingualDialog();
                logMessage(`✅ Auto dialog result: ${JSON.stringify(result)}`);
            } catch (error) {
                logMessage(`❌ Auto dialog error: ${error.message}`);
            }
        }

        // إعادة تعيين المربعات
        function resetDialogs() {
            resetShownDialogs();
            logMessage('🔄 Reset all shown dialogs');
        }

        // تسجيل رسالة
        function logMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            consoleMessages.push(logEntry);
            
            // الاحتفاظ بآخر 50 رسالة فقط
            if (consoleMessages.length > 50) {
                consoleMessages = consoleMessages.slice(-50);
            }
            
            updateConsoleOutput();
            console.log(message);
        }

        // تحديث عرض وحدة التحكم
        function updateConsoleOutput() {
            const output = document.getElementById('consoleOutput');
            output.textContent = consoleMessages.join('\n');
            output.scrollTop = output.scrollHeight;
        }

        // مسح سجل وحدة التحكم
        function clearConsoleOutput() {
            consoleMessages = [];
            updateConsoleOutput();
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('🚀 Test page loaded');
            
            // انتظار تحميل النظام
            setTimeout(() => {
                updateSystemStatus();
                logMessage('📊 System status updated');
            }, 1000);
            
            // مراقبة تغييرات اللغة
            window.addEventListener('languageChanged', (event) => {
                logMessage(`🔄 Language changed event: ${event.detail.language}`);
                updateSystemStatus();
            });
        });

        // اعتراض console.log لعرضه في الصفحة
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            // عرض رسائل النظام فقط
            const message = args.join(' ');
            if (message.includes('🌍') || message.includes('📱') || message.includes('✅') || 
                message.includes('❌') || message.includes('🔄') || message.includes('🧪')) {
                logMessage(message);
            }
        };
    </script>
</body>
</html>
