<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Handler Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: var(--card-background);
            border-radius: var(--border-radius);
            color: var(--text-color);
        }
        
        .test-button {
            background: linear-gradient(45deg, #4caf50, #45a049);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .test-button:hover {
            transform: scale(1.05);
        }
        
        .test-button.error {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .test-button.warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-info {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.05);
        }
    </style>
</head>
<body>
    <div class="top-fixed-bar">
        <span>Network Handler Test Page</span>
    </div>

    <div class="test-container">
        <h1>Network Handler Test Suite</h1>
        
        <div class="status-info">
            <h3>Current Status:</h3>
            <p>Online Status: <span id="onlineStatus">Unknown</span></p>
            <p>Network Handler: <span id="handlerStatus">Not Loaded</span></p>
            <p>Last Check: <span id="lastCheck">Never</span></p>
        </div>

        <div class="test-controls">
            <h3>Test Controls:</h3>
            <button class="test-button" onclick="testConnectivity()">Test Connectivity</button>
            <button class="test-button" onclick="simulateOffline()">Simulate Offline</button>
            <button class="test-button" onclick="simulateOnline()">Simulate Online</button>
            <button class="test-button warning" onclick="showNetworkStatus('Test Warning', 'warning')">Test Warning</button>
            <button class="test-button error" onclick="showNetworkStatus('Test Error', 'error')">Test Error</button>
            <button class="test-button" onclick="showNetworkStatus('Test Success', 'success')">Test Success</button>
            <button class="test-button" onclick="testRetryMechanism()">Test Retry</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>

        <div class="test-results" id="testResults">
Test results will appear here...
        </div>
    </div>

    <!-- Load scripts in correct order -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="network-handler.js"></script>
    <script src="script.js"></script>

    <script>
        // Test functions
        function updateStatus() {
            document.getElementById('onlineStatus').textContent = navigator.onLine ? 'Online' : 'Offline';
            document.getElementById('handlerStatus').textContent = typeof networkHandler !== 'undefined' ? 'Loaded' : 'Not Loaded';
            document.getElementById('lastCheck').textContent = new Date().toLocaleTimeString();
        }

        function logResult(message) {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            results.textContent += `[${timestamp}] ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }

        async function testConnectivity() {
            logResult('Testing connectivity...');
            try {
                const isConnected = await checkNetworkConnectivity();
                logResult(`Connectivity test result: ${isConnected ? 'CONNECTED' : 'DISCONNECTED'}`);
            } catch (error) {
                logResult(`Connectivity test error: ${error.message}`);
            }
            updateStatus();
        }

        function simulateOffline() {
            logResult('Simulating offline mode...');
            if (typeof networkHandler !== 'undefined') {
                networkHandler.isOnline = false;
                networkHandler.showOfflineMode();
                logResult('Offline mode activated');
            } else {
                logResult('Network handler not available');
            }
            updateStatus();
        }

        function simulateOnline() {
            logResult('Simulating online mode...');
            if (typeof networkHandler !== 'undefined') {
                networkHandler.isOnline = true;
                networkHandler.hideOfflineMode();
                logResult('Online mode activated');
            } else {
                logResult('Network handler not available');
            }
            updateStatus();
        }

        function showNetworkStatus(message, type) {
            logResult(`Showing ${type} status: ${message}`);
            if (typeof networkHandler !== 'undefined') {
                networkHandler.showNetworkStatus(message, type, 3000);
            } else {
                logResult('Network handler not available');
            }
        }

        async function testRetryMechanism() {
            logResult('Testing retry mechanism...');
            if (typeof networkHandler !== 'undefined') {
                try {
                    // Test with a URL that will likely fail
                    const result = await networkHandler.fetchWithRetry('https://nonexistent-domain-test.com/api', {}, 2);
                    logResult('Retry test unexpectedly succeeded');
                } catch (error) {
                    logResult(`Retry test failed as expected: ${error.message}`);
                }
            } else {
                logResult('Network handler not available');
            }
        }

        function clearResults() {
            document.getElementById('testResults').textContent = 'Test results cleared...\n';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logResult('Test page loaded');
            updateStatus();
            
            // Update status every 5 seconds
            setInterval(updateStatus, 5000);
            
            // Listen for online/offline events
            window.addEventListener('online', () => {
                logResult('Browser detected: ONLINE');
                updateStatus();
            });
            
            window.addEventListener('offline', () => {
                logResult('Browser detected: OFFLINE');
                updateStatus();
            });
        });
    </script>
</body>
</html>
