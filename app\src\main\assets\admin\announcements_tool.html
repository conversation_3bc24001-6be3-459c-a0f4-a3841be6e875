<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإعلانات</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #21221f;
            color: #ffffff;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: #000000;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 700px;
            border: 1px solid #ffcc00;
            margin-bottom: 30px;
        }
        h1, h2 {
            color: #ffcc00;
            text-align: center;
            margin-bottom: 25px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #ffcc00;
            font-weight: bold;
        }
        input[type="text"],
        input[type="url"],
        textarea,
        select {
            width: calc(100% - 22px);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #555;
            background-color: #333;
            color: #ffffff;
            font-size: 1rem;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        input[type="checkbox"], input[type="radio"] {
            margin-right: 8px;
            vertical-align: middle;
        }
        .checkbox-label, .radio-label {
            color: #ffffff;
            font-weight: normal;
            display: inline-block;
            margin-left: 5px; /* For RTL, this pushes text away from radio/checkbox */
        }
        button {
            background: linear-gradient(to right, #ffcc00, #ff9800);
            color: #000000;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: opacity 0.3s ease;
            display: block;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        #statusMessage {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            display: none;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .error {
            background-color: #dc3545;
            color: white;
        }
        .announcements-list {
            margin-top: 30px;
        }
        .announcement-item {
            background-color: #2a2b28;
            border: 1px solid #444;
            border-right: 5px solid #ff9800; /* Adjusted for RTL */
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .announcement-item h3 {
            color: #ffcc00;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .announcement-item p {
            margin: 5px 0;
            font-size: 0.9rem;
            color: #e0e0e0;
        }
        .announcement-item p strong {
            color: #ffcc00;
        }
        .announcement-item .actions {
            margin-top: 10px;
        }
        .announcement-item .actions button {
            font-size: 0.9rem;
            padding: 8px 12px;
            margin-left: 10px; /* Adjusted for RTL */
            width: auto;
            display: inline-block;
        }
        .announcement-item .actions .toggle-active-btn.active {
             background: linear-gradient(to right, #4CAF50, #45a049);
             color: white;
        }
        .announcement-item .actions .toggle-active-btn.inactive {
             background: linear-gradient(to right, #f44336, #d32f2f);
             color: white;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #ffcc00;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading-text {
            text-align: center;
            color: #ffcc00;
        }
        .delete-btn {
            background: linear-gradient(to right, #c0392b, #e74c3c) !important;
            color: white !important;
        }
        .form-group-radio label { /* For radio group label */
            margin-bottom: 10px;
        }
        .form-group-radio div { /* Container for radio buttons */
            display: flex;
            gap: 15px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إدارة الإعلانات</h1>

        <form id="announcementForm">
            <div class="form-group">
                <label for="title">عنوان الإعلان:</label>
                <input type="text" id="title" name="title" required>
            </div>

            <div class="form-group">
                <label for="description">وصف الإعلان:</label>
                <textarea id="description" name="description" required></textarea>
            </div>

            <div class="form-group">
                <label for="imageUpload">صورة الإعلان (اختياري):</label>
                <input type="file" id="imageUpload" name="image_upload" accept="image/*" style="width: 100%; padding: 10px 0;">
                <div id="imagePreview" style="margin-top: 10px; display: none;">
                    <img id="previewImage" src="" alt="معاينة الصورة" style="max-width: 100%; max-height: 200px; border-radius: 8px;">
                    <button type="button" id="removeImageBtn" style="width: auto; margin-top: 10px; background: linear-gradient(to right, #c0392b, #e74c3c); color: white;">إزالة الصورة</button>
                </div>
                <input type="hidden" id="imageUrl" name="image_url">
                <div id="uploadProgress" style="margin-top: 10px; display: none;">
                    <div style="height: 20px; background-color: #333; border-radius: 4px; overflow: hidden;">
                        <div id="progressBar" style="height: 100%; width: 0%; background: linear-gradient(to right, #ffcc00, #ff9800);"></div>
                    </div>
                    <p id="progressText" style="text-align: center; margin-top: 5px; font-size: 0.9rem;">0%</p>
                </div>
            </div>

            <div class="form-group">
                <label for="buttonText">نص زر المتابعة:</label>
                <input type="text" id="buttonText" name="button_text" value="متابعة" required>
            </div>
            
            <div class="form-group form-group-radio">
                <label>مرات الظهور:</label>
                <div>
                    <input type="radio" id="showOnce" name="display_frequency" value="once" checked>
                    <label for="showOnce" class="radio-label">مرة واحدة لكل مستخدم</label>
                </div>
                <div>
                    <input type="radio" id="showAlways" name="display_frequency" value="always">
                    <label for="showAlways" class="radio-label">في كل مرة يفتح التطبيق</label>
                </div>
            </div>

            <div class="form-group">
                <input type="checkbox" id="isActive" name="is_active" checked>
                <label for="isActive" class="checkbox-label">تفعيل الإعلان (سيظهر للمستخدمين)</label>
            </div>
            
            <input type="hidden" id="announcementId" name="announcement_id">

            <button type="submit" id="saveBtn">حفظ الإعلان</button>
        </form>
        <div id="statusMessage"></div>
    </div>

    <div class="container announcements-list">
        <h2>الإعلانات الحالية</h2>
        <div id="announcementsContainer">
            <div class="loading-text">جاري تحميل الإعلانات...</div>
            <div class="loader"></div>
        </div>
    </div>

    <script>
        // Supabase Configuration (Same as update_notification_tool.html for now)
        const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
        const ANNOUNCEMENTS_TABLE = 'app_announcements'; // New table for announcements
        const STORAGE_BUCKET = 'image3'; // Changed to use image3 bucket

        const supabaseHeaders = (method = 'GET') => {
            const headers = {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            };
            if (method !== 'GET' && method !== 'HEAD') {
                headers['Content-Type'] = 'application/json';
            }
            if (method === 'POST' || method === 'PATCH') {
                headers['Prefer'] = 'return=representation';
            } else if (method === 'DELETE') {
                headers['Prefer'] = 'return=minimal';
            }
            return headers;
        };
        
        const form = document.getElementById('announcementForm');
        const statusMessage = document.getElementById('statusMessage');
        const announcementsContainer = document.getElementById('announcementsContainer');
        const saveBtn = document.getElementById('saveBtn');
        const announcementIdField = document.getElementById('announcementId');
        const imageUrlInput = document.getElementById('imageUrl');

        // document.documentElement.setAttribute('dir', 'rtl'); // Already set in HTML tag

        async function fetchAnnouncements() {
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${ANNOUNCEMENTS_TABLE}?select=*&order=created_at.desc`, {
                    method: 'GET',
                    headers: supabaseHeaders('GET')
                });
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(`خطأ في الطلب! الحالة: ${response.status}. ${errorData.message || ''}`);
                }
                const announcements = await response.json();
                renderAnnouncements(announcements);
            } catch (error) {
                console.error('خطأ في جلب الإعلانات:', error);
                announcementsContainer.innerHTML = `<p style="color:red;">فشل في تحميل الإعلانات: ${error.message}. تأكد من صحة إعدادات Supabase واسم الجدول.</p>`;
            }
        }

        function renderAnnouncements(announcements) {
            if (!announcements || announcements.length === 0) {
                announcementsContainer.innerHTML = '<p>لا توجد إعلانات حالياً.</p>';
                return;
            }
            
            announcementsContainer.innerHTML = ''; // Clear previous items
            
            announcements.forEach(ann => {
                const item = document.createElement('div');
                item.classList.add('announcement-item');
                
                const safeTitle = escapeHtml(ann.title);
                const safeDescription = escapeHtml(ann.description).substring(0, 150) + (ann.description.length > 150 ? '...' : '');
                const safeButtonText = escapeHtml(ann.button_text);
                const safeImageUrl = escapeHtml(ann.image_url || '');
                const safeId = escapeHtml(ann.id);

                let imagePreviewHtml = '';
                if (safeImageUrl) {
                    imagePreviewHtml = `<p><strong>الصورة:</strong> <a href="${safeImageUrl}" target="_blank">${safeImageUrl.substring(0,30)}...</a> <img src="${safeImageUrl}" alt="Preview" style="max-width: 50px; max-height: 50px; vertical-align: middle; margin-left: 10px; border-radius: 4px;"></p>`;
                }

                const displayFrequencyText = ann.display_frequency === 'once' ? 'مرة واحدة لكل مستخدم' : 'في كل مرة يفتح التطبيق';

                item.innerHTML = `
                    <h3>${safeTitle}</h3>
                    <p><strong>المعرف:</strong> ${safeId}</p>
                    <p><strong>الوصف:</strong> ${safeDescription}</p>
                    <p><strong>نص الزر:</strong> ${safeButtonText}</p>
                    ${imagePreviewHtml}
                    <p><strong>مرات الظهور:</strong> ${displayFrequencyText}</p>
                    <p><strong>نشط:</strong> ${ann.is_active ? 'نعم' : 'لا'}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${new Date(ann.created_at).toLocaleString('ar-SA')}</p>
                    <div class="actions">
                        <button onclick="editAnnouncement('${safeId}')">تعديل</button>
                        <button class="toggle-active-btn ${ann.is_active ? 'active' : 'inactive'}" 
                                onclick="toggleActiveStatus('${safeId}', ${!ann.is_active})">
                            ${ann.is_active ? 'تعطيل' : 'تفعيل'}
                        </button>
                        <button class="delete-btn" onclick="deleteAnnouncement('${safeId}')">حذف</button>
                    </div>
                `;
                announcementsContainer.appendChild(item);
            });
        }
        
        window.editAnnouncement = async function(id) {
            try {
                showStatus('جاري تحميل بيانات الإعلان...', false, true);
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${ANNOUNCEMENTS_TABLE}?id=eq.${encodeURIComponent(id)}&select=*`, {
                    method: 'GET',
                    headers: supabaseHeaders('GET')
                });
                if (!response.ok) {
                     const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(`فشل في جلب تفاصيل الإعلان. الحالة: ${response.status}. ${errorData.message || ''}`);
                }
                const annArray = await response.json();
                if (!annArray || annArray.length === 0) throw new Error('الإعلان غير موجود.');
                
                const ann = annArray[0];
                document.getElementById('title').value = ann.title;
                document.getElementById('description').value = ann.description;
                document.getElementById('buttonText').value = ann.button_text;
                imageUrlInput.value = ann.image_url || '';
                if (ann.image_url) {
                    document.getElementById('previewImage').src = ann.image_url;
                    document.getElementById('imagePreview').style.display = 'block';
                } else {
                    document.getElementById('imagePreview').style.display = 'none';
                }
                document.getElementById('isActive').checked = Boolean(ann.is_active);
                
                // Set radio button for display_frequency
                if (ann.display_frequency === 'once') {
                    document.getElementById('showOnce').checked = true;
                } else if (ann.display_frequency === 'always') {
                    document.getElementById('showAlways').checked = true;
                }

                announcementIdField.value = ann.id;
                saveBtn.textContent = 'تحديث الإعلان';
                window.scrollTo(0, 0);
                hideStatus();
            } catch (error) {
                console.error('خطأ في تحميل الإعلان للتعديل:', error);
                showStatus('خطأ في تحميل الإعلان: ' + error.message, true);
            }
        }

        window.toggleActiveStatus = async function(id, newStatus) {
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${ANNOUNCEMENTS_TABLE}?id=eq.${encodeURIComponent(id)}`, {
                    method: 'PATCH',
                    headers: supabaseHeaders('PATCH'), 
                    body: JSON.stringify({ is_active: newStatus })
                });
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(errorData.message || `فشل في تبديل الحالة. الحالة: ${response.status}`);
                }
                showStatus(`تم ${newStatus ? 'تفعيل' : 'تعطيل'} الإعلان بنجاح.`, false);
                fetchAnnouncements();
            } catch (error) {
                console.error('خطأ في تبديل حالة التفعيل:', error);
                showStatus('خطأ: ' + error.message, true);
            }
        }
        
        window.deleteAnnouncement = async function(id) {
            if (!confirm('هل أنت متأكد من رغبتك في حذف هذا الإعلان؟')) return;
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${ANNOUNCEMENTS_TABLE}?id=eq.${encodeURIComponent(id)}`, {
                    method: 'DELETE',
                    headers: supabaseHeaders('DELETE')
                });
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(errorData.message || `فشل في حذف الإعلان. الحالة: ${response.status}`);
                }
                showStatus('تم حذف الإعلان بنجاح.', false);
                fetchAnnouncements();
            } catch (error) {
                console.error('خطأ في حذف الإعلان:', error);
                showStatus('خطأ: ' + error.message, true);
            }
        }

        function showStatus(message, isError, persistent = false) {
            statusMessage.textContent = message;
            statusMessage.className = isError ? 'error' : 'success';
            statusMessage.style.display = 'block';
            if (!persistent) {
                setTimeout(hideStatus, 5000);
            }
        }
        
        function hideStatus() {
            statusMessage.textContent = '';
            statusMessage.className = '';
            statusMessage.style.display = 'none';
        }
        
        function escapeHtml(unsafe) {
    if (unsafe === null || unsafe === undefined) return '';
    return String(unsafe)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

        const imageUpload = document.getElementById('imageUpload');
        const previewImage = document.getElementById('previewImage');
        const imagePreview = document.getElementById('imagePreview');
        const removeImageBtn = document.getElementById('removeImageBtn');
        const uploadProgress = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        async function uploadImage(file) {
            try {
                const fileName = `announcement_img_${Date.now()}_${file.name.replace(/[^a-zA-Z0-9._-]/g, '')}`; // Sanitize file name
                const filePath = `${fileName}`; // Store directly in the bucket root for simplicity, or use a folder
                
                uploadProgress.style.display = 'block';
                progressBar.style.width = '0%';
                progressText.textContent = '0%';
                
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${SUPABASE_URL}/storage/v1/object/${STORAGE_BUCKET}/${filePath}`);
                xhr.setRequestHeader('apikey', SUPABASE_ANON_KEY);
                xhr.setRequestHeader('Authorization', `Bearer ${SUPABASE_ANON_KEY}`);
                xhr.setRequestHeader('x-upsert', 'true'); // Overwrite if file with same name exists
                xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream');
                
                xhr.upload.onprogress = (event) => {
                    if (event.lengthComputable) {
                        const percentComplete = Math.round((event.loaded / event.total) * 100);
                        progressBar.style.width = percentComplete + '%';
                        progressText.textContent = percentComplete + '%';
                    }
                };
                
                return new Promise((resolve, reject) => {
                    xhr.onload = function() {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            uploadProgress.style.display = 'none';
                            const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/${STORAGE_BUCKET}/${filePath}`;
                            resolve(publicUrl);
                        } else {
                            uploadProgress.style.display = 'none';
                            const errorResponseText = xhr.responseText || "No response text.";
                            console.error(`Supabase Storage Error Response: ${errorResponseText}`);
                            reject(new Error(`فشل في رفع الصورة. الرمز: ${xhr.status}. التفاصيل: ${errorResponseText}`));
                        }
                    };
                    
                    xhr.onerror = function() {
                        uploadProgress.style.display = 'none';
                        reject(new Error('حدث خطأ في الشبكة أثناء رفع الصورة'));
                    };
                    
                    xhr.send(file);
                });
            } catch (error) {
                uploadProgress.style.display = 'none';
                console.error('خطأ في رفع الصورة:', error);
                throw error;
            }
        }
        
        imageUpload.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    imagePreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                imagePreview.style.display = 'none';
                imageUrlInput.value = ''; // Clear if no file selected
            }
        });
        
        removeImageBtn.addEventListener('click', function() {
            imageUpload.value = ''; // Clear the file input
            previewImage.src = '';
            imagePreview.style.display = 'none';
            imageUrlInput.value = ''; // Clear the hidden image URL field
        });
        
        form.addEventListener('submit', async function(event) {
            event.preventDefault();
            hideStatus();
            saveBtn.disabled = true;
            saveBtn.textContent = 'جاري الحفظ...';

            try {
                const imageFile = imageUpload.files[0];
                if (imageFile) { // If a new image is selected for upload
                    showStatus('جاري رفع الصورة...', false, true);
                    const uploadedImageUrl = await uploadImage(imageFile);
                    imageUrlInput.value = uploadedImageUrl; // Set the hidden input to the new URL
                }
                // If no new image is selected, imageUrlInput.value will retain its existing value (e.g., from editing an item) or be empty.
                
                await submitForm();
            } catch (error) {
                console.error('خطأ في معالجة الإرسال:', error);
                showStatus('خطأ: ' + error.message, true);
            } finally {
                saveBtn.disabled = false;
                saveBtn.textContent = announcementIdField.value ? 'تحديث الإعلان' : 'حفظ الإعلان';
            }
        });
        
        async function submitForm() {
            const formData = new FormData(form);
            const data = {};
            for (const [key, value] of formData.entries()) {
                if (key !== 'image_upload') {
                    data[key] = value;
                }
            }
            data['is_active'] = document.getElementById('isActive').checked;
            // display_frequency is already captured by FormData
            
            const currentAnnouncementId = announcementIdField.value;
            let url, methodRequest; 
            const payload = { ...data };
            delete payload.announcement_id; // This was the hidden input field name
            // 'id' is not part of the form, so no need to delete if not present

            if (currentAnnouncementId) { 
                methodRequest = 'PATCH';
                url = `${SUPABASE_URL}/rest/v1/${ANNOUNCEMENTS_TABLE}?id=eq.${encodeURIComponent(currentAnnouncementId)}`;
            } else { 
                methodRequest = 'POST';
                url = `${SUPABASE_URL}/rest/v1/${ANNOUNCEMENTS_TABLE}`;
            }

            try {
                showStatus('جاري حفظ البيانات...', false, true);
                const response = await fetch(url, {
                    method: methodRequest,
                    headers: supabaseHeaders(methodRequest), 
                    body: JSON.stringify(payload),
                });

                if (response.ok) {
                    let resultMessage = '';
                     // For PATCH, Supabase might return 204 (No Content) or 200 with representation
                    if (methodRequest === 'PATCH' && (response.status === 204 || response.status === 200)) {
                        resultMessage = 'تم تحديث الإعلان بنجاح!';
                         if (response.status === 200) await response.json(); // Consume body if present
                    } else if (methodRequest === 'POST' && response.status === 201) {
                        const resultArray = await response.json();
                        const result = resultArray && resultArray.length > 0 ? resultArray[0] : null;
                        resultMessage = 'تم حفظ الإعلان بنجاح!' + (result && result.id ? ` المعرف: ${result.id}` : '');
                    } else {
                        // Handle unexpected success statuses if necessary
                        const responseData = await response.json().catch(() => null);
                        resultMessage = `تمت العملية بنجاح (الحالة: ${response.status}).`;
                        console.log("Unexpected success response:", responseData);
                    }
                    showStatus(resultMessage, false);
                    resetForm();
                    fetchAnnouncements(); 
                } else {
                    let detailedErrorMessage = `فشل حفظ الإعلان. Status: ${response.status}`;
                    try {
                        const errorResult = await response.json();
                        if (errorResult && errorResult.message) {
                            detailedErrorMessage = `خطأ: ${errorResult.message}`;
                            if (errorResult.details) detailedErrorMessage += ` التفاصيل: ${errorResult.details}`;
                            if (errorResult.hint) detailedErrorMessage += ` تلميح: ${errorResult.hint}`;
                            if (errorResult.code) detailedErrorMessage += ` (الرمز: ${errorResult.code})`;
                        }
                    } catch (e) { /* Failed to parse error JSON */ }
                    showStatus(detailedErrorMessage, true);
                }
            } catch (error) {
                console.error('خطأ في الإرسال (submitForm):', error);
                showStatus('خطأ في الشبكة أو عدم استجابة الخادم.', true);
            }
        }
        
        function resetForm() {
            form.reset(); // Resets all form fields to their default values
            announcementIdField.value = ''; 
            saveBtn.textContent = 'حفظ الإعلان';
            imageUrlInput.value = ''; 
            imageUpload.value = ''; // Clear file input explicitly
            previewImage.src = '';
            imagePreview.style.display = 'none';
            document.getElementById('isActive').checked = true; // Default active
            document.getElementById('showOnce').checked = true; // Default show once
            // Default values for text inputs if needed
            // document.getElementById('title').value = ''; 
            // document.getElementById('description').value = '';
            // document.getElementById('buttonText').value = 'متابعة';
        }
        
        document.addEventListener('DOMContentLoaded', fetchAnnouncements);
    </script></body>
</html>
