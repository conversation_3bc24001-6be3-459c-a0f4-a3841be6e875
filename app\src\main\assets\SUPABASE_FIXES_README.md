# Supabase Multiple Client Instances & Table Issues - Solution

## Problems Identified

### 1. Multiple GoTrueClient Instances Warning
```
Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
```

### 2. Table Not Found Error (400 Status)
```
ytqxxodyecdeosnqoure.supabase.co/rest/v1/suggested_mods?select=id%2Cmod_id%2Cdisplay_order%2Cis_required&order=display_order.asc:1 
Failed to load resource: the server responded with a status of 400 ()
Error loading current suggested mods: Object
```

## Root Causes

1. **Multiple Client Instances**: Different scripts were creating separate Supabase client instances
2. **Missing Tables**: The `suggested_mods` table and other required tables don't exist in the database
3. **No Error Handling**: No graceful handling for missing tables or connection issues

## Solutions Implemented

### 1. Centralized Supabase Client Manager

**File**: `app/src/main/assets/supabase-manager.js`

Created a centralized manager that:
- **Prevents multiple client instances** by using a singleton pattern
- **Manages both main and storage clients** with proper configuration
- **Automatically initializes required tables** when they don't exist
- **Provides safe query methods** with error handling and fallbacks

**Key Features**:
```javascript
class SupabaseManager {
    // Singleton pattern prevents multiple instances
    getClient(type = 'main') // Returns cached client or creates new one
    getMainClient()          // Database operations
    getStorageClient()       // File storage operations
    
    // Table management
    initializeTables()       // Creates missing tables
    ensureTableExists()      // Checks and creates individual tables
    safeQuery()             // Query with error handling and fallbacks
}
```

### 2. Enhanced Client Configuration

**Auth Settings** to prevent session conflicts:
```javascript
{
    auth: {
        persistSession: false,    // Prevent session conflicts
        autoRefreshToken: false,  // Disable auto-refresh
        detectSessionInUrl: false // Disable URL session detection
    }
}
```

### 3. Automatic Table Creation

The manager automatically creates missing tables:

#### suggested_mods Table
```sql
CREATE TABLE IF NOT EXISTS suggested_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_suggested_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_suggested_mod UNIQUE (mod_id)
);
```

#### featured_addons Table
```sql
CREATE TABLE IF NOT EXISTS featured_addons (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_featured_addons_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_featured_addon UNIQUE (mod_id)
);
```

#### free_addons Table
```sql
CREATE TABLE IF NOT EXISTS free_addons (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_free_addons_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_free_addon UNIQUE (mod_id)
);
```

#### banner_ads Table
```sql
CREATE TABLE IF NOT EXISTS banner_ads (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    click_url TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 4. Updated Script Files

#### Main Script (`script.js`)
- **Removed direct client creation**
- **Uses centralized manager** for all Supabase operations
- **Enhanced error handling** for missing tables
- **Cached data fallbacks** for offline scenarios

#### Admin Script (`admin_script.js`)
- **Uses centralized clients** to prevent multiple instances
- **Enhanced error handling** for table operations
- **Graceful degradation** when tables don't exist
- **Safe query methods** with automatic retries

#### HTML Files
- **Added supabase-manager.js** before other scripts
- **Proper script loading order** to ensure dependencies

### 5. Enhanced Error Handling

#### Table Not Found Handling
```javascript
if (suggestedError.code === 'PGRST116' || suggestedError.message.includes('does not exist')) {
    currentSuggestedModsBody.innerHTML = '<tr><td colspan="5">جدول المودات المقترحة غير موجود. سيتم إنشاؤه تلقائياً عند إضافة أول مود مقترح.</td></tr>';
    return;
}
```

#### Safe Query Wrapper
```javascript
async safeQuery(tableName, queryBuilder, fallbackData = null) {
    try {
        const result = await queryBuilder;
        return result;
    } catch (error) {
        if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
            await this.initializeTables();
            // Retry once
            return await queryBuilder;
        }
        return { data: fallbackData, error };
    }
}
```

## Benefits

### 1. Eliminated Multiple Client Warnings
- **Single client instance** per type (main/storage)
- **Proper session management** prevents conflicts
- **Cleaner console output** without warnings

### 2. Automatic Table Management
- **No manual table creation** required
- **Graceful handling** of missing tables
- **Automatic retry** after table creation

### 3. Better Error Handling
- **User-friendly error messages** in Arabic
- **Fallback data** when possible
- **Prevents app crashes** from missing tables

### 4. Improved Performance
- **Cached client instances** reduce overhead
- **Efficient table checking** with minimal queries
- **Background initialization** doesn't block UI

## Testing Recommendations

### 1. Verify Single Client Instance
- Check browser console for warnings
- Confirm no multiple GoTrueClient messages

### 2. Test Missing Tables
- Delete tables from Supabase dashboard
- Verify automatic recreation
- Check error handling messages

### 3. Test Admin Functions
- Try adding suggested mods
- Verify table creation on first use
- Test error recovery

## Future Enhancements

### 1. Migration System
- **Version tracking** for table schemas
- **Automatic migrations** for schema changes
- **Rollback capabilities** for failed migrations

### 2. Connection Pooling
- **Connection reuse** for better performance
- **Automatic reconnection** on failures
- **Load balancing** for multiple instances

### 3. Advanced Caching
- **Query result caching** for frequently accessed data
- **Intelligent cache invalidation** on updates
- **Offline-first architecture** with sync capabilities

This implementation provides a robust foundation for Supabase operations while eliminating the multiple client instance warnings and handling missing tables gracefully.
