// إدارة المودات المرتبطة بالمربع المخصص
let supabaseClient;
let dialogId;
let allMods = [];
let selectedMods = new Set();
let currentPage = 1;
const modsPerPage = 12;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 بدء تهيئة تطبيق إدارة مودات المربع...');

    try {
        // الحصول على معرف المربع من URL
        const urlParams = new URLSearchParams(window.location.search);
        dialogId = urlParams.get('dialog_id');

        console.log('🆔 معرف المربع:', dialogId);

        if (!dialogId) {
            showMessage('معرف المربع غير موجود', 'error');
            return;
        }

        // انتظار تحميل Supabase Manager
        console.log('⏳ انتظار تحميل Supabase Manager...');
        await waitForSupabaseManager();
        supabaseClient = supabaseManager.getMainClient();
        console.log('✅ تم الحصول على عميل Supabase');

        // تحميل بيانات المربع
        console.log('📦 تحميل بيانات المربع...');
        await loadDialogInfo();

        // تحميل المودات المحددة مسبقاً
        console.log('🎯 تحميل المودات المحددة مسبقاً...');
        await loadSelectedMods();

        // تحميل جميع المودات
        console.log('📋 تحميل جميع المودات...');
        await loadMods();

        // إعداد البحث
        console.log('🔍 إعداد البحث...');
        setupSearch();

        console.log('🎉 تم تهيئة التطبيق بنجاح!');

    } catch (error) {
        console.error('💥 خطأ في تهيئة التطبيق:', error);
        showMessage('خطأ في تحميل التطبيق: ' + error.message, 'error');

        // عرض رسالة خطأ في شبكة المودات أيضاً
        document.getElementById('modsGrid').innerHTML =
            '<div class="error-message">خطأ في تهيئة التطبيق: ' + error.message + '</div>';
    }
});

// إنشاء Supabase Manager مبسط إذا لم يكن موجوداً
function createFallbackSupabaseManager() {
    console.log('🔧 إنشاء Supabase Manager احتياطي...');

    const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';

    window.supabaseManager = {
        getMainClient() {
            if (!this.client) {
                console.log('🔗 إنشاء عميل Supabase جديد...');
                this.client = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
                    auth: {
                        persistSession: false,
                        autoRefreshToken: false,
                        detectSessionInUrl: false
                    }
                });
            }
            return this.client;
        }
    };

    console.log('✅ تم إنشاء Supabase Manager احتياطي');
}

// انتظار تحميل Supabase Manager
function waitForSupabaseManager() {
    return new Promise((resolve, reject) => {
        console.log('🔍 فحص وجود Supabase Manager...');

        if (window.supabaseManager) {
            console.log('✅ Supabase Manager موجود مسبقاً');
            resolve();
            return;
        }

        console.log('⏳ انتظار تحميل Supabase Manager...');
        let attempts = 0;
        const maxAttempts = 30; // 3 ثوان

        const checkInterval = setInterval(() => {
            attempts++;
            console.log(`🔄 محاولة ${attempts}/${maxAttempts} للعثور على Supabase Manager...`);

            if (window.supabaseManager) {
                console.log('✅ تم العثور على Supabase Manager!');
                clearInterval(checkInterval);
                resolve();
            } else if (attempts >= maxAttempts) {
                console.warn('⚠️ لم يتم العثور على Supabase Manager، سيتم إنشاء نسخة احتياطية');
                clearInterval(checkInterval);

                // إنشاء نسخة احتياطية
                try {
                    createFallbackSupabaseManager();
                    resolve();
                } catch (error) {
                    console.error('❌ فشل في إنشاء Supabase Manager احتياطي:', error);
                    reject(new Error('فشل في تحميل أو إنشاء Supabase Manager: ' + error.message));
                }
            }
        }, 100);
    });
}

// تحميل معلومات المربع
async function loadDialogInfo() {
    try {
        const { data, error } = await supabaseClient
            .from('custom_mod_dialogs')
            .select('*')
            .eq('id', dialogId)
            .single();

        if (error) {
            throw error;
        }

        document.getElementById('dialogTitle').textContent = data.title;
        document.getElementById('dialogDescription').textContent = data.description || 'لا يوجد وصف';

    } catch (error) {
        console.error('خطأ في تحميل معلومات المربع:', error);
        showMessage('خطأ في تحميل معلومات المربع: ' + error.message, 'error');
    }
}

// تحميل المودات المحددة مسبقاً
async function loadSelectedMods() {
    try {
        const { data, error } = await supabaseClient
            .from('custom_dialog_mods')
            .select(`
                mod_id,
                mods (
                    id,
                    name,
                    image_urls,
                    category,
                    downloads,
                    likes
                )
            `)
            .eq('dialog_id', dialogId);

        if (error) {
            throw error;
        }

        selectedMods.clear();
        data.forEach(item => {
            if (item.mods) {
                selectedMods.add(item.mod_id);
            }
        });

        updateSelectedModsDisplay();

    } catch (error) {
        console.error('خطأ في تحميل المودات المحددة:', error);
        showMessage('خطأ في تحميل المودات المحددة: ' + error.message, 'error');
    }
}

// تحميل جميع المودات
async function loadMods() {
    console.log('🔄 بدء تحميل المودات...');

    try {
        const searchTerm = document.getElementById('searchBox').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;

        console.log('🔍 معايير البحث:', { searchTerm, categoryFilter });

        let query = supabaseClient
            .from('mods')
            .select('id, name, image_urls, category, downloads, likes, created_at');

        if (categoryFilter) {
            query = query.eq('category', categoryFilter);
        }

        if (searchTerm) {
            query = query.ilike('name', `%${searchTerm}%`);
        }

        console.log('📡 إرسال طلب إلى Supabase...');
        const { data, error } = await query
            .order('created_at', { ascending: false });

        console.log('📊 استجابة Supabase:', { data: data?.length, error });

        if (error) {
            console.error('❌ خطأ من Supabase:', error);
            throw error;
        }

        allMods = data || [];
        currentPage = 1;

        console.log(`✅ تم تحميل ${allMods.length} مود بنجاح`);
        displayMods();

    } catch (error) {
        console.error('💥 خطأ في تحميل المودات:', error);
        document.getElementById('modsGrid').innerHTML =
            '<div class="error-message">خطأ في تحميل المودات: ' + error.message + '</div>';
    }
}

// عرض المودات
function displayMods() {
    const container = document.getElementById('modsGrid');
    const startIndex = (currentPage - 1) * modsPerPage;
    const endIndex = startIndex + modsPerPage;
    const modsToShow = allMods.slice(startIndex, endIndex);

    if (modsToShow.length === 0) {
        container.innerHTML = '<p style="color: #888; text-align: center; grid-column: 1 / -1;">لا توجد مودات</p>';
        return;
    }

    const modsHTML = modsToShow.map(mod => {
        const isSelected = selectedMods.has(mod.id);
        const mainImage = getMainImage(mod.image_urls);

        return `
            <div class="mod-item ${isSelected ? 'selected' : ''}"
                 onclick="toggleModSelection('${mod.id}')"
                 data-mod-id="${mod.id}">
                <img src="${mainImage}" alt="${mod.name}" class="mod-image"
                     onerror="this.src='../image/placeholder.png'">
                <div class="mod-name">${escapeHtml(mod.name)}</div>
                <div class="mod-stats">
                    📥 ${formatCount(mod.downloads || 0)} |
                    ❤️ ${formatCount(mod.likes || 0)}
                </div>
                <div class="mod-category">${mod.category}</div>
            </div>
        `;
    }).join('');

    container.innerHTML = modsHTML;
    updatePagination();
}

// تبديل تحديد المود
function toggleModSelection(modId) {
    if (selectedMods.has(modId)) {
        selectedMods.delete(modId);
    } else {
        selectedMods.add(modId);
    }

    // تحديث عرض المود في الشبكة
    const modElement = document.querySelector(`[data-mod-id="${modId}"]`);
    if (modElement) {
        modElement.classList.toggle('selected', selectedMods.has(modId));
    }

    updateSelectedModsDisplay();
}

// تحديث عرض المودات المحددة
async function updateSelectedModsDisplay() {
    const container = document.getElementById('selectedModsList');
    const countElement = document.getElementById('selectedCount');

    countElement.textContent = selectedMods.size;

    if (selectedMods.size === 0) {
        container.innerHTML = '<p style="color: #888; text-align: center;">لم يتم تحديد أي مودات بعد</p>';
        return;
    }

    try {
        const { data, error } = await supabaseClient
            .from('mods')
            .select('id, name, image_urls')
            .in('id', Array.from(selectedMods));

        if (error) {
            throw error;
        }

        const selectedHTML = data.map(mod => {
            const mainImage = getMainImage(mod.image_urls);

            return `
                <div class="selected-mod-item">
                    <img src="${mainImage}" alt="${mod.name}" class="selected-mod-image"
                         onerror="this.src='../image/placeholder.png'">
                    <div class="selected-mod-info">
                        <div class="selected-mod-name">${escapeHtml(mod.name)}</div>
                        <div style="color: #888; font-size: 0.9rem;">ID: ${mod.id}</div>
                    </div>
                    <button class="remove-btn" onclick="toggleModSelection('${mod.id}')">
                        إزالة
                    </button>
                </div>
            `;
        }).join('');

        container.innerHTML = selectedHTML;

    } catch (error) {
        console.error('خطأ في تحديث عرض المودات المحددة:', error);
    }
}

// حفظ المودات المحددة
async function saveSelectedMods() {
    try {
        // حذف الربطات الموجودة
        const { error: deleteError } = await supabaseClient
            .from('custom_dialog_mods')
            .delete()
            .eq('dialog_id', dialogId);

        if (deleteError) {
            throw deleteError;
        }

        // إضافة الربطات الجديدة
        if (selectedMods.size > 0) {
            const insertData = Array.from(selectedMods).map(modId => ({
                dialog_id: parseInt(dialogId),
                mod_id: modId
            }));

            const { error: insertError } = await supabaseClient
                .from('custom_dialog_mods')
                .insert(insertData);

            if (insertError) {
                throw insertError;
            }
        }

        showMessage(`تم حفظ ${selectedMods.size} مود بنجاح`, 'success');

    } catch (error) {
        console.error('خطأ في حفظ المودات:', error);
        showMessage('خطأ في حفظ المودات: ' + error.message, 'error');
    }
}

// إعداد البحث
function setupSearch() {
    const searchBox = document.getElementById('searchBox');
    const categoryFilter = document.getElementById('categoryFilter');

    let searchTimeout;

    searchBox.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            loadMods();
        }, 500);
    });

    categoryFilter.addEventListener('change', function() {
        loadMods();
    });
}

// تحديث ترقيم الصفحات
function updatePagination() {
    const container = document.getElementById('pagination');
    const totalPages = Math.ceil(allMods.length / modsPerPage);

    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // زر السابق
    paginationHTML += `
        <button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
            السابق
        </button>
    `;

    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            paginationHTML += `
                <button onclick="changePage(${i})" ${i === currentPage ? 'class="active"' : ''}>
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            paginationHTML += '<span>...</span>';
        }
    }

    // زر التالي
    paginationHTML += `
        <button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
            التالي
        </button>
    `;

    container.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    const totalPages = Math.ceil(allMods.length / modsPerPage);
    if (page < 1 || page > totalPages) return;

    currentPage = page;
    displayMods();
}

// الحصول على الصورة الرئيسية
function getMainImage(imageUrls) {
    if (!imageUrls) return '../image/placeholder.png';

    try {
        const urls = typeof imageUrls === 'string' ? JSON.parse(imageUrls) : imageUrls;
        return Array.isArray(urls) && urls.length > 0 ? urls[0] : '../image/placeholder.png';
    } catch {
        return '../image/placeholder.png';
    }
}

// تنسيق الأرقام
function formatCount(count) {
    if (count >= 1000000) {
        return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
}

// تأمين النص من HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// عرض الرسائل
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;

    container.innerHTML = '';
    container.appendChild(messageDiv);

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}
