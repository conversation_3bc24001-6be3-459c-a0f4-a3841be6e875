
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة إشعارات التحديث</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #21221f;
            color: #ffffff;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: #000000;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 700px;
            border: 1px solid #ffcc00;
            margin-bottom: 30px;
        }
        h1, h2 {
            color: #ffcc00;
            text-align: center;
            margin-bottom: 25px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #ffcc00;
            font-weight: bold;
        }
        input[type="text"],
        input[type="url"],
        textarea {
            width: calc(100% - 22px);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #555;
            background-color: #333;
            color: #ffffff;
            font-size: 1rem;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        input[type="checkbox"] {
            margin-right: 8px;
            vertical-align: middle;
        }
        .checkbox-label {
            color: #ffffff;
            font-weight: normal;
            display: inline-block;
        }
        button {
            background: linear-gradient(to right, #ffcc00, #ff9800);
            color: #000000;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: opacity 0.3s ease;
            display: block;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        #statusMessage {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            display: none;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .error {
            background-color: #dc3545;
            color: white;
        }
        .notifications-list {
            margin-top: 30px;
        }
        .notification-item {
            background-color: #2a2b28;
            border: 1px solid #444;
            border-left: 5px solid #ff9800;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .notification-item h3 {
            color: #ffcc00;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .notification-item p {
            margin: 5px 0;
            font-size: 0.9rem;
            color: #e0e0e0;
        }
        .notification-item p strong {
            color: #ffcc00;
        }
        .notification-item .actions {
            margin-top: 10px;
        }
        .notification-item .actions button {
            font-size: 0.9rem;
            padding: 8px 12px;
            margin-right: 10px;
            width: auto;
            display: inline-block;
        }
        .notification-item .actions .toggle-active-btn.active {
             background: linear-gradient(to right, #4CAF50, #45a049);
             color: white;
        }
        .notification-item .actions .toggle-active-btn.inactive {
             background: linear-gradient(to right, #f44336, #d32f2f);
             color: white;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #ffcc00;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading-text {
            text-align: center;
            color: #ffcc00;
        }
        .delete-btn {
            background: linear-gradient(to right, #c0392b, #e74c3c) !important;
            color: white !important;
        }
        /* RTL support */
        [dir="rtl"] .notification-item {
            border-left: none;
            border-right: 5px solid #ff9800;
        }
        [dir="rtl"] .notification-item .actions button {
            margin-right: 0;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إدارة إشعارات التحديث</h1>

        <form id="updateNotificationForm">
            <div class="form-group">
                <label for="title">عنوان الإشعار:</label>
                <input type="text" id="title" name="title" value="تحديث متاح!" required>
            </div>

            <div class="form-group">
                <label for="description">وصف الإشعار:</label>
                <textarea id="description" name="description" required>إصدار جديد من التطبيق جاهز. قم بالتحديث الآن للحصول على أحدث الميزات والتحسينات.</textarea>
            </div>

            <div class="form-group">
                <label for="updateUrl">رابط التحديث:</label>
                <input type="url" id="updateUrl" name="update_url" value="https://example.com/update-link" required>
            </div>

            <div class="form-group">
                <label for="versionName">اسم الإصدار (مثال: 1.2.3):</label>
                <input type="text" id="versionName" name="version_name" placeholder="اختياري">
            </div>

            <div class="form-group">
                <input type="checkbox" id="showCancelButton" name="show_cancel_button" checked>
                <label for="showCancelButton" class="checkbox-label">تضمين زر "تخطي / إلغاء"</label>
            </div>

            <div class="form-group">
                <input type="checkbox" id="isActive" name="is_active" checked>
                <label for="isActive" class="checkbox-label">تعيين كإشعار نشط</label>
            </div>
            
            <input type="hidden" id="notificationId" name="notification_id">

            <button type="submit" id="saveBtn">حفظ الإشعار</button>
        </form>
        <div id="statusMessage"></div>
    </div>

    <div class="container notifications-list">
        <h2>الإشعارات الحالية</h2>
        <div id="notificationsContainer">
            <div class="loading-text">جاري تحميل الإشعارات...</div>
            <div class="loader"></div>
        </div>
    </div>

    <script>
        // Supabase Configuration
        // IMPORTANT: Replace 'notifications' if your Supabase table has a different name.
        const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
        const NOTIFICATIONS_TABLE = 'update_notifications'; // Adjust if your table name is different

        const supabaseHeaders = (method = 'GET') => {
            const headers = {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            };
            if (method !== 'GET' && method !== 'HEAD') { // Content-Type for POST, PATCH, PUT, DELETE
                headers['Content-Type'] = 'application/json';
            }
            // For Supabase, 'Prefer' header can be useful for POST, PATCH, DELETE
            if (method === 'POST' || method === 'PATCH') {
                headers['Prefer'] = 'return=representation'; // Get the full new/updated record back
            } else if (method === 'DELETE') {
                headers['Prefer'] = 'return=minimal'; // No content needed back for delete
            }
            return headers;
        };
        
        // عناصر DOM
        const form = document.getElementById('updateNotificationForm');
        const statusMessage = document.getElementById('statusMessage');
        const notificationsContainer = document.getElementById('notificationsContainer');
        const saveBtn = document.getElementById('saveBtn');
        const notificationIdField = document.getElementById('notificationId');

        // تحديد اتجاه الصفحة
        document.documentElement.setAttribute('dir', 'rtl'); // للعربية استخدم 'rtl', للإنجليزية 'ltr'

        /**
         * جلب قائمة الإشعارات من الخادم
         */
        async function fetchNotifications() {
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${NOTIFICATIONS_TABLE}?select=*&order=created_at.desc`, {
                    method: 'GET',
                    headers: supabaseHeaders('GET')
                });
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(`خطأ في الطلب! الحالة: ${response.status}. ${errorData.message || ''}`);
                }
                const notifications = await response.json();
                renderNotifications(notifications);
            } catch (error) {
                console.error('خطأ في جلب الإشعارات:', error);
                notificationsContainer.innerHTML = `<p style="color:red;">فشل في تحميل الإشعارات: ${error.message}. تأكد من صحة إعدادات Supabase واسم الجدول.</p>`;
            }
        }

        /**
         * عرض الإشعارات في واجهة المستخدم
         * @param {Array} notifications - قائمة بالإشعارات
         */
        function renderNotifications(notifications) {
            if (!notifications || notifications.length === 0) {
                notificationsContainer.innerHTML = '<p>لا توجد إشعارات.</p>';
                return;
            }
            
            notificationsContainer.innerHTML = ''; // مسح العناصر السابقة
            
            notifications.forEach(notification => {
                const item = document.createElement('div');
                item.classList.add('notification-item');
                
                // تنقية البيانات لمنع XSS
                const safeTitle = escapeHtml(notification.title);
                const safeVersionName = escapeHtml(notification.version_name || 'غير محدد');
                const safeId = escapeHtml(notification.id);
                const safeDescription = escapeHtml(notification.description).substring(0, 100) + '...';
                const safeUrl = escapeHtml(notification.update_url);
                
                const itemHtmlContent = `
                    <h3>${safeTitle} (${safeVersionName})</h3>
                    <p><strong>المعرف:</strong> ${safeId}</p>
                    <p><strong>الوصف:</strong> ${safeDescription}</p>
                    <p><strong>الرابط:</strong> <a href="${safeUrl}" target="_blank">${safeUrl}</a></p>
                    <p><strong>إظهار زر الإلغاء:</strong> ${notification.show_cancel_button ? 'نعم' : 'لا'}</p>
                    <p><strong>نشط:</strong> ${notification.is_active ? 'نعم' : 'لا'}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${new Date(notification.created_at).toLocaleString('ar-SA')}</p>
                    <div class="actions">
                        <button onclick="editNotification('${safeId}')">تعديل</button>
                        <button class="toggle-active-btn ${notification.is_active ? 'active' : 'inactive'}" 
                                onclick="toggleActiveStatus('${safeId}', ${!notification.is_active})">
                            ${notification.is_active ? 'تعطيل' : 'تفعيل'}
                        </button>
                        <button class="delete-btn" onclick="deleteNotification('${safeId}')">حذف</button>
                    </div>
                `;
                
                item.innerHTML = itemHtmlContent;
                notificationsContainer.appendChild(item);
            });
        }
        
        /**
         * تحميل تفاصيل إشعار للتعديل
         * @param {string} id - معرف الإشعار
         */
        window.editNotification = async function(id) {
            try {
                // إظهار حالة التحميل
                showStatus('جاري تحميل بيانات الإشعار...', false, true);
                
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${NOTIFICATIONS_TABLE}?id=eq.${encodeURIComponent(id)}&select=*`, {
                    method: 'GET',
                    headers: supabaseHeaders('GET')
                });
                if (!response.ok) {
                     const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(`فشل في جلب تفاصيل الإشعار. الحالة: ${response.status}. ${errorData.message || ''}`);
                }
                
                const notifArray = await response.json();
                if (!notifArray || notifArray.length === 0) {
                    throw new Error('الإشعار غير موجود.');
                }
                const notif = notifArray[0]; // Supabase returns an array

                // ملء النموذج بالبيانات
                document.getElementById('title').value = notif.title;
                document.getElementById('description').value = notif.description;
                document.getElementById('updateUrl').value = notif.update_url;
                document.getElementById('versionName').value = notif.version_name || '';
                document.getElementById('showCancelButton').checked = Boolean(notif.show_cancel_button);
                document.getElementById('isActive').checked = Boolean(notif.is_active);
                notificationIdField.value = notif.id; // تخزين المعرف للتحديث
                
                saveBtn.textContent = 'تحديث الإشعار';
                window.scrollTo(0, 0); // التمرير إلى أعلى لرؤية النموذج
                
                // إخفاء رسالة الحالة
                hideStatus();
            } catch (error) {
                console.error('خطأ في تحميل الإشعار للتعديل:', error);
                showStatus('خطأ في تحميل الإشعار: ' + error.message, true);
            }
        }

        /**
         * تبديل حالة تفعيل الإشعار
         * @param {string} id - معرف الإشعار
         * @param {boolean} newStatus - الحالة الجديدة
         */
        window.toggleActiveStatus = async function(id, newStatus) {
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${NOTIFICATIONS_TABLE}?id=eq.${encodeURIComponent(id)}`, {
                    method: 'PATCH',
                    headers: supabaseHeaders('PATCH'), 
                    body: JSON.stringify({ is_active: newStatus })
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(errorData.message || `فشل في تبديل الحالة. الحالة: ${response.status}`);
                }
                // const updatedNotif = await response.json(); // If Prefer: return=representation is set by helper

                const toggleStatusMsg = `تم ${newStatus ? 'تفعيل' : 'تعطيل'} الإشعار بنجاح.`;
                showStatus(toggleStatusMsg, false);
                fetchNotifications(); // تحديث القائمة
            } catch (error) {
                console.error('خطأ في تبديل حالة التفعيل:', error);
                showStatus('خطأ: ' + error.message, true);
            }
        }
        
        /**
         * حذف إشعار
         * @param {string} id - معرف الإشعار
         */
        window.deleteNotification = async function(id) {
            if (!confirm('هل أنت متأكد من رغبتك في حذف هذا الإشعار؟')) return;
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/${NOTIFICATIONS_TABLE}?id=eq.${encodeURIComponent(id)}`, {
                    method: 'DELETE',
                    headers: supabaseHeaders('DELETE')
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: response.statusText }));
                    throw new Error(errorData.message || `فشل في حذف الإشعار. الحالة: ${response.status}`);
                }
                
                showStatus('تم حذف الإشعار بنجاح.', false);
                fetchNotifications(); // تحديث القائمة
            } catch (error) {
                console.error('خطأ في حذف الإشعار:', error);
                showStatus('خطأ: ' + error.message, true);
            }
        }

        /**
         * معالجة إرسال النموذج
         */
        form.addEventListener('submit', async function(event) {
            event.preventDefault();
            hideStatus();

            // جمع بيانات النموذج
            const formData = new FormData(form);
            const data = {};
            
            // استخراج البيانات من النموذج
            for (const [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // التعامل مع مربعات الاختيار بشكل صحيح
            data['show_cancel_button'] = document.getElementById('showCancelButton').checked;
            data['is_active'] = document.getElementById('isActive').checked;
            
            const currentNotificationId = notificationIdField.value;
            let url, methodRequest; 
            let bodyPayload;

            const payload = { ...data };
            delete payload.notification_id; 
            delete payload.id;


            if (currentNotificationId) { 
                methodRequest = 'PATCH';
                url = `${SUPABASE_URL}/rest/v1/${NOTIFICATIONS_TABLE}?id=eq.${encodeURIComponent(currentNotificationId)}`;
                bodyPayload = JSON.stringify(payload);
            } else { 
                methodRequest = 'POST';
                url = `${SUPABASE_URL}/rest/v1/${NOTIFICATIONS_TABLE}`;
                bodyPayload = JSON.stringify(payload);
            }

            try {
                // إظهار حالة الإرسال
                showStatus('جاري حفظ البيانات...', false, true);
                
                const response = await fetch(url, {
                    method: methodRequest,
                    headers: supabaseHeaders(methodRequest), 
                    body: bodyPayload,
                });

                if (response.ok) {
                    const resultArray = await response.json(); 
                    if (!resultArray || resultArray.length === 0) {
                        throw new Error("لم يتم إرجاع أي بيانات من الخادم بعد الحفظ.");
                    }
                    const result = resultArray[0];
                    showStatus(currentNotificationId ? 'تم تحديث الإشعار بنجاح!' : 'تم حفظ الإشعار بنجاح! المعرف: ' + (result.id || 'غير معروف'), false);
                    
                    // إعادة تعيين النموذج
                    form.reset();
                    notificationIdField.value = ''; // مسح حقل المعرف
                    saveBtn.textContent = 'حفظ الإشعار';
                    
                    // تعبئة النموذج بالقيم الافتراضية
                    document.getElementById('title').value = 'تحديث متاح!';
                    document.getElementById('description').value = 'إصدار جديد من التطبيق جاهز. قم بالتحديث الآن للحصول على أحدث الميزات والتحسينات.';
                    document.getElementById('updateUrl').value = 'https://example.com/update-link';
                    document.getElementById('showCancelButton').checked = true;
                    document.getElementById('isActive').checked = true;
                    
                    fetchNotifications(); // تحديث القائمة
                } else {
                    const errorResult = await response.json();
                    showStatus('خطأ: ' + (errorResult.message || 'فشل في حفظ الإشعار.'), true);
                }
            } catch (error) {
                console.error('خطأ في الإرسال:', error);
                showStatus('خطأ في الشبكة أو عدم استجابة الخادم.', true);
            }
        });

        /**
         * عرض رسالة الحالة
         * @param {string} message - نص الرسالة
         * @param {boolean} isError - هل هي رسالة خطأ
         * @param {boolean} persistent - هل تبقى الرسالة مرئية
         */
        function showStatus(message, isError, persistent = false) {
            statusMessage.textContent = message;
            statusMessage.className = isError ? 'error' : 'success';
            statusMessage.style.display = 'block';
            
            if (!persistent) {
                setTimeout(() => {
                    hideStatus();
                }, 5000);
            }
        }
        
        /**
         * إخفاء رسالة الحالة
         */
        function hideStatus() {
            statusMessage.textContent = '';
            statusMessage.className = '';
            statusMessage.style.display = 'none';
        }
        
        /**
         * تنقية النص لمنع هجمات XSS
         * @param {string} unsafe - النص غير الآمن
         * @return {string} - النص المنقى
         */
        function escapeHtml(unsafe) {
            if (unsafe === null || unsafe === undefined) return '';
            return String(unsafe)
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // جلب الإشعارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', fetchNotifications);
    </script>
</body>
</html>
