-- Script para crear las tablas necesarias para la funcionalidad de categorías

-- Tabla de categorías
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    icon_class TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de relación entre categorías y mods
CREATE TABLE IF NOT EXISTS category_mods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    mod_id UUID NOT NULL REFERENCES mods(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(category_id, mod_id)
);

-- Crear políticas de seguridad para las tablas (RLS)
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE category_mods ENABLE ROW LEVEL SECURITY;

-- Políticas para la tabla categories
CREATE POLICY "Permitir lectura pública de categorías" 
    ON categories FOR SELECT 
    USING (true);

CREATE POLICY "Permitir inserción de categorías a usuarios autenticados" 
    ON categories FOR INSERT 
    TO authenticated 
    WITH CHECK (true);

CREATE POLICY "Permitir actualización de categorías a usuarios autenticados" 
    ON categories FOR UPDATE 
    TO authenticated 
    USING (true);

CREATE POLICY "Permitir eliminación de categorías a usuarios autenticados" 
    ON categories FOR DELETE 
    TO authenticated 
    USING (true);

-- Políticas para la tabla category_mods
CREATE POLICY "Permitir lectura pública de relaciones categoría-mod" 
    ON category_mods FOR SELECT 
    USING (true);

CREATE POLICY "Permitir inserción de relaciones categoría-mod a usuarios autenticados" 
    ON category_mods FOR INSERT 
    TO authenticated 
    WITH CHECK (true);

CREATE POLICY "Permitir actualización de relaciones categoría-mod a usuarios autenticados" 
    ON category_mods FOR UPDATE 
    TO authenticated 
    USING (true);

CREATE POLICY "Permitir eliminación de relaciones categoría-mod a usuarios autenticados" 
    ON category_mods FOR DELETE 
    TO authenticated 
    USING (true);

-- Insertar algunas categorías predeterminadas
INSERT INTO categories (name, icon_class) VALUES
    ('Addons', 'fas fa-puzzle-piece'),
    ('Texture', 'fas fa-image'),
    ('Shaders', 'fas fa-palette')
ON CONFLICT (name) DO NOTHING;

-- Crear un índice para mejorar el rendimiento de las consultas
CREATE INDEX IF NOT EXISTS idx_category_mods_category_id ON category_mods(category_id);
CREATE INDEX IF NOT EXISTS idx_category_mods_mod_id ON category_mods(mod_id);
