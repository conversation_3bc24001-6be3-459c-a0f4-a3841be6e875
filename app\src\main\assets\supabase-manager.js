// Centralized Supabase Client Manager
// Prevents multiple client instances and provides unified configuration

class SupabaseManager {
    constructor() {
        this.clients = new Map();
        this.initialized = false;
        this.config = {
            main: {
                url: 'https://ytqxxodyecdeosnqoure.supabase.co',
                key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4',
                options: {
                    auth: {
                        persistSession: false, // Prevent session conflicts
                        autoRefreshToken: false,
                        detectSessionInUrl: false
                    }
                }
            },
            storage: {
                url: 'https://mwxzwfeqsashcwvqthmd.supabase.co',
                key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im13eHp3ZmVxc2FzaGN3dnF0aG1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MzU2NDcsImV4cCI6MjA2MTQxMTY0N30.nU0smAgNsoLi1zRNKA3AFM3q112jp4fhPgYeeXqKmPU',
                options: {
                    auth: {
                        persistSession: false,
                        autoRefreshToken: false,
                        detectSessionInUrl: false
                    }
                }
            }
        };
    }

    // Get or create a client instance
    getClient(type = 'main') {
        if (!this.clients.has(type)) {
            const config = this.config[type];
            if (!config) {
                throw new Error(`Unknown client type: ${type}`);
            }

            console.log(`Creating new Supabase client for: ${type}`);
            const client = supabase.createClient(config.url, config.key, config.options);
            this.clients.set(type, client);
        }

        return this.clients.get(type);
    }

    // Get main database client
    getMainClient() {
        return this.getClient('main');
    }

    // Get storage client
    getStorageClient() {
        return this.getClient('storage');
    }

    // Initialize all required tables (optimized for speed)
    async initializeTables() {
        if (this.initialized) return;

        // Skip table initialization for admin pages to speed up loading
        const isAdminPage = window.location.pathname.includes('/admin/');
        if (isAdminPage) {
            console.log('⚡ Skipping table initialization for admin page (speed optimization)');
            this.initialized = true;
            return;
        }

        const client = this.getMainClient();

        try {
            console.log('🔄 Quick table check...');

            // Only check essential tables for the current page
            const essentialTables = this.getEssentialTablesForCurrentPage();

            for (const tableName of essentialTables) {
                await this.quickTableCheck(tableName);
            }

            this.initialized = true;
            console.log('✅ Essential tables checked successfully');

        } catch (error) {
            console.error('❌ Error in quick table check:', error);
            // Don't throw error, just log it to prevent app from breaking
            this.initialized = true; // Mark as initialized to prevent retry loops
        }
    }

    // Get essential tables based on current page
    getEssentialTablesForCurrentPage() {
        const path = window.location.pathname;

        if (path.includes('custom_copyright')) {
            return ['custom_copyright_mods'];
        } else if (path.includes('custom_dialogs')) {
            return ['custom_mod_dialogs', 'custom_dialog_mods'];
        } else if (path.includes('admin')) {
            return []; // No table checks for admin pages
        } else {
            return ['user_languages']; // Only essential for main app
        }
    }

    // Quick table check without creation attempts
    async quickTableCheck(tableName) {
        const client = this.getMainClient();

        try {
            // Simple existence check
            const { error } = await client
                .from(tableName)
                .select('id')
                .limit(1);

            if (error && (error.code === 'PGRST116' || error.message.includes('does not exist'))) {
                console.warn(`⚠️ Table ${tableName} does not exist - please create it manually`);
            } else if (error) {
                console.warn(`⚠️ Table ${tableName} check failed:`, error.message);
            } else {
                console.log(`✅ Table ${tableName} exists`);
            }
        } catch (error) {
            console.warn(`⚠️ Quick check failed for ${tableName}:`, error.message);
        }
    }

    // Check if table exists and create if needed
    async ensureTableExists(tableName, createSQL) {
        const client = this.getMainClient();

        try {
            // Try to query the table
            const { error } = await client
                .from(tableName)
                .select('id')
                .limit(1);

            if (error && (error.code === 'PGRST116' || error.message.includes('does not exist'))) {
                console.log(`Table ${tableName} does not exist`);

                // Try to create the table using RPC if available
                try {
                    const { error: createError } = await client.rpc('execute_sql', {
                        sql_query: createSQL
                    });

                    if (createError) {
                        console.warn(`Could not create table ${tableName} via RPC:`, createError.message);
                        console.warn(`Please create the table manually in Supabase SQL Editor using the SQL from database/missing_tables.sql`);

                        // Store missing table info for later reference
                        this.storeMissingTableInfo(tableName, createSQL);
                    } else {
                        console.log(`Table ${tableName} created successfully`);
                    }
                } catch (rpcError) {
                    console.warn(`RPC function 'execute_sql' not available. Table ${tableName} needs to be created manually.`);
                    console.warn(`Please execute the SQL from database/missing_tables.sql in Supabase SQL Editor`);

                    // Store missing table info for later reference
                    this.storeMissingTableInfo(tableName, createSQL);
                }
            } else if (error) {
                console.warn(`Error checking table ${tableName}:`, error.message);
            } else {
                console.log(`Table ${tableName} exists and is accessible`);
            }
        } catch (error) {
            console.warn(`Unexpected error checking table ${tableName}:`, error.message);
        }
    }

    // Store information about missing tables
    storeMissingTableInfo(tableName, createSQL) {
        if (!this.missingTables) {
            this.missingTables = new Map();
        }
        this.missingTables.set(tableName, createSQL);

        // Show user-friendly message
        this.showMissingTableNotification(tableName);
    }

    // Show notification about missing tables
    showMissingTableNotification(tableName) {
        console.group(`🔧 Table Setup Required`);
        console.warn(`Table '${tableName}' is missing from your Supabase database.`);
        console.info(`To fix this issue:`);
        console.info(`1. Open Supabase Dashboard → SQL Editor`);
        console.info(`2. Execute the SQL from: database/missing_tables.sql`);
        console.info(`3. Refresh the application`);
        console.groupEnd();
    }

    // Get list of missing tables
    getMissingTables() {
        return this.missingTables || new Map();
    }

    // Enhanced query with error handling
    async safeQuery(tableName, queryBuilder, fallbackData = null) {
        try {
            const result = await queryBuilder;
            return result;
        } catch (error) {
            console.error(`Error querying ${tableName}:`, error);

            // Check if it's a table not found error
            if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
                console.log(`Table ${tableName} not found, attempting to initialize...`);
                await this.initializeTables();

                // Retry the query once
                try {
                    const retryResult = await queryBuilder;
                    return retryResult;
                } catch (retryError) {
                    console.error(`Retry failed for ${tableName}:`, retryError);
                    return { data: fallbackData, error: retryError };
                }
            }

            return { data: fallbackData, error };
        }
    }

    // Get table names constants
    getTableNames() {
        return {
            MODS: 'mods',
            SUGGESTED_MODS: 'suggested_mods',
            FEATURED_ADDONS: 'featured_addons',
            FREE_ADDONS: 'free_addons',
            BANNER_ADS: 'banner_ads',
            UPDATE_NOTIFICATIONS: 'update_notifications',
            APP_ANNOUNCEMENTS: 'app_announcements',
            DRAWER_LINKS: 'drawer_links',
            USER_LANGUAGES: 'user_languages',
            CUSTOM_MOD_DIALOGS: 'custom_mod_dialogs',
            CUSTOM_DIALOG_MODS: 'custom_dialog_mods',
            CUSTOM_COPYRIGHT_MODS: 'custom_copyright_mods'
        };
    }

    // Cleanup method
    cleanup() {
        this.clients.clear();
        this.initialized = false;
    }
}

// Create global instance
let supabaseManager;

// Initialize when DOM is loaded (optimized)
document.addEventListener('DOMContentLoaded', function() {
    if (!supabaseManager) {
        supabaseManager = new SupabaseManager();
        console.log('⚡ Supabase Manager initialized (fast mode)');

        // Skip table initialization for faster loading
        // Tables will be checked only when needed
        setTimeout(() => {
            supabaseManager.initializeTables().catch(error => {
                console.warn('Background table check failed:', error);
            });
        }, 100); // Delay to not block page loading
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupabaseManager;
}

// Make available globally
window.SupabaseManager = SupabaseManager;
