// Enhanced Network Connectivity Handler
// Handles proxy connection failures and network issues

class NetworkHandler {
    constructor() {
        this.isOnline = navigator.onLine;
        this.retryAttempts = 0;
        this.maxRetries = 3;
        this.retryDelay = 2000; // 2 seconds
        this.networkStatusIndicator = null;
        this.offlineBanner = null;
        this.init();
    }

    init() {
        this.createNetworkStatusIndicator();
        this.setupEventListeners();
        this.checkInitialConnectivity();
    }

    createNetworkStatusIndicator() {
        // Create network status indicator
        this.networkStatusIndicator = document.createElement('div');
        this.networkStatusIndicator.className = 'network-status-indicator';
        this.networkStatusIndicator.id = 'network-status-indicator';
        document.body.appendChild(this.networkStatusIndicator);

        // Create offline banner
        this.offlineBanner = document.createElement('div');
        this.offlineBanner.className = 'offline-banner';
        this.offlineBanner.id = 'offline-banner';
        this.offlineBanner.style.display = 'none';
        this.offlineBanner.innerHTML = `
            <span>🔌 وضع عدم الاتصال - يتم عرض المحتوى المحفوظ</span>
            <button class="retry-btn" onclick="networkHandler.retryConnection()">إعادة المحاولة</button>
        `;
        document.body.appendChild(this.offlineBanner);
    }

    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            console.log('Network: Back online');
            this.isOnline = true;
            this.hideOfflineMode();
            this.showNetworkStatus('تم استعادة الاتصال بالإنترنت', 'success', 3000);
            this.retryFailedRequests();
        });

        window.addEventListener('offline', () => {
            console.log('Network: Gone offline');
            this.isOnline = false;
            this.showOfflineMode();
            this.showNetworkStatus('لا يوجد اتصال بالإنترنت', 'error', 5000);
        });
    }

    async checkInitialConnectivity() {
        const isConnected = await this.checkNetworkConnectivity();
        if (!isConnected) {
            this.isOnline = false;
            this.showOfflineMode();
        }
    }

    async checkNetworkConnectivity() {
        const testUrls = [
            'https://www.google.com/favicon.ico',
            'https://httpbin.org/status/200',
            'https://jsonplaceholder.typicode.com/posts/1'
        ];

        for (const url of testUrls) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

                const response = await fetch(url, {
                    method: 'HEAD',
                    mode: 'no-cors',
                    cache: 'no-cache',
                    signal: controller.signal
                });

                clearTimeout(timeoutId);
                console.log(`Network check successful with ${url}`);
                return true;
            } catch (error) {
                console.warn(`Network check failed with ${url}:`, error.message);
                continue;
            }
        }

        console.error('All network connectivity checks failed');
        return false;
    }

    showNetworkStatus(message, type = 'error', duration = 5000) {
        if (!this.networkStatusIndicator) return;

        this.networkStatusIndicator.textContent = message;
        this.networkStatusIndicator.className = `network-status-indicator ${type}`;
        this.networkStatusIndicator.style.display = 'block';

        // Auto-hide after duration
        setTimeout(() => {
            this.hideNetworkStatus();
        }, duration);
    }

    hideNetworkStatus() {
        if (this.networkStatusIndicator) {
            this.networkStatusIndicator.style.display = 'none';
        }
    }

    showOfflineMode() {
        document.body.classList.add('offline-mode');
        if (this.offlineBanner) {
            this.offlineBanner.style.display = 'block';
            // Adjust body padding to account for offline banner
            document.body.style.paddingTop = '90px'; // 45px top bar + 45px offline banner
        }
    }

    hideOfflineMode() {
        document.body.classList.remove('offline-mode');
        if (this.offlineBanner) {
            this.offlineBanner.style.display = 'none';
            // Reset body padding
            document.body.style.paddingTop = '55px'; // Back to normal
        }
    }

    async retryConnection() {
        this.showNetworkStatus('جاري التحقق من الاتصال...', 'warning', 2000);
        
        const isConnected = await this.checkNetworkConnectivity();
        if (isConnected) {
            this.isOnline = true;
            this.hideOfflineMode();
            this.showNetworkStatus('تم استعادة الاتصال بنجاح!', 'success', 3000);
            this.retryFailedRequests();
        } else {
            this.showNetworkStatus('لا يزال الاتصال غير متاح', 'error', 3000);
        }
    }

    async retryFailedRequests() {
        // Retry failed Supabase requests
        console.log('Retrying failed requests...');
        
        // Trigger a refresh of the main content
        if (typeof displayModsBySection === 'function') {
            try {
                await displayModsBySection();
                console.log('Successfully refreshed content after reconnection');
            } catch (error) {
                console.error('Failed to refresh content after reconnection:', error);
            }
        }

        // Retry other failed requests
        if (typeof fetchAndDisplayUpdateNotification === 'function') {
            fetchAndDisplayUpdateNotification();
        }

        if (typeof fetchAndDisplayAppAnnouncements === 'function') {
            fetchAndDisplayAppAnnouncements();
        }

        if (typeof loadDrawerLinks === 'function') {
            loadDrawerLinks();
        }
    }

    // Enhanced fetch wrapper with retry logic
    async fetchWithRetry(url, options = {}, maxRetries = this.maxRetries) {
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // Check if we're online before attempting
                if (!this.isOnline && attempt === 0) {
                    throw new Error('Device is offline');
                }

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // Reset retry attempts on success
                this.retryAttempts = 0;
                return response;

            } catch (error) {
                lastError = error;
                console.warn(`Fetch attempt ${attempt + 1} failed:`, error.message);

                // Check if it's a network-related error
                if (this.isNetworkError(error)) {
                    this.isOnline = false;
                    this.showOfflineMode();
                }

                // Don't retry on the last attempt
                if (attempt < maxRetries) {
                    const delay = this.retryDelay * Math.pow(2, attempt); // Exponential backoff
                    console.log(`Retrying in ${delay}ms...`);
                    await this.sleep(delay);
                }
            }
        }

        throw lastError;
    }

    isNetworkError(error) {
        const networkErrorMessages = [
            'Failed to fetch',
            'ERR_PROXY_CONNECTION_FAILED',
            'ERR_NETWORK',
            'ERR_INTERNET_DISCONNECTED',
            'NetworkError',
            'TypeError: Failed to fetch',
            'AbortError'
        ];

        return networkErrorMessages.some(msg => 
            error.message.includes(msg) || error.name.includes(msg)
        );
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Method to wrap Supabase calls
    async wrapSupabaseCall(supabaseCall, fallbackData = null) {
        try {
            const result = await supabaseCall();
            return result;
        } catch (error) {
            console.error('Supabase call failed:', error);
            
            if (this.isNetworkError(error)) {
                this.showNetworkStatus('مشكلة في الاتصال بالخادم', 'error', 5000);
                
                // Return cached data if available
                if (fallbackData) {
                    console.log('Using fallback/cached data');
                    return { data: fallbackData, error: null };
                }
            }
            
            throw error;
        }
    }
}

// Initialize network handler when DOM is loaded
let networkHandler;
document.addEventListener('DOMContentLoaded', function() {
    networkHandler = new NetworkHandler();
    console.log('Network handler initialized');
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NetworkHandler;
}
