<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Mods - Mod Etaris</title>
    <link rel="stylesheet" href="style.css">
    <!-- Add Font Awesome if needed for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="search-page"> <!-- Added class for page-specific styling -->
    <div class="top-fixed-bar">
        <!-- Content for the bar, if any, can be added here later if needed -->
    </div>
    <!-- Drawer and overlay removed -->

    <header class="header">
         <img src="image/tape.png" alt="" class="header-image"> <!-- Added background image -->
        <button class="back-btn" onclick="window.history.back()">
            <i class="fas fa-arrow-left"></i> <!-- Use Font Awesome icon -->
        </button>
        <h1>Mod Etaris</h1>
        <button class="search-btn" style="visibility: hidden;"> <!-- Hide search button on search page -->
             <img src="image/search.png" alt="Search">
        </button>
        <!-- Categories might not be needed on search page, or could be adapted -->
        <div id="categories">
            <!-- Category buttons can be loaded dynamically or removed -->
        </div>
    </header>

    <main>
        <div class="search-container">
             <input type="text" id="searchInput" class="search-input" placeholder="Search for mods...">
        </div>

        <!-- Container for search results, using the same vertical layout -->
        <div id="sectionsContainer" class="vertical-layout">
            <!-- Search results will be loaded here by JavaScript -->
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="loading-indicator" style="display: none;">
            <div class="loading-spinner"></div>
            Loading...
        </div>
    </main>

    <!-- Modal for item details - Reverted Structure -->
    <div class="modal" id="modal"> <!-- Reverted ID -->
        <div class="modal-content">
             <span class="close-btn" onclick="closeModal()"><img src="image/close_icon.png" alt="Close" style="width: 20px; height: 20px; vertical-align: middle;"></span>
             <div id="modalContent">
                 <!-- Content will be injected here by script.js -->
             </div>
        </div>
         <!-- Download bar might be part of injected content or needs separate handling -->
     </div>

    <!-- Load Supabase library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Load supabase manager first -->
    <script src="supabase-manager.js"></script>

    <!-- Load translations -->
    <script src="translations.js"></script>

    <!-- Load network handler -->
    <script src="network-handler.js"></script>

    <!-- Load script.js FIRST because search.js depends on functions within it -->
    <script src="script.js"></script>
    <script src="search.js"></script> <!-- Load search logic AFTER dependencies -->

    <!-- Removed Bottom Fixed Bar -->
</body>
</html>
