<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص حالة الجداول - Table Status Checker</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .table-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .table-card.exists {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .table-card.missing {
            border-color: #f44336;
            background: #fff8f8;
        }
        
        .table-card.checking {
            border-color: #ff9800;
            background: #fffaf0;
        }
        
        .table-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .table-status {
            padding: 5px 10px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .status-exists {
            background: #4CAF50;
        }
        
        .status-missing {
            background: #f44336;
        }
        
        .status-checking {
            background: #ff9800;
        }
        
        .table-description {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        
        .table-columns {
            font-size: 0.8em;
            color: #888;
        }
        
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .summary {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .summary h3 {
            margin-top: 0;
            color: #333;
        }
        
        .error-log {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .error-log h4 {
            color: #e53e3e;
            margin-top: 0;
        }
        
        .error-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #e53e3e;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص حالة جداول Supabase</h1>
        
        <div class="controls">
            <button class="btn" onclick="checkAllTables()">🔄 فحص جميع الجداول</button>
            <button class="btn" onclick="exportReport()">📊 تصدير التقرير</button>
        </div>
        
        <div id="summary" class="summary" style="display: none;">
            <h3>📋 ملخص الحالة</h3>
            <div id="summaryContent"></div>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>جاري فحص الجداول...</p>
        </div>
        
        <div id="statusGrid" class="status-grid"></div>
        
        <div id="errorLog" class="error-log" style="display: none;">
            <h4>🚨 سجل الأخطاء</h4>
            <div id="errorContent"></div>
        </div>
    </div>

    <script>
        // Supabase Configuration
        const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
        const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // Tables to check
        const TABLES_TO_CHECK = [
            {
                name: 'mods',
                description: 'الجدول الرئيسي للمودات',
                columns: 'id, title, description, description_ar, image_url, download_url, category, creator_name'
            },
            {
                name: 'suggested_mods',
                description: 'المودات المقترحة',
                columns: 'id, mod_id, display_order, is_required, is_active'
            },
            {
                name: 'user_languages',
                description: 'لغات المستخدمين',
                columns: 'id, user_id, selected_language, created_at'
            },
            {
                name: 'featured_addons',
                description: 'الإضافات المميزة',
                columns: 'id, mod_id, display_order, is_active'
            },
            {
                name: 'free_addons',
                description: 'الإضافات المجانية',
                columns: 'id, mod_id, display_order, is_active'
            },
            {
                name: 'banner_ads',
                description: 'إعلانات البانر',
                columns: 'id, title, description, image_url, target_url, is_active'
            },
            {
                name: 'custom_mod_dialogs',
                description: 'النوافذ المنبثقة المخصصة للمودات',
                columns: 'id, title, description, image_url, button_text, is_active'
            },
            {
                name: 'custom_dialog_mods',
                description: 'ربط النوافذ المنبثقة بالمودات',
                columns: 'id, dialog_id, mod_id, created_at'
            },
            {
                name: 'custom_copyright_mods',
                description: 'المودات ذات حقوق الطبع والنشر المخصصة',
                columns: 'id, mod_id, is_active, created_at'
            }
        ];

        let checkResults = {};
        let errors = [];

        async function checkAllTables() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('statusGrid').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
            document.getElementById('errorLog').style.display = 'none';
            
            checkResults = {};
            errors = [];

            for (const table of TABLES_TO_CHECK) {
                await checkTable(table);
            }

            document.getElementById('loading').style.display = 'none';
            updateSummary();
            
            if (errors.length > 0) {
                showErrors();
            }
        }

        async function checkTable(table) {
            const card = createTableCard(table, 'checking');
            document.getElementById('statusGrid').appendChild(card);

            try {
                const { data, error } = await supabaseClient
                    .from(table.name)
                    .select('*')
                    .limit(1);

                if (error) {
                    if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
                        checkResults[table.name] = 'missing';
                        errors.push({
                            table: table.name,
                            error: 'Table does not exist',
                            solution: 'Execute database/missing_tables.sql in Supabase SQL Editor'
                        });
                    } else {
                        checkResults[table.name] = 'error';
                        errors.push({
                            table: table.name,
                            error: error.message,
                            solution: 'Check table permissions and structure'
                        });
                    }
                } else {
                    checkResults[table.name] = 'exists';
                }
            } catch (err) {
                checkResults[table.name] = 'error';
                errors.push({
                    table: table.name,
                    error: err.message,
                    solution: 'Check network connection and Supabase configuration'
                });
            }

            // Update card
            card.remove();
            const updatedCard = createTableCard(table, checkResults[table.name]);
            document.getElementById('statusGrid').appendChild(updatedCard);
        }

        function createTableCard(table, status) {
            const card = document.createElement('div');
            card.className = `table-card ${status}`;
            
            const statusText = {
                'checking': 'جاري الفحص...',
                'exists': '✅ موجود',
                'missing': '❌ مفقود',
                'error': '⚠️ خطأ'
            };

            const statusClass = {
                'checking': 'status-checking',
                'exists': 'status-exists',
                'missing': 'status-missing',
                'error': 'status-missing'
            };

            card.innerHTML = `
                <div class="table-name">${table.name}</div>
                <div class="table-status ${statusClass[status]}">${statusText[status]}</div>
                <div class="table-description">${table.description}</div>
                <div class="table-columns"><strong>الأعمدة:</strong> ${table.columns}</div>
            `;

            return card;
        }

        function updateSummary() {
            const total = TABLES_TO_CHECK.length;
            const existing = Object.values(checkResults).filter(r => r === 'exists').length;
            const missing = Object.values(checkResults).filter(r => r === 'missing').length;
            const errorCount = Object.values(checkResults).filter(r => r === 'error').length;

            document.getElementById('summaryContent').innerHTML = `
                <p><strong>إجمالي الجداول:</strong> ${total}</p>
                <p><strong>الجداول الموجودة:</strong> ${existing} ✅</p>
                <p><strong>الجداول المفقودة:</strong> ${missing} ❌</p>
                <p><strong>جداول بها أخطاء:</strong> ${errorCount} ⚠️</p>
                <p><strong>نسبة الاكتمال:</strong> ${Math.round((existing / total) * 100)}%</p>
            `;

            document.getElementById('summary').style.display = 'block';
        }

        function showErrors() {
            const errorContent = document.getElementById('errorContent');
            errorContent.innerHTML = '';

            errors.forEach(error => {
                const errorItem = document.createElement('div');
                errorItem.className = 'error-item';
                errorItem.innerHTML = `
                    <strong>الجدول:</strong> ${error.table}<br>
                    <strong>الخطأ:</strong> ${error.error}<br>
                    <strong>الحل:</strong> ${error.solution}
                `;
                errorContent.appendChild(errorItem);
            });

            document.getElementById('errorLog').style.display = 'block';
        }

        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: TABLES_TO_CHECK.length,
                    existing: Object.values(checkResults).filter(r => r === 'exists').length,
                    missing: Object.values(checkResults).filter(r => r === 'missing').length,
                    errors: Object.values(checkResults).filter(r => r === 'error').length
                },
                tables: TABLES_TO_CHECK.map(table => ({
                    name: table.name,
                    description: table.description,
                    status: checkResults[table.name] || 'not_checked'
                })),
                errors: errors
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `supabase_tables_report_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Auto-check on page load
        window.addEventListener('load', () => {
            setTimeout(checkAllTables, 1000);
        });
    </script>
</body>
</html>
