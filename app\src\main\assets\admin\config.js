// Supabase Configuration
const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';

// Initialize Supabase client
const supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);

// Table names
const BANNER_ADS_TABLE = 'banner_ads';
const FEATURED_ADDONS_TABLE = 'featured_addons';
const MODS_TABLE = 'mods';

// Cache duration
const CACHE_DURATION_MS = 60 * 60 * 1000; // 1 hour

// Helper function to generate a unique user ID
function generateUserId() {
    let userId = localStorage.getItem('userId');
    if (!userId) {
        userId = `user_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        localStorage.setItem('userId', userId);
    }
    return userId;
}

// Helper function to format large numbers
function formatCount(count) {
    const num = Number(count);
    if (isNaN(num)) return '0';

    if (num >= 1_000_000) return (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + "M";
    if (num >= 1_000) return (num / 1_000).toFixed(1).replace(/\.0$/, '') + "K";
    return num.toString();
}
