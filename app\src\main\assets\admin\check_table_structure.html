<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Table Structure</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        pre {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Check Supabase Table Structure</h1>
    <button id="checkButton">Check Table Structure</button>
    <div id="result"></div>

    <script>
        document.getElementById('checkButton').addEventListener('click', checkTableStructure);

        async function checkTableStructure() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Checking table structure...</p>';

            try {
                // Supabase Configuration
                const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
                const sbClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

                // Try to get a single row to see the structure
                const { data, error } = await sbClient
                    .from('update_notifications')
                    .select('*')
                    .limit(1);

                if (error) {
                    resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
                    return;
                }

                // Get table definition
                const { data: tableInfo, error: tableError } = await sbClient.rpc('get_table_definition', {
                    table_name: 'update_notifications'
                });

                let output = '';

                if (data && data.length > 0) {
                    output += '<h2>Sample Row:</h2>';
                    output += `<pre>${JSON.stringify(data[0], null, 2)}</pre>`;
                    
                    output += '<h2>Column Names:</h2>';
                    output += '<ul>';
                    for (const key in data[0]) {
                        output += `<li>${key}: ${typeof data[0][key]}</li>`;
                    }
                    output += '</ul>';
                } else {
                    output += '<p>No data found in the table.</p>';
                }

                if (tableInfo) {
                    output += '<h2>Table Definition:</h2>';
                    output += `<pre>${JSON.stringify(tableInfo, null, 2)}</pre>`;
                } else if (tableError) {
                    output += `<p>Error getting table definition: ${tableError.message}</p>`;
                }

                resultDiv.innerHTML = output;
            } catch (error) {
                resultDiv.innerHTML = `<p>Unexpected error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
