// إدارة المربعات المخصصة
let supabaseClient;
let currentEditingId = null;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 بدء تهيئة تطبيق إدارة المربعات المخصصة...');

    try {
        console.log('⏳ انتظار تحميل Supabase Manager...');
        // انتظار تحميل Supabase Manager
        await waitForSupabaseManager();
        console.log('✅ تم تحميل Supabase Manager');

        supabaseClient = supabaseManager.getMainClient();
        console.log('🔗 تم الحصول على عميل Supabase:', supabaseClient);

        // إعداد نموذج الحفظ
        console.log('📝 إعداد نموذج الحفظ...');
        setupForm();

        // تحميل المربعات الموجودة
        console.log('📦 بدء تحميل المربعات الموجودة...');
        await loadDialogs();

        console.log('🎉 تم تهيئة التطبيق بنجاح!');

    } catch (error) {
        console.error('💥 خطأ في تهيئة التطبيق:', error);
        showMessage('خطأ في تحميل التطبيق: ' + error.message, 'error');

        // عرض رسالة خطأ في قائمة المربعات أيضاً
        document.getElementById('dialogsList').innerHTML =
            '<div class="error-message">خطأ في تهيئة التطبيق: ' + error.message + '</div>';
    }
});

// إنشاء Supabase Manager مبسط إذا لم يكن موجوداً
function createFallbackSupabaseManager() {
    console.log('🔧 إنشاء Supabase Manager احتياطي...');

    const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';

    window.supabaseManager = {
        getMainClient() {
            if (!this.client) {
                console.log('🔗 إنشاء عميل Supabase جديد...');
                this.client = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
                    auth: {
                        persistSession: false,
                        autoRefreshToken: false,
                        detectSessionInUrl: false
                    }
                });
            }
            return this.client;
        }
    };

    console.log('✅ تم إنشاء Supabase Manager احتياطي');
}

// انتظار تحميل Supabase Manager
function waitForSupabaseManager() {
    return new Promise((resolve, reject) => {
        console.log('🔍 فحص وجود Supabase Manager...');

        if (window.supabaseManager) {
            console.log('✅ Supabase Manager موجود مسبقاً');
            resolve();
            return;
        }

        console.log('⏳ انتظار تحميل Supabase Manager...');
        let attempts = 0;
        const maxAttempts = 30; // 3 ثوان

        const checkInterval = setInterval(() => {
            attempts++;
            console.log(`🔄 محاولة ${attempts}/${maxAttempts} للعثور على Supabase Manager...`);

            if (window.supabaseManager) {
                console.log('✅ تم العثور على Supabase Manager!');
                clearInterval(checkInterval);
                resolve();
            } else if (attempts >= maxAttempts) {
                console.warn('⚠️ لم يتم العثور على Supabase Manager، سيتم إنشاء نسخة احتياطية');
                clearInterval(checkInterval);

                // إنشاء نسخة احتياطية
                try {
                    createFallbackSupabaseManager();
                    resolve();
                } catch (error) {
                    console.error('❌ فشل في إنشاء Supabase Manager احتياطي:', error);
                    reject(new Error('فشل في تحميل أو إنشاء Supabase Manager: ' + error.message));
                }
            }
        }, 100);
    });
}

// إعداد نموذج الحفظ
function setupForm() {
    const form = document.getElementById('dialogForm');
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        await saveDialog();
    });
}

// حفظ المربع
async function saveDialog() {
    try {
        const formData = getFormData();

        if (!formData.title.trim() && !formData.titleEn.trim()) {
            showMessage('يرجى إدخال عنوان المربع بإحدى اللغتين على الأقل', 'error');
            return;
        }

        let result;
        if (currentEditingId) {
            // تحديث مربع موجود
            result = await supabaseClient
                .from('custom_mod_dialogs')
                .update({
                    title: formData.title,
                    title_en: formData.titleEn,
                    description: formData.description,
                    description_en: formData.descriptionEn,
                    image_url: formData.imageUrl,
                    button_text: formData.buttonText,
                    button_text_en: formData.buttonTextEn,
                    show_dont_show_again: formData.showDontShowAgain,
                    is_active: formData.isActive,
                    updated_at: new Date().toISOString()
                })
                .eq('id', currentEditingId);
        } else {
            // إنشاء مربع جديد
            result = await supabaseClient
                .from('custom_mod_dialogs')
                .insert([{
                    title: formData.title,
                    title_en: formData.titleEn,
                    description: formData.description,
                    description_en: formData.descriptionEn,
                    image_url: formData.imageUrl,
                    button_text: formData.buttonText,
                    button_text_en: formData.buttonTextEn,
                    show_dont_show_again: formData.showDontShowAgain,
                    is_active: formData.isActive
                }]);
        }

        if (result.error) {
            // التحقق من وجود الجدول
            if (result.error.code === 'PGRST116' || result.error.message.includes('does not exist')) {
                showMessage('جدول المربعات المخصصة غير موجود. يرجى إنشاء الجداول المطلوبة أولاً.', 'error');
                return;
            }
            throw result.error;
        }

        showMessage(currentEditingId ? 'تم تحديث المربع بنجاح' : 'تم إنشاء المربع بنجاح', 'success');
        resetForm();
        await loadDialogs();

    } catch (error) {
        console.error('خطأ في حفظ المربع:', error);
        showMessage('خطأ في حفظ المربع: ' + error.message, 'error');
    }
}

// الحصول على بيانات النموذج
function getFormData() {
    return {
        title: document.getElementById('dialogTitle').value.trim(),
        titleEn: document.getElementById('dialogTitleEn').value.trim(),
        description: document.getElementById('dialogDescription').value.trim(),
        descriptionEn: document.getElementById('dialogDescriptionEn').value.trim(),
        imageUrl: document.getElementById('dialogImage').value.trim(),
        buttonText: document.getElementById('buttonText').value.trim() || 'تم',
        buttonTextEn: document.getElementById('buttonTextEn').value.trim() || 'OK',
        showDontShowAgain: document.getElementById('showDontShowAgain').checked,
        isActive: document.getElementById('isActive').checked
    };
}

// تحميل المربعات الموجودة
async function loadDialogs() {
    console.log('🔄 بدء تحميل المربعات...');

    try {
        console.log('📡 إرسال طلب إلى Supabase...');
        const { data, error } = await supabaseClient
            .from('custom_mod_dialogs')
            .select('*')
            .order('created_at', { ascending: false });

        console.log('📊 استجابة Supabase:', { data, error });

        if (error) {
            console.error('❌ خطأ من Supabase:', error);
            // التحقق من وجود الجدول
            if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
                document.getElementById('dialogsList').innerHTML = `
                    <div class="error-message">
                        <h3>⚠️ جدول المربعات المخصصة غير موجود</h3>
                        <p>يرجى إنشاء الجداول المطلوبة أولاً:</p>
                        <ol>
                            <li>افتح Supabase Dashboard → SQL Editor</li>
                            <li>نفذ الكود التالي:</li>
                        </ol>
                        <textarea readonly style="width: 100%; height: 200px; margin: 10px 0; font-family: monospace; font-size: 12px;">
CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100) DEFAULT 'تم',
    show_dont_show_again BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
                        </textarea>
                        <button onclick="location.reload()" class="btn btn-primary">إعادة تحميل الصفحة بعد إنشاء الجداول</button>
                    </div>
                `;
                return;
            }
            throw error;
        }

        console.log('✅ تم تحميل البيانات بنجاح:', data);
        displayDialogs(data || []);

    } catch (error) {
        console.error('💥 خطأ غير متوقع في تحميل المربعات:', error);
        document.getElementById('dialogsList').innerHTML =
            '<div class="error-message">خطأ في تحميل المربعات: ' + error.message + '</div>';
    }
}

// عرض المربعات
function displayDialogs(dialogs) {
    const container = document.getElementById('dialogsList');

    if (dialogs.length === 0) {
        container.innerHTML = '<p style="color: #888; text-align: center;">لا توجد مربعات محفوظة</p>';
        return;
    }

    const dialogsHTML = dialogs.map(dialog => `
        <div class="dialog-item">
            <div class="dialog-titles">
                <h3>🇸🇦 ${escapeHtml(dialog.title || 'غير محدد')}</h3>
                ${dialog.title_en ? `<h4>🇺🇸 ${escapeHtml(dialog.title_en)}</h4>` : ''}
            </div>

            ${dialog.description || dialog.description_en ? `
                <div class="dialog-descriptions">
                    ${dialog.description ? `<p class="description-ar">🇸🇦 ${escapeHtml(dialog.description)}</p>` : ''}
                    ${dialog.description_en ? `<p class="description-en">🇺🇸 ${escapeHtml(dialog.description_en)}</p>` : ''}
                </div>
            ` : ''}

            <div class="dialog-meta">
                <span class="status-indicator ${dialog.is_active ? 'status-active' : 'status-inactive'}">
                    ${dialog.is_active ? 'مفعل' : 'غير مفعل'}
                </span>
                <div class="button-texts">
                    <span>🇸🇦 نص الزر: "${escapeHtml(dialog.button_text || 'تم')}"</span>
                    ${dialog.button_text_en ? `<span>🇺🇸 Button: "${escapeHtml(dialog.button_text_en)}"</span>` : ''}
                </div>
                • عدم الظهور مجدداً: ${dialog.show_dont_show_again ? 'نعم' : 'لا'}
                <br>
                تاريخ الإنشاء: ${formatDate(dialog.created_at)}
            </div>
            <div class="dialog-actions">
                <button class="btn btn-primary" onclick="editDialog(${dialog.id})">تعديل</button>
                <button class="btn btn-success" onclick="previewDialogById(${dialog.id}, 'ar')">معاينة عربي</button>
                <button class="btn btn-success" onclick="previewDialogById(${dialog.id}, 'en')">Preview English</button>
                <button class="btn btn-secondary" onclick="manageDialogMods(${dialog.id})">إدارة المودات</button>
                <button class="btn btn-danger" onclick="deleteDialog(${dialog.id})">حذف</button>
            </div>
        </div>
    `).join('');

    container.innerHTML = dialogsHTML;
}

// تعديل مربع
async function editDialog(id) {
    try {
        const { data, error } = await supabaseClient
            .from('custom_mod_dialogs')
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            throw error;
        }

        // ملء النموذج بالبيانات
        document.getElementById('dialogId').value = data.id;
        document.getElementById('dialogTitle').value = data.title || '';
        document.getElementById('dialogTitleEn').value = data.title_en || '';
        document.getElementById('dialogDescription').value = data.description || '';
        document.getElementById('dialogDescriptionEn').value = data.description_en || '';
        document.getElementById('dialogImage').value = data.image_url || '';
        document.getElementById('buttonText').value = data.button_text || 'تم';
        document.getElementById('buttonTextEn').value = data.button_text_en || 'OK';
        document.getElementById('showDontShowAgain').checked = data.show_dont_show_again;
        document.getElementById('isActive').checked = data.is_active;

        currentEditingId = id;
        document.getElementById('formTitle').textContent = 'تعديل المربع';

        // التمرير إلى النموذج
        document.querySelector('.dialog-form').scrollIntoView({ behavior: 'smooth' });

    } catch (error) {
        console.error('خطأ في تحميل بيانات المربع:', error);
        showMessage('خطأ في تحميل بيانات المربع: ' + error.message, 'error');
    }
}

// حذف مربع
async function deleteDialog(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المربع؟ سيتم حذف جميع ربطه بالمودات أيضاً.')) {
        return;
    }

    try {
        const { error } = await supabaseClient
            .from('custom_mod_dialogs')
            .delete()
            .eq('id', id);

        if (error) {
            throw error;
        }

        showMessage('تم حذف المربع بنجاح', 'success');
        await loadDialogs();

    } catch (error) {
        console.error('خطأ في حذف المربع:', error);
        showMessage('خطأ في حذف المربع: ' + error.message, 'error');
    }
}

// معاينة مربع بواسطة ID
async function previewDialogById(id, language = 'ar') {
    try {
        const { data, error } = await supabaseClient
            .from('custom_mod_dialogs')
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            throw error;
        }

        showPreview(data, language);

    } catch (error) {
        console.error('خطأ في تحميل بيانات المعاينة:', error);
        showMessage('خطأ في تحميل بيانات المعاينة: ' + error.message, 'error');
    }
}

// معاينة المربع من النموذج
function previewDialog() {
    const formData = getFormData();

    if (!formData.title.trim()) {
        showMessage('يرجى إدخال عنوان المربع أولاً', 'error');
        return;
    }

    showPreview(formData);
}

// عرض المعاينة
function showPreview(data, language = 'ar') {
    const modal = document.getElementById('previewModal');
    const image = document.getElementById('previewImage');
    const title = document.getElementById('previewTitle');
    const description = document.getElementById('previewDescription');
    const buttonText = document.getElementById('previewButtonText');

    // عرض الصورة إذا كانت موجودة
    if (data.image_url || data.imageUrl) {
        image.src = data.image_url || data.imageUrl;
        image.style.display = 'block';
    } else {
        image.style.display = 'none';
    }

    // عرض المحتوى حسب اللغة المحددة
    if (language === 'ar') {
        title.textContent = data.title || data.title_en || 'عنوان غير محدد';
        description.textContent = data.description || data.description_en || '';
        buttonText.textContent = data.button_text || data.buttonText || 'تم';
        modal.setAttribute('dir', 'rtl');
    } else {
        title.textContent = data.title_en || data.title || 'No title specified';
        description.textContent = data.description_en || data.description || '';
        buttonText.textContent = data.button_text_en || data.buttonTextEn || 'OK';
        modal.setAttribute('dir', 'ltr');
    }

    modal.style.display = 'flex';
}

// إغلاق المعاينة
function closePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

// إعادة تعيين النموذج
function resetForm() {
    document.getElementById('dialogForm').reset();
    document.getElementById('dialogId').value = '';
    document.getElementById('buttonText').value = 'تم';
    document.getElementById('buttonTextEn').value = 'OK';
    document.getElementById('showDontShowAgain').checked = true;
    document.getElementById('isActive').checked = true;
    currentEditingId = null;
    document.getElementById('formTitle').textContent = 'إنشاء مربع جديد';
}

// إدارة المودات المرتبطة بالمربع
function manageDialogMods(dialogId) {
    // سيتم إنشاء صفحة منفصلة لإدارة المودات
    window.open(`dialog_mods.html?dialog_id=${dialogId}`, '_blank');
}

// عرض الرسائل
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;

    container.innerHTML = '';
    container.appendChild(messageDiv);

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
}

// تأمين النص من HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// إغلاق المعاينة عند النقر خارجها
document.addEventListener('click', function(e) {
    const modal = document.getElementById('previewModal');
    if (e.target === modal) {
        closePreview();
    }
});
