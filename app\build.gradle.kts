plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

android {
    signingConfigs {
        create("customDebugConfig") {
            storeFile = file("../modetaris.jks") // Corrected path to project root
            storePassword = "GGSIDIVALL35"    // New store password
            keyAlias = "key0"                 // New alias
            keyPassword = "GGSIDIVALL35"        // New key password (same as store)
        }
        // You might want to create a separate 'release' signing config for actual releases
        // create("release") {
        //     storeFile = file("modetaris.jks")
        //     storePassword = System.getenv("RELEASE_STORE_PASSWORD") // Or your actual release store password
        //     keyAlias = "key0" // Or your release alias
        //     keyPassword = System.getenv("RELEASE_KEY_PASSWORD") // Or your actual release key password
        // }
    }
    namespace = "com.sidimohamed.modetaris"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.sidimohamed.modetaris"
        minSdk = 24
        targetSdk = 35
        versionCode = 4
        versionName = "1.3"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            isShrinkResources = true // Add this line to shrink resources
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // Use the explicitly defined 'debug' signing configuration for release builds (for local testing only!)
            // For actual releases, you should use a release signingConfig:
            // signingConfig = signingConfigs.getByName("release")
            signingConfig = signingConfigs.getByName("customDebugConfig") // Point to the new custom config
        }
        getByName("debug") {
            signingConfig = signingConfigs.getByName("customDebugConfig") // Point to the new custom config
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        compose = true
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.playServicesAds) // Use camelCase alias for AdMob
    implementation(libs.androidx.ui)
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0") // Added SwipeRefreshLayout dependency
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.google.material) // Added Google Material dependency for Snackbar
    implementation("androidx.localbroadcastmanager:localbroadcastmanager:1.1.0") // Add LocalBroadcastManager dependency directly
    implementation(libs.androidx.core.splashscreen) // Add splash screen dependency

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}
