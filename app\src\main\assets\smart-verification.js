// ========================================
// نظام التحقق الذكي المحسن
// Enhanced Smart Verification System
// يدعم: يوتيوب، تيليجرام، ديسكورد
// ========================================

class SmartVerificationSystem {
    constructor(supabaseClient) {
        this.supabaseClient = supabaseClient;
        this.verificationInProgress = new Set();
        this.retryDelays = [2000, 5000, 10000]; // تأخيرات إعادة المحاولة بالميلي ثانية
    }

    // ========================================
    // التحقق الذكي الموحد
    // ========================================
    async verifyTask(userId, taskId, showProgress = true) {
        // منع التحقق المتعدد للمهمة نفسها
        const verificationKey = `${userId}-${taskId}`;
        if (this.verificationInProgress.has(verificationKey)) {
            return {
                success: false,
                error: 'التحقق جاري بالفعل لهذه المهمة'
            };
        }

        this.verificationInProgress.add(verificationKey);

        try {
            if (showProgress) {
                this.showVerificationProgress('بدء التحقق...', 'info');
            }

            // استدعاء دالة التحقق الذكي في قاعدة البيانات
            const { data, error } = await this.supabaseClient.rpc('smart_verify_task', {
                p_user_id: userId,
                p_task_id: taskId
            });

            if (error) {
                console.error('خطأ في التحقق الذكي:', error);
                if (showProgress) {
                    this.showVerificationProgress('فشل التحقق: ' + error.message, 'error');
                }
                return {
                    success: false,
                    error: error.message
                };
            }

            // معالجة نتيجة التحقق
            const result = data || {};
            
            if (result.success) {
                if (showProgress) {
                    this.showVerificationProgress(
                        `✅ تم التحقق بنجاح! النقاط: ${result.verification_score || 0}`,
                        'success'
                    );
                }
                
                // تحديث واجهة المستخدم
                this.updateTaskUI(taskId, 'verified', result.verification_score);
                
                return {
                    success: true,
                    platform: result.platform,
                    verification_score: result.verification_score,
                    verified_at: result.verified_at
                };
            } else {
                if (showProgress) {
                    this.showVerificationProgress(
                        `❌ فشل التحقق: ${result.error || 'خطأ غير معروف'}`,
                        'error'
                    );
                }
                
                this.updateTaskUI(taskId, 'failed', 0);
                
                return {
                    success: false,
                    error: result.error || 'فشل التحقق',
                    max_attempts: result.max_attempts
                };
            }

        } catch (error) {
            console.error('خطأ في نظام التحقق:', error);
            if (showProgress) {
                this.showVerificationProgress('حدث خطأ أثناء التحقق', 'error');
            }
            return {
                success: false,
                error: 'حدث خطأ أثناء التحقق'
            };
        } finally {
            this.verificationInProgress.delete(verificationKey);
        }
    }

    // ========================================
    // التحقق من إكمال جميع المهام
    // ========================================
    async checkAllTasksCompletion(userId, campaignId) {
        try {
            this.showVerificationProgress('التحقق من إكمال جميع المهام...', 'info');

            // الحصول على جميع المهام المطلوبة
            const { data: requiredTasks, error: tasksError } = await this.supabaseClient
                .from('campaign_tasks')
                .select('id, title_ar, task_type')
                .eq('campaign_id', campaignId)
                .eq('is_required', true)
                .order('display_order');

            if (tasksError) {
                throw tasksError;
            }

            // الحصول على تقدم المستخدم
            const { data: userProgress, error: progressError } = await this.supabaseClient
                .from('user_task_progress')
                .select('task_id, status, verification_score')
                .eq('user_id', userId)
                .in('task_id', requiredTasks.map(t => t.id));

            if (progressError) {
                throw progressError;
            }

            // تحليل النتائج
            const completedTasks = userProgress.filter(p => p.status === 'verified');
            const totalRequired = requiredTasks.length;
            const totalCompleted = completedTasks.length;

            if (totalCompleted < totalRequired) {
                const remaining = totalRequired - totalCompleted;
                this.showVerificationProgress(
                    `يجب إكمال ${remaining} مهمة أخرى للحصول على الاشتراك المجاني`,
                    'warning'
                );
                
                return {
                    success: false,
                    completed_tasks: totalCompleted,
                    required_tasks: totalRequired,
                    remaining_tasks: remaining
                };
            }

            // جميع المهام مكتملة - تفعيل الاشتراك
            return await this.activateSubscription(userId, campaignId);

        } catch (error) {
            console.error('خطأ في التحقق من إكمال المهام:', error);
            this.showVerificationProgress('حدث خطأ أثناء التحقق من المهام', 'error');
            return {
                success: false,
                error: error.message
            };
        }
    }

    // ========================================
    // تفعيل الاشتراك المجاني
    // ========================================
    async activateSubscription(userId, campaignId) {
        try {
            this.showVerificationProgress('تفعيل الاشتراك المجاني...', 'info');

            const { data, error } = await this.supabaseClient.rpc('activate_free_subscription', {
                p_user_id: userId,
                p_campaign_id: campaignId
            });

            if (error) {
                throw error;
            }

            const result = data || {};

            if (result.success) {
                // حساب تاريخ انتهاء الاشتراك
                const expiresAt = new Date(result.expires_at);
                const durationDays = result.duration_days;

                this.showVerificationProgress(
                    `🎉 تهانينا! تم تفعيل اشتراكك المجاني لمدة ${durationDays} يوم`,
                    'success'
                );

                // إغلاق جميع النوافذ المنبثقة
                this.closeAllModals();

                // حفظ معلومات الاشتراك محلياً
                this.saveSubscriptionInfo({
                    subscription_id: result.subscription_id,
                    expires_at: result.expires_at,
                    verification_score: result.verification_score,
                    duration_days: durationDays
                });

                return {
                    success: true,
                    subscription_id: result.subscription_id,
                    expires_at: result.expires_at,
                    verification_score: result.verification_score,
                    duration_days: durationDays
                };
            } else {
                this.showVerificationProgress(
                    `فشل تفعيل الاشتراك: ${result.error}`,
                    'error'
                );
                return {
                    success: false,
                    error: result.error
                };
            }

        } catch (error) {
            console.error('خطأ في تفعيل الاشتراك:', error);
            this.showVerificationProgress('حدث خطأ أثناء تفعيل الاشتراك', 'error');
            return {
                success: false,
                error: error.message
            };
        }
    }

    // ========================================
    // معالجة النقر على مهمة
    // ========================================
    async handleTaskClick(taskId, taskData) {
        try {
            // فتح الرابط المطلوب
            if (taskData.target_url) {
                if (typeof Android !== 'undefined' && Android.openUrl) {
                    Android.openUrl(taskData.target_url);
                } else {
                    window.open(taskData.target_url, '_blank');
                }
            }

            // انتظار قصير للسماح للمستخدم بإكمال المهمة
            const delay = taskData.verification_delay_seconds || 30;
            
            this.showVerificationProgress(
                `يرجى إكمال المهمة ثم الانتظار ${delay} ثانية للتحقق التلقائي...`,
                'info'
            );

            // انتظار التأخير المحدد
            await this.sleep(delay * 1000);

            // بدء التحقق التلقائي
            const userId = this.generateUserId();
            return await this.verifyTask(userId, taskId);

        } catch (error) {
            console.error('خطأ في معالجة النقر على المهمة:', error);
            this.showVerificationProgress('حدث خطأ أثناء معالجة المهمة', 'error');
            return {
                success: false,
                error: error.message
            };
        }
    }

    // ========================================
    // دوال مساعدة
    // ========================================
    
    // تحديث واجهة المستخدم للمهمة
    updateTaskUI(taskId, status, score = 0) {
        const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
        if (!taskElement) return;

        const statusElement = taskElement.querySelector('.task-status');
        const buttonElement = taskElement.querySelector('.complete-task-btn');

        if (status === 'verified') {
            if (statusElement) {
                statusElement.textContent = `✅ مكتملة (${score}%)`;
                statusElement.style.background = 'rgba(34, 197, 94, 0.2)';
                statusElement.style.color = '#22c55e';
            }
            if (buttonElement) {
                buttonElement.textContent = '✅ تم التحقق';
                buttonElement.disabled = true;
                buttonElement.style.background = '#22c55e';
            }
        } else if (status === 'failed') {
            if (statusElement) {
                statusElement.textContent = '❌ فشل';
                statusElement.style.background = 'rgba(239, 68, 68, 0.2)';
                statusElement.style.color = '#ef4444';
            }
            if (buttonElement) {
                buttonElement.textContent = '🔄 إعادة المحاولة';
                buttonElement.disabled = false;
            }
        }
    }

    // عرض تقدم التحقق
    showVerificationProgress(message, type = 'info') {
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    // إغلاق جميع النوافذ المنبثقة
    closeAllModals() {
        document.querySelectorAll('.tasks-modal, .subscription-campaign-modal').forEach(modal => {
            modal.remove();
        });
    }

    // حفظ معلومات الاشتراك
    saveSubscriptionInfo(subscriptionData) {
        try {
            localStorage.setItem('activeSubscription', JSON.stringify(subscriptionData));
        } catch (error) {
            console.error('خطأ في حفظ معلومات الاشتراك:', error);
        }
    }

    // توليد معرف المستخدم
    generateUserId() {
        if (typeof generateUserId === 'function') {
            return generateUserId();
        }
        // fallback
        return localStorage.getItem('user_id') || 'user_' + Date.now();
    }

    // دالة النوم
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تصدير النظام للاستخدام العام
window.SmartVerificationSystem = SmartVerificationSystem;
