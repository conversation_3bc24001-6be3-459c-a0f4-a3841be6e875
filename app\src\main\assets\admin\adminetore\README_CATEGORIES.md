# إضافة ميزة الأقسام لتطبيق Modetaris

هذا الملف يشرح كيفية إضافة ميزة الأقسام الجديدة (مثل Shaders وTexture Pack) لتطبيق Modetaris، مع القدرة على تعيين مودات محددة لكل قسم وإضافة أيقونات للأقسام.

## الخطوات المطلوبة

### 1. إنشاء الجداول في Supabase

قم بتنفيذ ملف SQL المرفق `create_tables.sql` في لوحة تحكم Supabase (SQL Editor) لإنشاء الجداول المطلوبة:
- `categories`: جدول لتخزين الأقسام مع الأيقونات
- `category_mods`: جدول للعلاقة بين الأقسام والمودات

### 2. تحديث ملفات التطبيق

تم تحديث الملفات التالية:
- `admin_script.js`: إضافة وظائف إدارة الأقسام
- `admin_style.css`: إضافة أنماط CSS للنافذة المنبثقة وعناصر إدارة الأقسام

### 3. استخدام الميزة الجديدة

#### إدارة الأقسام
1. انتقل إلى قسم "إدارة الأقسام" في لوحة التحكم
2. أضف قسمًا جديدًا بإدخال اسم القسم ورمز الأيقونة (Font Awesome)
3. يمكنك تعديل أو حذف الأقسام الموجودة

#### ربط المودات بالأقسام
1. انقر على زر "إدارة المودات" بجانب القسم المطلوب
2. ستظهر نافذة منبثقة تعرض المودات المتاحة والمودات الموجودة في القسم
3. استخدم زر "إضافة" لإضافة مود إلى القسم أو "إزالة" لإزالته من القسم
4. يمكنك استخدام حقل البحث للعثور على مودات محددة

#### إضافة مود جديد مع تحديد القسم
عند إضافة مود جديد أو تعديل مود موجود، يمكنك اختيار القسم من القائمة المنسدلة التي تعرض جميع الأقسام المتاحة.

## ملاحظات فنية

### هيكل الجداول

#### جدول categories
- `id`: معرف فريد (UUID)
- `name`: اسم القسم
- `icon_class`: رمز الأيقونة (Font Awesome)
- `created_at`: تاريخ الإنشاء
- `updated_at`: تاريخ التحديث

#### جدول category_mods
- `id`: معرف فريد (UUID)
- `category_id`: معرف القسم (مفتاح خارجي)
- `mod_id`: معرف المود (مفتاح خارجي)
- `created_at`: تاريخ الإنشاء

### الوظائف الرئيسية

- `fetchCategories()`: جلب قائمة الأقسام من قاعدة البيانات
- `handleCategoryFormSubmit()`: إضافة أو تحديث قسم
- `handleManageCategoryModsClick()`: فتح نافذة إدارة مودات القسم
- `loadModsForCategory()`: تحميل المودات المتاحة والمودات الموجودة في القسم
- `handleAddModToCategory()`: إضافة مود إلى قسم
- `handleRemoveModFromCategory()`: إزالة مود من قسم
- `loadCategoriesForDropdown()`: تحميل الأقسام في القوائم المنسدلة
- `updateCategoryFilters()`: تحديث فلاتر الأقسام في جميع أنحاء التطبيق

## الأيقونات المدعومة

يمكنك استخدام أي أيقونة من مكتبة Font Awesome 6. بعض الأمثلة:
- `fas fa-folder`: مجلد
- `fas fa-image`: صورة
- `fas fa-palette`: لوحة ألوان
- `fas fa-puzzle-piece`: قطعة أحجية
- `fas fa-gamepad`: وحدة تحكم
- `fas fa-cubes`: مكعبات
- `fas fa-mountain`: جبل
- `fas fa-water`: ماء
- `fas fa-fire`: نار
- `fas fa-magic`: سحر
